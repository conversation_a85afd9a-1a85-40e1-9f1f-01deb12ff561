import React, { useEffect, useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import io from 'socket.io-client';
import serverConfigService from './services/serverConfig';
import enhancedToastService from './services/enhanced-toast-service';

// Layouts
import MainLayout from './components/layouts/MainLayout.jsx';

// Pages
import Dashboard from './pages/Dashboard.jsx';
import DeviceList from './pages/DeviceList.jsx';
import DeviceDetail from './pages/DeviceDetail.jsx';
import DeviceDetailTest from './pages/DeviceDetailTest.jsx';

import NotificationsPage from './pages/NotificationsPage.jsx'; // ✅ Basit bildirimler sayfası
// Ayarlar sayfaları
import SettingsIndex from './pages/settings/SettingsIndex.jsx';
import GeneralSettings from './pages/settings/GeneralSettings.jsx';
import MonitoringSettings from './pages/settings/MonitoringSettings.jsx';
import NotificationSettings from './pages/settings/NotificationSettings.jsx';
import SecuritySettings from './pages/settings/SecuritySettings.jsx';
import SystemSettings from './pages/settings/SystemSettings.jsx';
import UserSettings from './pages/settings/UserSettings.jsx';
import NotFound from './pages/NotFound.jsx';
import Login from './pages/Login.jsx';
import Register from './pages/Register.jsx';
import ForgotPassword from './pages/ForgotPassword.jsx';
import ResetPassword from './pages/ResetPassword.jsx';
import Profile from './pages/Profile.jsx';
import UserManagement from './pages/UserManagement.jsx';

// Grafikler sayfaları
import GraphsIndex from './pages/graphs/GraphsIndex.jsx';
import StatusDistribution from './pages/graphs/StatusDistribution.jsx';
import StatusHistory from './pages/graphs/StatusHistory.jsx';
import ResponseTime from './pages/graphs/ResponseTime.jsx';
import GroupAnalysis from './pages/graphs/GroupAnalysis.jsx';

// Context
import { SocketProvider } from './contexts/SocketContext';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';

import { DeviceProvider } from './contexts/DeviceContext';
import { SystemProvider } from './contexts/SystemContext';
// NotificationProvider kaldırıldı - Basit sistem kullanılıyor
import { NotificationMenuProvider } from './contexts/NotificationMenuContext';
import { ToastSettingsProvider } from './contexts/ToastSettingsContext';
import { Toaster } from './components/ui/sonner';

// Providers
import { QueryProvider } from './providers/QueryProvider.jsx';

// Components
import ProtectedRoute from './components/ProtectedRoute';
import Loading from './components/ui/loading';
import ErrorBoundary from './components/ErrorBoundary';

// Toast component'i - Sabit sağ alt konum
const ToasterComponent = () => {
  return <Toaster position="bottom-right" />;
};

function App() {
  // Başlangıçta varsayılan değerlerle socket oluştur
  const [socket, setSocket] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Uygulama başladığında sunucu bilgilerini yükle
  useEffect(() => {
    const initializeApp = async () => {
      setIsLoading(true);

      try {
        // Sunucu bilgilerini yükle
        await serverConfigService.loadServerConfig();

        // Socket.io URL'sini al
        const SOCKET_URL = serverConfigService.getSocketUrl();
        console.log('Socket.io bağlantı URL:', SOCKET_URL);

        // Socket.io nesnesini oluştur - basitleştirilmiş yapılandırma
        const socketInstance = io(SOCKET_URL, {
          withCredentials: false,
          transports: ['websocket', 'polling'],
          reconnectionAttempts: Infinity,
          autoConnect: true,
          forceNew: true
        });

        // Global zorla çıkış olayını dinle
        socketInstance.on('user:force-logout', (data) => {
          console.log('App.js: Zorla çıkış olayı alındı:', data);

          // Token'ları ve kullanıcı bilgilerini temizle
          localStorage.removeItem('auth_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('user');

          // Toast bildirimi göster
          enhancedToastService.error(
            'Hesap Devre Dışı Bırakıldı',
            data.reason || 'Hesabınız yönetici tarafından devre dışı bırakıldı. Lütfen sistem yöneticisi ile iletişime geçin.',
            { duration: 8000, playSound: true }
          );

          // 2 saniye sonra login sayfasına yönlendir (kullanıcının mesajı okuması için)
          setTimeout(() => {
            window.location.href = '/login';
          }, 2000);
        });

        // Socket.io nesnesini state'e kaydet
        setSocket(socketInstance);

        // Socket'i global olarak erişilebilir yap
        window.socket = socketInstance;
      } catch (error) {
        console.error('Uygulama başlatılırken hata:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);
  useEffect(() => {
    // Socket henüz oluşturulmadıysa, dinleme işlemlerini atla
    if (!socket) return;

    // Socket.io bağlantısını dinle
    socket.on('connect', () => {
      console.log('Socket.io connected');
    });

    socket.on('disconnect', () => {
      console.log('Socket.io disconnected');
    });

    socket.on('notification:new', (data) => {
      console.log('App.js - Yeni bildirim alındı:', data);
    });

    // Temizlik fonksiyonu
    return () => {
      socket.off('connect');
      socket.off('disconnect');
      socket.off('notification:new');
    };
  }, [socket]); // socket değiştiğinde useEffect'i yeniden çalıştır

  // Uygulama yükleniyorsa yükleniyor ekranı göster
  if (isLoading || !socket) {
    return (
      <Loading
        type="fullscreen"
        size="lg"
        message="Uygulama Yükleniyor..."
        description="Sunucu bilgileri alınıyor, lütfen bekleyin."
      />
    );
  }

  return (
    <ErrorBoundary>
      <QueryProvider>
        <ThemeProvider>
          <AuthProvider>
            <div className="min-h-screen bg-background font-sans antialiased">
                {/* Toast component'i tüm uygulama için - en üstte */}
                <ToasterComponent />

            <Routes>
              {/* Kimlik doğrulama gerektirmeyen rotalar */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password/:token" element={<ResetPassword />} />

              {/* Kimlik doğrulama gerektiren rotalar */}
              <Route path="/" element={
                <ProtectedRoute>
                  <SocketProvider value={socket}>
                    <DeviceProvider>
                      <SystemProvider>
                        <ToastSettingsProvider>
                          <NotificationMenuProvider>
                            <MainLayout />
                          </NotificationMenuProvider>
                        </ToastSettingsProvider>
                      </SystemProvider>
                    </DeviceProvider>
                  </SocketProvider>
                </ProtectedRoute>
              }>
              <Route index element={<Dashboard />} />
              <Route path="devices" element={<DeviceList />} />
              <Route path="devices/:id" element={<DeviceDetail />} />
              <Route path="test/device-detail" element={<DeviceDetailTest />} />
              <Route path="graphs" element={<GraphsIndex />} />
              <Route path="graphs/status" element={<StatusDistribution />} />
              <Route path="graphs/history" element={<StatusHistory />} />
              <Route path="graphs/response" element={<ResponseTime />} />
              <Route path="graphs/groups" element={<GroupAnalysis />} />
              {/* Alerts sayfası kaldırıldı */}
              <Route path="notifications" element={<NotificationsPage />} />
              <Route path="settings" element={<SettingsIndex />} />
              <Route path="settings/general" element={<GeneralSettings />} />
              <Route path="settings/monitoring" element={<MonitoringSettings />} />
              <Route path="settings/notifications" element={<NotificationSettings />} />
              <Route path="settings/security" element={<SecuritySettings />} />
              <Route path="settings/system" element={<SystemSettings />} />
              <Route path="settings/users" element={
                <ProtectedRoute adminOnly={true}>
                  <UserSettings />
                </ProtectedRoute>
              } />
              <Route path="profile" element={<Profile />} />
              <Route path="users" element={
                <ProtectedRoute adminOnly={true}>
                  <UserManagement />
                </ProtectedRoute>
              } />

              <Route path="*" element={<NotFound />} />
            </Route>
          </Routes>
            </div>
          </AuthProvider>
        </ThemeProvider>
      </QueryProvider>
    </ErrorBoundary>
  );
}

export default App;
