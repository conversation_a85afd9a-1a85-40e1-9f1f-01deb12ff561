import { useState, useEffect, useCallback } from 'react';
import { useDevices } from '../contexts/DeviceContext';

/**
 * Custom hook for managing monitor status data
 * Handles real-time status updates and monitor management
 */
export const useMonitorStatus = (deviceId) => {
  const { devices, statuses } = useDevices();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Get device data
  const device = devices?.find(d => d.id === deviceId) || null;

  // Get current status for the device - add proper null checks
  const deviceStatus = (statuses && deviceId && statuses[deviceId]) ? statuses[deviceId] : {};

  // Monitor status helpers
  const getMonitorStatus = useCallback((monitorType) => {
    return deviceStatus[monitorType] || null;
  }, [deviceStatus]);

  // Check if monitor type is enabled
  const isMonitorEnabled = useCallback((monitorType) => {
    return device?.monitors?.[monitorType]?.enabled || false;
  }, [device]);

  // Get monitor configuration
  const getMonitorConfig = useCallback((monitorType) => {
    return device?.monitors?.[monitorType] || {};
  }, [device]);

  // Get formatted monitor data for display
  const getFormattedMonitorData = useCallback((monitorType) => {
    const status = getMonitorStatus(monitorType);
    const config = getMonitorConfig(monitorType);
    const enabled = isMonitorEnabled(monitorType);

    if (!enabled || !status) {
      return null;
    }

    // Handle different monitor types
    switch (monitorType) {
      case 'icmp':
        return {
          ...status,
          type: 'icmp',
          enabled,
          config,
          displayName: 'ICMP/Ping'
        };

      case 'http':
        return {
          ...status,
          type: 'http',
          enabled,
          config,
          displayName: 'HTTP/HTTPS'
        };

      case 'tcp':
        // TCP can have multiple ports, handle as object
        if (typeof status === 'object' && Object.keys(status).length > 0) {
          const portData = Object.values(status)[0];
          const port = Object.keys(status)[0];
          return {
            ...portData,
            port,
            type: 'tcp',
            enabled,
            config,
            displayName: 'TCP Port'
          };
        }
        return null;

      case 'dns':
        return {
          ...status,
          type: 'dns',
          enabled,
          config,
          displayName: 'DNS'
        };

      case 'ssl':
        return {
          ...status,
          type: 'ssl',
          enabled,
          config,
          displayName: 'SSL Certificate'
        };

      default:
        return {
          ...status,
          type: monitorType,
          enabled,
          config,
          displayName: monitorType.toUpperCase()
        };
    }
  }, [getMonitorStatus, getMonitorConfig, isMonitorEnabled]);

  // Get all enabled monitors with their data
  const getEnabledMonitors = useCallback(() => {
    if (!device) return [];

    const monitorTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl'];
    const enabledMonitors = [];

    monitorTypes.forEach(type => {
      if (isMonitorEnabled(type)) {
        const monitorData = getFormattedMonitorData(type);
        if (monitorData) {
          enabledMonitors.push(monitorData);
        }
      }
    });

    return enabledMonitors;
  }, [device, isMonitorEnabled, getFormattedMonitorData]);

  // Get monitor status summary
  const getStatusSummary = useCallback(() => {
    const enabledMonitors = getEnabledMonitors();
    
    const summary = {
      total: enabledMonitors.length,
      up: 0,
      warning: 0,
      critical: 0,
      down: 0,
      unknown: 0
    };

    enabledMonitors.forEach(monitor => {
      const status = monitor.status || 'unknown';
      if (summary.hasOwnProperty(status)) {
        summary[status]++;
      } else {
        summary.unknown++;
      }
    });

    return summary;
  }, [getEnabledMonitors]);

  // Get overall device status
  const getOverallStatus = useCallback(() => {
    if (!device) return 'unknown';
    
    // Use device status from context if available
    const deviceStatusData = deviceStatus.device;
    if (deviceStatusData?.status) {
      return deviceStatusData.status;
    }

    // Fallback to calculating from monitor statuses
    const summary = getStatusSummary();
    
    if (summary.down > 0) return 'down';
    if (summary.critical > 0) return 'critical';
    if (summary.warning > 0) return 'warning';
    if (summary.up > 0) return 'up';
    
    return 'unknown';
  }, [device, deviceStatus, getStatusSummary]);

  // Monitor type validation
  const validateMonitorType = useCallback((monitorType) => {
    const validTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl'];
    return validTypes.includes(monitorType);
  }, []);

  // Error handling and loading state management
  useEffect(() => {
    if (!deviceId) {
      setError('Device ID is required');
      setLoading(false);
      return;
    }

    // If devices array is empty, we're still loading
    if (!devices || devices.length === 0) {
      setLoading(true);
      setError(null);
      return;
    }

    // If devices are loaded but device not found
    if (!device) {
      setError('Device not found');
      setLoading(false);
      return;
    }

    // If statuses is not yet available
    if (!statuses) {
      setLoading(true);
      setError(null);
      return;
    }

    // All good
    setError(null);
    setLoading(false);
  }, [deviceId, device, devices, statuses]);

  return {
    device,
    deviceStatus,
    loading,
    error,
    getMonitorStatus,
    isMonitorEnabled,
    getMonitorConfig,
    getFormattedMonitorData,
    getEnabledMonitors,
    getStatusSummary,
    getOverallStatus,
    validateMonitorType
  };
};
