import { useState, useEffect, useCallback } from 'react';
import { monitorService } from '../services/api';

/**
 * Custom hook for managing device history data
 * Handles loading, pagination, and aggregation of monitor history
 */
export const useDeviceHistory = (deviceId, device) => {
  const [historyData, setHistoryData] = useState({
    icmp: [],
    dns: [],
    ssl: [],
    tcp: [],
    http: []
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Load history data for all enabled monitor types
  const loadHistoryData = useCallback(async () => {
    if (!deviceId || !device) return;

    setLoading(true);
    setError(null);

    try {
      const promises = [];
      const types = [];

      // Only load history for enabled monitor types
      if (device.monitors?.icmp?.enabled) {
        promises.push(monitorService.getIcmpHistory(deviceId));
        types.push('icmp');
      }
      if (device.monitors?.dns?.enabled) {
        promises.push(monitorService.getDnsHistory(deviceId));
        types.push('dns');
      }
      if (device.monitors?.ssl?.enabled) {
        promises.push(monitorService.getSslHistory(deviceId));
        types.push('ssl');
      }
      if (device.monitors?.tcp?.enabled) {
        promises.push(monitorService.getTcpHistory(deviceId));
        types.push('tcp');
      }
      if (device.monitors?.http?.enabled) {
        promises.push(monitorService.getHttpHistory(deviceId));
        types.push('http');
      }

      // Execute all promises in parallel for better performance
      const results = await Promise.allSettled(promises);
      
      const newHistoryData = { ...historyData };
      
      results.forEach((result, index) => {
        const type = types[index];
        if (result.status === 'fulfilled' && result.value?.success) {
          newHistoryData[type] = result.value.data || [];
        } else {
          console.error(`Failed to load ${type} history:`, result.reason);
          newHistoryData[type] = [];
        }
      });

      setHistoryData(newHistoryData);
    } catch (err) {
      console.error('Error loading history data:', err);
      setError('Geçmiş verileri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [deviceId, device]);

  // Aggregate all history data for display
  const aggregatedHistory = useCallback(() => {
    const allHistory = [];

    // Helper function to get monitor icon
    const getMonitorTypeLabel = (type) => {
      const labels = {
        icmp: 'ICMP',
        dns: 'DNS',
        ssl: 'SSL',
        tcp: 'TCP',
        http: 'HTTP'
      };
      return labels[type] || type.toUpperCase();
    };

    const getStatusLabel = (type, status) => {
      const labels = {
        icmp: { up: 'Çevrimiçi', down: 'Çevrimdışı' },
        dns: { up: 'Başarılı', down: 'Başarısız' },
        ssl: { up: 'Geçerli', down: 'Geçersiz' },
        tcp: { up: 'Açık', down: 'Kapalı' },
        http: { up: 'Başarılı', down: 'Başarısız' }
      };
      return labels[type]?.[status] || status;
    };

    // Process each monitor type
    Object.entries(historyData).forEach(([type, records]) => {
      if (Array.isArray(records) && records.length > 0) {
        records.forEach(record => {
          if (record && typeof record === 'object') {
            allHistory.push({
              ...record,
              type,
              typeLabel: getMonitorTypeLabel(type),
              statusLabel: getStatusLabel(type, record.status)
            });
          }
        });
      }
    });

    // Sort by timestamp (newest first)
    allHistory.sort((a, b) => parseInt(b.timestamp) - parseInt(a.timestamp));

    return allHistory;
  }, [historyData]);

  // Paginated history data
  const paginatedHistory = useCallback(() => {
    const allHistory = aggregatedHistory();
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = allHistory.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(allHistory.length / itemsPerPage);

    return {
      items: currentItems,
      totalPages,
      totalItems: allHistory.length,
      currentPage,
      itemsPerPage
    };
  }, [aggregatedHistory, currentPage, itemsPerPage]);

  // Page change handler
  const handlePageChange = useCallback((pageNumber) => {
    setCurrentPage(pageNumber);
  }, []);

  // Page size change handler
  const handlePageSizeChange = useCallback((newPageSize) => {
    const allHistory = aggregatedHistory();
    const newTotalPages = Math.ceil(allHistory.length / newPageSize);
    const newCurrentPage = Math.min(currentPage, newTotalPages);

    setItemsPerPage(newPageSize);
    setCurrentPage(newCurrentPage);
  }, [aggregatedHistory, currentPage]);

  // Load data when dependencies change
  useEffect(() => {
    loadHistoryData();
  }, [loadHistoryData]);

  return {
    historyData,
    loading,
    error,
    paginatedHistory: paginatedHistory(),
    handlePageChange,
    handlePageSizeChange,
    refreshHistory: loadHistoryData
  };
};
