import { useEffect } from 'react';

/**
 * Tarayıcı sekmesinin başlığını değiştirmek için kullanılan hook
 * @param {string} title - Ayarlanacak başlık
 * @param {boolean} [resetOnUnmount=false] - Bileşen kaldırıldığında başlığı sıfırla
 */
const useDocumentTitle = (title, resetOnUnmount = false) => {
  useEffect(() => {
    // Önceki başlığı sakla
    const defaultTitle = document.title;
    
    // Yeni başlığı ayarla
    if (title) {
      document.title = title;
    }
    
    // Temizlik fonksiyonu
    return () => {
      // Eğer resetOnUnmount true ise, başlığı sıfırla
      if (resetOnUnmount) {
        document.title = defaultTitle;
      }
    };
  }, [title, resetOnUnmount]);
};

export default useDocumentTitle;
