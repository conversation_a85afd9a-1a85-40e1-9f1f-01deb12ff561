import { useState, useEffect, useRef } from 'react';

/**
 * Akıllı Loading Hook
 * İlk yükleme vs veri yenileme durumlarını otomatik olarak ayırt eder
 */
export const useSmartLoading = (initialData = null) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasData, setHasData] = useState(false);
  const [error, setError] = useState(null);

  // İlk yükleme tamamlandı mı?
  const hasInitiallyLoaded = useRef(false);

  // Data değişikliklerini izle
  useEffect(() => {
    if (initialData !== null && initialData !== undefined) {
      setHasData(Array.isArray(initialData) ? initialData.length > 0 : true);

      if (!hasInitiallyLoaded.current) {
        setIsInitialLoading(false);
        hasInitiallyLoaded.current = true;
      }
    }
  }, [initialData]);

  // Loading durumunu başlat
  const startLoading = (isRefresh = false) => {
    setError(null);

    if (!hasInitiallyLoaded.current) {
      // İlk yükleme
      setIsInitialLoading(true);
      setIsLoading(true);
    } else if (isRefresh) {
      // Veri yenileme
      setIsRefreshing(true);
      setIsLoading(true);
    } else {
      // Normal loading
      setIsLoading(true);
    }
  };

  // Loading durumunu bitir
  const stopLoading = (newData = null, errorMessage = null) => {
    if (errorMessage) {
      setError(errorMessage);
    }

    if (newData !== null) {
      setHasData(Array.isArray(newData) ? newData.length > 0 : true);
    }

    setIsLoading(false);
    setIsInitialLoading(false);
    setIsRefreshing(false);

    if (!hasInitiallyLoaded.current) {
      hasInitiallyLoaded.current = true;
    }
  };

  // Loading türünü belirle
  const getLoadingType = () => {
    if (isInitialLoading && !hasData) return 'initial';
    if (isRefreshing && hasData) return 'refresh';
    if (isLoading) return 'loading';
    return 'idle';
  };

  // Hangi loading component'ini kullanacağını belirle
  const shouldShowFullPageLoading = () => {
    return getLoadingType() === 'initial';
  };

  const shouldShowSkeleton = () => {
    return getLoadingType() === 'refresh' || (getLoadingType() === 'loading' && hasData);
  };

  const shouldShowInlineLoading = () => {
    return getLoadingType() === 'loading' && !hasData && hasInitiallyLoaded.current;
  };

  return {
    // States
    isLoading,
    isInitialLoading,
    isRefreshing,
    hasData,
    error,

    // Actions
    startLoading,
    stopLoading,
    setError,

    // Helpers
    getLoadingType,
    shouldShowFullPageLoading,
    shouldShowSkeleton,
    shouldShowInlineLoading,

    // Computed
    loadingType: getLoadingType()
  };
};

/**
 * Sayfa Loading Hook
 * Sayfa seviyesinde loading yönetimi için
 */
export const usePageLoading = (data, contextLoading = false) => {
  const smartLoading = useSmartLoading(data);

  // Context loading'i de dikkate al
  const isContextLoading = contextLoading && !smartLoading.hasData;

  return {
    ...smartLoading,
    shouldShowFullPageLoading: () => {
      return smartLoading.shouldShowFullPageLoading() || isContextLoading;
    },
    shouldShowSkeleton: () => {
      return smartLoading.shouldShowSkeleton() && !isContextLoading;
    }
  };
};

/**
 * Tablo Loading Hook
 * Tablo seviyesinde loading yönetimi için
 */
export const useTableLoading = (data, isSearching = false, isRefreshing = false) => {
  const smartLoading = useSmartLoading(data);

  return {
    ...smartLoading,
    shouldShowSkeleton: () => {
      return smartLoading.shouldShowSkeleton() || isSearching || isRefreshing;
    },
    shouldShowEmptyState: () => {
      return !smartLoading.isLoading && !isSearching && !isRefreshing &&
             smartLoading.hasData === false && smartLoading.loadingType === 'idle';
    }
  };
};

export default useSmartLoading;
