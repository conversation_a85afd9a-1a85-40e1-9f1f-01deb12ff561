import { useQuery } from '@tanstack/react-query';
import { monitorService, deviceService } from '../services/api';
import { useMemo } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  STATUS_TYPES,
  getChartStatusColors,
  getChartColors
} from '../lib/theme';
import { getFormattedSubCategory } from '../utils/categoryUtils';

// Zaman aralığı değerini dakikaya çeviren yardımcı fonksiyon
const getTimeRangeMinutes = (timeRangeValue) => {
  const TIME_RANGES = [
    { value: '1h', label: 'Son 1 Saat', minutes: 60 },
    { value: '6h', label: 'Son 6 Saat', minutes: 360 },
    { value: '12h', label: 'Son 12 Saat', minutes: 720 },
    { value: '24h', label: 'Son 24 Saat', minutes: 1440 },
    { value: '3d', label: 'Son 3 Gün', minutes: 4320 },
    { value: '7d', label: 'Son 7 Gün', minutes: 10080 },
    { value: '30d', label: 'Son 30 Gün', minutes: 43200 }
  ];

  const range = TIME_RANGES.find(r => r.value === timeRangeValue);
  return range ? range.minutes : 1440; // Varsayılan: 24 saat
};

// Zaman noktaları oluşturmak için yardımcı fonksiyon
const generateTimePoints = (timeRangeMinutes, count = 24) => {
  const now = Date.now();
  const timePoints = [];

  for (let i = count - 1; i >= 0; i--) {
    const timePoint = now - (timeRangeMinutes * 60 * 1000 * i / (count - 1));
    timePoints.push(timePoint);
  }

  return timePoints;
};

// Zaman etiketlerini formatlamak için yardımcı fonksiyon
const formatTimeLabels = (timestamps, timeRangeValue) => {
  return timestamps.map(timestamp => {
    const date = new Date(timestamp);
    if (timeRangeValue === '24h' || timeRangeValue === '1h' || timeRangeValue === '6h' || timeRangeValue === '12h') {
      return format(date, 'HH:mm', { locale: tr });
    } else {
      return format(date, 'dd MMM', { locale: tr });
    }
  });
};

// Durum renkleri ve grafik renkleri artık theme.js'den içe aktarılıyor

// Grafik veri seti oluşturmak için yardımcı fonksiyon
const createDataset = (label, data, colorIndex, options = {}) => {
  const colors = getChartColors();
  const color = colors[colorIndex % colors.length];

  return {
    label,
    data,
    borderColor: color,
    backgroundColor: color.replace('0.7', '0.1'),
    borderWidth: 2,
    pointRadius: 3,
    pointHoverRadius: 5,
    fill: false,
    tension: 0.4,
    spanGaps: true,
    ...options
  };
};

// Durum veri seti oluşturmak için yardımcı fonksiyon
const createStatusDataset = (label, data, status) => {
  const colors = getChartStatusColors();
  const color = colors[status];

  return {
    label,
    data,
    backgroundColor: color.bg,
    borderColor: color.border,
    borderWidth: 1
  };
};

// Cihaz durumlarını getiren hook
export const useDeviceStats = () => {
  return useQuery({
    queryKey: ['deviceStats'],
    queryFn: async () => {
      try {
        const statuses = await monitorService.getAllStatuses();
        console.log('Cihaz durumları:', statuses);
        return statuses;
      } catch (error) {
        console.error('Cihaz durumları çekilirken hata:', error);
        // Hata durumunda boş bir nesne döndür
        return {};
      }
    },
    // Periyodik yenileme kaldırıldı - sadece ilk yükleme ve manuel tetikleme ile çalışacak
    retry: 2, // Hata durumunda 2 kez daha dene
  });
};

// Cihaz listesini getiren hook
export const useDevices = () => {
  return useQuery({
    queryKey: ['devices'],
    queryFn: async () => {
      try {
        const devices = await deviceService.getAll();
        console.log('Cihaz listesi:', devices);
        return devices;
      } catch (error) {
        console.error('Cihaz listesi çekilirken hata:', error);
        // Hata durumunda boş bir dizi döndür
        return [];
      }
    },
    // Periyodik yenileme kaldırıldı - sadece ilk yükleme ve manuel tetikleme ile çalışacak
    retry: 2, // Hata durumunda 2 kez daha dene
  });
};

// Yanıt süresi verilerini getiren hook
export const useResponseTimeData = (deviceIds, timeRange, allDevices = []) => {
  const timeRangeMinutes = getTimeRangeMinutes(timeRange);

  // Cihaz ID'lerine göre isim eşleştirme tablosu oluştur
  const deviceNameMap = useMemo(() => {
    const map = {};
    if (Array.isArray(allDevices)) {
      allDevices.forEach(device => {
        if (device && device.id) {
          map[device.id] = device.name || `Cihaz ${device.id.substring(0, 4)}`;
        }
      });
    }
    return map;
  }, [allDevices]);

  return useQuery({
    queryKey: ['responseTime', deviceIds, timeRange],
    queryFn: async () => {
      console.log('Yanıt süresi verisi için istek:', { deviceIds, timeRangeMinutes });

      // Cihaz seçili değilse boş veri seti döndür
      if (!deviceIds || deviceIds.length === 0) {
        console.log('Yanıt süresi için seçili cihaz yok');

        // Boş veri seti döndür
        const timePoints = generateTimePoints(timeRangeMinutes, 24);
        const labels = formatTimeLabels(timePoints, timeRange);
        return { labels, datasets: [] };
      }

      try {
        // Her cihaz için yanıt süresi geçmişini çek
        const promises = deviceIds.map(deviceId =>
          monitorService.getIcmpHistory(deviceId, timeRangeMinutes)
            .then(data => {
              console.log(`Device ${deviceId} ICMP history:`, {
                count: data?.length || 0,
                sample: data?.length > 0 ? data[0] : null
              });
              return data;
            })
            .catch(error => {
              console.error(`Error fetching ICMP history for device ${deviceId}:`, error);
              return []; // Hata durumunda boş dizi döndür
            })
        );

        const results = await Promise.all(promises);

        // Gerçek veri var mı kontrol et
        const hasData = results.some(result => result && result.length > 0);
        if (!hasData) {
          console.log('Yanıt süresi için gerçek veri bulunamadı');

          // Boş veri seti döndür
          const timePoints = generateTimePoints(timeRangeMinutes, 24);
          const labels = formatTimeLabels(timePoints, timeRange);
          return { labels, datasets: [] };
        }

        // Veri noktası sayısı
        const maxDataPoints = 24;

        // Zaman noktalarını oluştur
        const timePoints = generateTimePoints(timeRangeMinutes, maxDataPoints);

        // Zaman etiketlerini formatla
        const labels = formatTimeLabels(timePoints, timeRange);

        // Sonuçları Chart.js formatına dönüştür
        const datasets = [];

        // Her cihaz için veri seti oluştur
        deviceIds.forEach((deviceId, index) => {
          const deviceData = results[index] || [];
          if (deviceData.length === 0) return; // Veri yoksa bu cihazı atla

          // Cihaz adını bul - önce deviceNameMap'ten, sonra deviceData'dan, son olarak varsayılan
          const deviceName = deviceNameMap[deviceId] ||
                            (deviceData[0]?.device?.name) ||
                            `Cihaz ${index + 1}`;

          // Zaman noktalarına göre veri noktaları oluştur
          const data = timePoints.map(timestamp => {
            // En yakın zaman damgasına sahip veri noktasını bul
            const dataPoint = deviceData.find(item => {
              return item.timestamp && Math.abs(parseInt(item.timestamp) - timestamp) < 60000; // 1 dakika tolerans
            });

            if (dataPoint) {
              // responseTime veya details.time değerini kullan
              if (dataPoint.responseTime) {
                return parseInt(dataPoint.responseTime);
              } else if (dataPoint.details && dataPoint.details.time) {
                return parseInt(dataPoint.details.time);
              }
            }

            // Veri noktası bulunamadıysa null döndür (grafik bu noktayı göstermez)
            return null;
          });

          // Veri noktalarının en az %10'u dolu olmalı
          const validDataPoints = data.filter(point => point !== null).length;
          if (validDataPoints < maxDataPoints * 0.1) {
            console.log(`Device ${deviceId} has insufficient data points (${validDataPoints}/${maxDataPoints}), skipping`);
            return; // Yeterli veri yoksa bu cihazı atla
          }

          datasets.push(createDataset(deviceName, data, index));
        });

        console.log('Yanıt süresi verisi oluşturuldu:', {
          labels: labels.length,
          datasets: datasets.length,
          sampleData: datasets[0]?.data?.slice(0, 5)
        });

        return { labels, datasets };
      } catch (error) {
        console.error('Yanıt süresi verileri alınırken hata:', error);

        // Hata durumunda boş veri seti döndür
        const timePoints = generateTimePoints(timeRangeMinutes, 24);
        const labels = formatTimeLabels(timePoints, timeRange);
        return { labels, datasets: [] };
      }
    },
    // Periyodik yenileme kaldırıldı - sadece ilk yükleme ve manuel tetikleme ile çalışacak
    retry: 2, // Hata durumunda 2 kez daha dene
    enabled: deviceIds && deviceIds.length > 0, // Cihaz seçili değilse çalıştırma
  });
};

// Durum geçmişi verilerini getiren hook
export const useStatusHistory = (timeRange) => {
  const timeRangeMinutes = getTimeRangeMinutes(timeRange);

  return useQuery({
    queryKey: ['statusHistory', timeRange],
    queryFn: async () => {
      try {
        // Durum geçmişini çek
        const statusHistory = await monitorService.getStatusHistory(timeRangeMinutes);
        console.log('Durum geçmişi verisi:', statusHistory);

        // Backend'den gelen veri zaten Chart.js formatında mı?
        if (statusHistory && statusHistory.labels && statusHistory.datasets) {
          console.log('Backend verisi Chart.js formatında, doğrudan kullanılıyor');
          return statusHistory;
        }

        // Gerçek veri var mı kontrol et (daha esnek kontrol)
        if (!statusHistory ||
            (Array.isArray(statusHistory) && statusHistory.length === 0) ||
            (typeof statusHistory === 'object' && Object.keys(statusHistory).length === 0)) {
          console.log('Durum geçmişi verisi bulunamadı, boş veri seti döndürülüyor');

          // Boş veri seti döndür
          const dataPoints = 12;
          const timePoints = generateTimePoints(timeRangeMinutes, dataPoints);
          const labels = formatTimeLabels(timePoints, timeRange);

          return {
            labels,
            datasets: [
              createStatusDataset('Çevrimiçi', Array(dataPoints).fill(0), STATUS_TYPES.UP),
              createStatusDataset('Çevrimdışı', Array(dataPoints).fill(0), STATUS_TYPES.DOWN),
              createStatusDataset('Uyarı Veren', Array(dataPoints).fill(0), STATUS_TYPES.WARNING),
              createStatusDataset('Kritik', Array(dataPoints).fill(0), STATUS_TYPES.CRITICAL),
              createStatusDataset('Performans Düşük', Array(dataPoints).fill(0), STATUS_TYPES.DEGRADED),
              createStatusDataset('Kararsız', Array(dataPoints).fill(0), STATUS_TYPES.FLAPPING),
              createStatusDataset('Kısmi Veri', Array(dataPoints).fill(0), STATUS_TYPES.PARTIAL),
              createStatusDataset('Bilinmiyor', Array(dataPoints).fill(0), STATUS_TYPES.UNKNOWN)
            ]
          };
        }

        // Veri noktası sayısı
        const dataPoints = 12; // Gösterilecek veri noktası sayısı

        // Zaman noktalarını oluştur
        const timePoints = generateTimePoints(timeRangeMinutes, dataPoints);

        // Her zaman noktası için durum dağılımını hesapla
        const onlineData = [];
        const offlineData = [];
        const warningData = [];
        const criticalData = [];
        const degradedData = [];
        const flappingData = [];
        const partialData = [];
        const unknownData = [];

        // Eğer statusHistory bir dizi ise (eski format)
        if (Array.isArray(statusHistory)) {
          console.log('Durum geçmişi verisi dizi formatında, işleniyor');

          timePoints.forEach(timePoint => {
            // Bu zaman noktasından önceki en yakın durum kayıtlarını bul
            const pointStatuses = statusHistory.filter(item => item.timestamp <= timePoint);

            // Her cihaz için en son durumu al
            const deviceStatuses = {};
            pointStatuses.forEach(item => {
              if (!deviceStatuses[item.deviceId] || item.timestamp > deviceStatuses[item.deviceId].timestamp) {
                deviceStatuses[item.deviceId] = item;
              }
            });

            // Durum sayılarını hesapla
            let online = 0;
            let offline = 0;
            let warning = 0;
            let critical = 0;
            let degraded = 0;
            let flapping = 0;
            let partial = 0;
            let unknown = 0;

            Object.values(deviceStatuses).forEach(status => {
              switch (status.status) {
                case STATUS_TYPES.UP:
                  online++;
                  break;
                case STATUS_TYPES.DOWN:
                  offline++;
                  break;
                case STATUS_TYPES.WARNING:
                  warning++;
                  break;
                case STATUS_TYPES.CRITICAL:
                  critical++;
                  break;
                case STATUS_TYPES.DEGRADED:
                  degraded++;
                  break;
                case STATUS_TYPES.FLAPPING:
                  flapping++;
                  break;
                case STATUS_TYPES.PARTIAL:
                  partial++;
                  break;
                default:
                  unknown++; // Bilinmeyen durumları unknown olarak say
              }
            });

            onlineData.push(online);
            offlineData.push(offline);
            warningData.push(warning);
            criticalData.push(critical);
            degradedData.push(degraded);
            flappingData.push(flapping);
            partialData.push(partial);
            unknownData.push(unknown);
          });
        } else {
          // Veri formatı bilinmiyor, varsayılan değerler kullan
          console.log('Durum geçmişi verisi bilinmeyen formatta, varsayılan değerler kullanılıyor');
          for (let i = 0; i < dataPoints; i++) {
            onlineData.push(0);
            offlineData.push(0);
            warningData.push(0);
            criticalData.push(0);
            degradedData.push(0);
            flappingData.push(0);
            partialData.push(0);
            unknownData.push(0);
          }
        }

        // Etiketleri formatla
        const labels = formatTimeLabels(timePoints, timeRange);

        // Veri setlerini oluştur
        return {
          labels,
          datasets: [
            createStatusDataset('Çevrimiçi', onlineData, STATUS_TYPES.UP),
            createStatusDataset('Çevrimdışı', offlineData, STATUS_TYPES.DOWN),
            createStatusDataset('Uyarı Veren', warningData, STATUS_TYPES.WARNING),
            createStatusDataset('Kritik', criticalData, STATUS_TYPES.CRITICAL),
            createStatusDataset('Performans Düşük', degradedData, STATUS_TYPES.DEGRADED),
            createStatusDataset('Kararsız', flappingData, STATUS_TYPES.FLAPPING),
            createStatusDataset('Kısmi Veri', partialData, STATUS_TYPES.PARTIAL),
            createStatusDataset('Bilinmiyor', unknownData, STATUS_TYPES.UNKNOWN)
          ]
        };
      } catch (error) {
        console.error('Durum geçmişi verisi alınırken hata:', error);

        // Hata durumunda boş veri seti döndür
        const dataPoints = 12;
        const timePoints = generateTimePoints(timeRangeMinutes, dataPoints);
        const labels = formatTimeLabels(timePoints, timeRange);

        return {
          labels,
          datasets: [
            createStatusDataset('Çevrimiçi', Array(dataPoints).fill(0), STATUS_TYPES.UP),
            createStatusDataset('Çevrimdışı', Array(dataPoints).fill(0), STATUS_TYPES.DOWN),
            createStatusDataset('Uyarı Veren', Array(dataPoints).fill(0), STATUS_TYPES.WARNING),
            createStatusDataset('Kritik', Array(dataPoints).fill(0), STATUS_TYPES.CRITICAL),
            createStatusDataset('Performans Düşük', Array(dataPoints).fill(0), STATUS_TYPES.DEGRADED),
            createStatusDataset('Kararsız', Array(dataPoints).fill(0), STATUS_TYPES.FLAPPING),
            createStatusDataset('Kısmi Veri', Array(dataPoints).fill(0), STATUS_TYPES.PARTIAL),
            createStatusDataset('Bilinmiyor', Array(dataPoints).fill(0), STATUS_TYPES.UNKNOWN)
          ]
        };
      }
    },
    // Periyodik yenileme kaldırıldı - sadece ilk yükleme ve manuel tetikleme ile çalışacak
    retry: 2, // Hata durumunda 2 kez daha dene
  });
};

// Durum dağılımı verilerini hesaplayan hook
export const useStatusDistribution = (devices, statuses) => {
  return useMemo(() => {
    // Varsayılan boş veri yapısı
    const emptyResult = {
      stats: {
        total: 0,
        online: 0,
        offline: 0,
        warning: 0,
        critical: 0,
        degraded: 0,
        flapping: 0,
        partial: 0,
        unknown: 0
      },
      data: {
        labels: ['Çevrimiçi', 'Çevrimdışı', 'Uyarı Veren', 'Kritik', 'Performans Düşük', 'Kararsız', 'Kısmi Veri', 'Bilinmiyor'],
        datasets: [
          {
            data: [0, 0, 0, 0, 0, 0, 0, 0],
            backgroundColor: (() => {
              const colors = getChartStatusColors();
              return [
                colors[STATUS_TYPES.UP].bg,
                colors[STATUS_TYPES.DOWN].bg,
                colors[STATUS_TYPES.WARNING].bg,
                colors[STATUS_TYPES.CRITICAL].bg,
                colors[STATUS_TYPES.DEGRADED].bg,
                colors[STATUS_TYPES.FLAPPING].bg,
                colors[STATUS_TYPES.PARTIAL].bg,
                colors[STATUS_TYPES.UNKNOWN].bg
              ];
            })(),
            borderColor: (() => {
              const colors = getChartStatusColors();
              return [
                colors[STATUS_TYPES.UP].border,
                colors[STATUS_TYPES.DOWN].border,
                colors[STATUS_TYPES.WARNING].border,
                colors[STATUS_TYPES.CRITICAL].border,
                colors[STATUS_TYPES.DEGRADED].border,
                colors[STATUS_TYPES.FLAPPING].border,
                colors[STATUS_TYPES.PARTIAL].border,
                colors[STATUS_TYPES.UNKNOWN].border
              ];
            })(),
            borderWidth: 1,
          }
        ]
      }
    };

    // Veri kontrolü
    if (!devices || !Array.isArray(devices)) {
      console.log('Durum dağılımı için cihaz verisi bulunamadı:', { devices });
      return emptyResult;
    }

    if (devices.length === 0) {
      console.log('Durum dağılımı için cihaz listesi boş');
      return emptyResult;
    }

    if (!statuses || typeof statuses !== 'object' || Object.keys(statuses).length === 0) {
      console.log('Durum dağılımı için durum verisi bulunamadı:', { statuses });
      return emptyResult;
    }

    console.log('Durum dağılımı için veri:', {
      deviceCount: devices.length,
      statusCount: Object.keys(statuses).length,
      sampleDevice: devices[0],
      sampleStatus: statuses[devices[0]?.id]
    });

    try {
      // Durum sayılarını hesapla
      let online = 0;
      let offline = 0;
      let warning = 0;
      let critical = 0;
      let degraded = 0;
      let flapping = 0;
      let partial = 0;
      let unknown = 0;

      devices.forEach(device => {
        if (!device || !device.id) return;

        const deviceStatus = statuses[device.id];
        console.log(`Device ${device.name} (${device.id}) status:`, deviceStatus);

        // Sadece hesaplanmış durumu (calculatedStatus) kullan
        const status = deviceStatus?.calculatedStatus || 'unknown';
        console.log(`Device ${device.name} calculated status:`, status);

        // Durum sayılarını güncelle
        switch (status) {
          case STATUS_TYPES.UP:
            online++;
            break;
          case STATUS_TYPES.DOWN:
            offline++;
            break;
          case STATUS_TYPES.WARNING:
            warning++;
            break;
          case STATUS_TYPES.CRITICAL:
            critical++;
            break;
          case STATUS_TYPES.DEGRADED:
            degraded++;
            break;
          case STATUS_TYPES.FLAPPING:
            flapping++;
            break;
          case STATUS_TYPES.PARTIAL:
            partial++;
            break;
          default:
            unknown++; // Bilinmeyen durumları unknown olarak say
        }
      });

      const total = devices.length;

      console.log('Calculated status counts:', {
        total, online, offline, warning, critical, degraded, flapping, partial, unknown
      });

      // Grafik verilerini oluştur
      const data = {
        labels: ['Çevrimiçi', 'Çevrimdışı', 'Uyarı Veren', 'Kritik', 'Performans Düşük', 'Kararsız', 'Kısmi Veri', 'Bilinmiyor'],
        datasets: [
          {
            data: [online, offline, warning, critical, degraded, flapping, partial, unknown],
            backgroundColor: (() => {
              const colors = getChartStatusColors();
              return [
                colors[STATUS_TYPES.UP].bg,
                colors[STATUS_TYPES.DOWN].bg,
                colors[STATUS_TYPES.WARNING].bg,
                colors[STATUS_TYPES.CRITICAL].bg,
                colors[STATUS_TYPES.DEGRADED].bg,
                colors[STATUS_TYPES.FLAPPING].bg,
                colors[STATUS_TYPES.PARTIAL].bg,
                colors[STATUS_TYPES.UNKNOWN].bg
              ];
            })(),
            borderColor: (() => {
              const colors = getChartStatusColors();
              return [
                colors[STATUS_TYPES.UP].border,
                colors[STATUS_TYPES.DOWN].border,
                colors[STATUS_TYPES.WARNING].border,
                colors[STATUS_TYPES.CRITICAL].border,
                colors[STATUS_TYPES.DEGRADED].border,
                colors[STATUS_TYPES.FLAPPING].border,
                colors[STATUS_TYPES.PARTIAL].border,
                colors[STATUS_TYPES.UNKNOWN].border
              ];
            })(),
            borderWidth: 1,
          }
        ]
      };

      return {
        stats: {
          total, online, offline, warning, critical, degraded, flapping, partial, unknown: 0
        },
        data
      };
    } catch (error) {
      console.error('Durum dağılımı hesaplanırken hata:', error);
      return emptyResult;
    }
  }, [devices, statuses]);
};

// Kategori analizi verilerini hesaplayan hook
export const useGroupAnalysis = (devices, statuses) => {
  return useMemo(() => {
    // Varsayılan boş veri yapısı
    const emptyResult = {
      groupedByCategory: {},
      data: {
        labels: ['Varsayılan'],
        datasets: [
          createStatusDataset('Çevrimiçi', [0], STATUS_TYPES.UP),
          createStatusDataset('Çevrimdışı', [0], STATUS_TYPES.DOWN),
          createStatusDataset('Uyarı Veren', [0], STATUS_TYPES.WARNING),
          createStatusDataset('Kritik', [0], STATUS_TYPES.CRITICAL),
          createStatusDataset('Performans Düşük', [0], STATUS_TYPES.DEGRADED),
          createStatusDataset('Kararsız', [0], STATUS_TYPES.FLAPPING),
          createStatusDataset('Kısmi Veri', [0], STATUS_TYPES.PARTIAL),
          createStatusDataset('Bilinmiyor', [0], STATUS_TYPES.UNKNOWN)
        ]
      }
    };

    // Veri kontrolü
    if (!devices || !Array.isArray(devices)) {
      console.log('Kategori analizi için cihaz verisi bulunamadı:', { devices });
      return emptyResult;
    }

    if (devices.length === 0) {
      console.log('Kategori analizi için cihaz listesi boş');
      return emptyResult;
    }

    if (!statuses || typeof statuses !== 'object' || Object.keys(statuses).length === 0) {
      console.log('Kategori analizi için durum verisi bulunamadı:', { statuses });
      return emptyResult;
    }

    console.log('Kategori analizi için veri:', {
      deviceCount: devices.length,
      statusCount: Object.keys(statuses).length,
      deviceCategories: [...new Set(devices.map(d => d.group || 'Varsayılan'))]
    });

    try {
      // Kategori bazlı durum sayılarını hesapla
      const groupedByCategory = {};

      devices.forEach(device => {
        if (!device || !device.id) return;

        const category = device.group || 'Varsayılan';
        const deviceStatus = statuses[device.id];

        // Sadece hesaplanmış durumu (calculatedStatus) kullan
        const status = deviceStatus?.calculatedStatus || 'unknown';
        console.log(`Device ${device.name} category ${category} calculated status:`, status);

        if (!groupedByCategory[category]) {
          groupedByCategory[category] = {
            online: 0,
            offline: 0,
            warning: 0,
            critical: 0,
            degraded: 0,
            flapping: 0,
            partial: 0,
            unknown: 0
          };
        }

        // Durum sayılarını güncelle
        switch (status) {
          case STATUS_TYPES.UP:
            groupedByCategory[category].online++;
            break;
          case STATUS_TYPES.DOWN:
            groupedByCategory[category].offline++;
            break;
          case STATUS_TYPES.WARNING:
            groupedByCategory[category].warning++;
            break;
          case STATUS_TYPES.CRITICAL:
            groupedByCategory[category].critical++;
            break;
          case STATUS_TYPES.DEGRADED:
            groupedByCategory[category].degraded++;
            break;
          case STATUS_TYPES.FLAPPING:
            groupedByCategory[category].flapping++;
            break;
          case STATUS_TYPES.PARTIAL:
            groupedByCategory[category].partial++;
            break;
          default:
            groupedByCategory[category].unknown++; // Bilinmeyen durumları unknown olarak say
        }
      });

      // Eğer hiç kategori yoksa varsayılan kategori ekle
      if (Object.keys(groupedByCategory).length === 0) {
        groupedByCategory['Varsayılan'] = {
          online: 0,
          offline: 0,
          warning: 0,
          critical: 0,
          degraded: 0,
          flapping: 0,
          partial: 0,
          unknown: 0
        };
      }

      console.log('Calculated category stats:', groupedByCategory);

      // Grafik verilerini oluştur
      const data = {
        // Kategori isimlerini formatla
        labels: Object.keys(groupedByCategory).map(category => {
          if (category.includes('/')) {
            return getFormattedSubCategory(category);
          }
          return category;
        }),
        datasets: [
          createStatusDataset('Çevrimiçi', Object.values(groupedByCategory).map(category => category.online), STATUS_TYPES.UP),
          createStatusDataset('Çevrimdışı', Object.values(groupedByCategory).map(category => category.offline), STATUS_TYPES.DOWN),
          createStatusDataset('Uyarı Veren', Object.values(groupedByCategory).map(category => category.warning), STATUS_TYPES.WARNING),
          createStatusDataset('Kritik', Object.values(groupedByCategory).map(category => category.critical), STATUS_TYPES.CRITICAL),
          createStatusDataset('Performans Düşük', Object.values(groupedByCategory).map(category => category.degraded), STATUS_TYPES.DEGRADED),
          createStatusDataset('Kararsız', Object.values(groupedByCategory).map(category => category.flapping), STATUS_TYPES.FLAPPING),
          createStatusDataset('Kısmi Veri', Object.values(groupedByCategory).map(category => category.partial), STATUS_TYPES.PARTIAL),
          createStatusDataset('Bilinmiyor', Object.values(groupedByCategory).map(category => category.unknown), STATUS_TYPES.UNKNOWN)
        ]
      };

      return { groupedByCategory, data };
    } catch (error) {
      console.error('Kategori analizi hesaplanırken hata:', error);
      return emptyResult;
    }
  }, [devices, statuses]);
};

// Grafik ayarlarını oluşturan hook'lar
export const useChartOptions = (showLegend, showGrid) => {
  // Pasta grafik ayarları
  const pieChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 1.5,
    plugins: {
      legend: {
        position: 'right',
        display: showLegend,
        labels: {
          boxWidth: 15,
          padding: 15,
          usePointStyle: true,
          pointStyle: 'circle',
          font: {
            size: 12
          }
        },
        maxWidth: 200
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
            return `${label}: ${value} cihaz (${percentage}%)`;
          }
        }
      }
    }
  }), [showLegend]);

  // Halka grafik ayarları
  const doughnutChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 1.5,
    cutout: '70%',
    plugins: {
      legend: {
        position: 'right',
        display: showLegend,
        labels: {
          boxWidth: 15,
          padding: 15,
          usePointStyle: true,
          pointStyle: 'circle',
          font: {
            size: 12
          }
        },
        maxWidth: 200
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
            return `${label}: ${value} cihaz (${percentage}%)`;
          }
        }
      }
    }
  }), [showLegend]);

  // Polar alan grafik ayarları
  const polarAreaChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 1.5,
    plugins: {
      legend: {
        position: 'right',
        display: showLegend,
        labels: {
          boxWidth: 15,
          padding: 15,
          usePointStyle: true,
          pointStyle: 'circle',
          font: {
            size: 12
          }
        },
        maxWidth: 200
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
            return `${label}: ${value} cihaz (${percentage}%)`;
          }
        }
      }
    }
  }), [showLegend]);

  // Yanıt süresi çizgi grafik ayarları
  const responseLineChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 2,
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Yanıt Süresi (ms)'
        },
        ticks: {
          callback: function(value) {
            return value + ' ms';
          }
        },
        grid: {
          display: showGrid
        }
      },
      x: {
        title: {
          display: true,
          text: 'Zaman'
        },
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y + ' ms';
          }
        }
      }
    },
    interaction: {
      mode: 'index',
      intersect: false,
    },
  }), [showGrid, showLegend]);

  // Yanıt süresi sütun grafik ayarları
  const responseBarChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 2,
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Yanıt Süresi (ms)'
        },
        ticks: {
          callback: function(value) {
            return value + ' ms';
          }
        },
        grid: {
          display: showGrid
        }
      },
      x: {
        title: {
          display: true,
          text: 'Zaman'
        },
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y + ' ms';
          }
        }
      }
    },
    interaction: {
      mode: 'index',
      intersect: false,
    },
  }), [showGrid, showLegend]);

  // Durum geçmişi grafik ayarları
  const statusHistoryChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 2,
    layout: {
      padding: {
        bottom: 20
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        stacked: true,
        title: {
          display: true,
          text: 'Cihaz Sayısı'
        },
        grid: {
          display: showGrid
        }
      },
      x: {
        stacked: true,
        grid: {
          display: false
        },
        title: {
          display: true,
          text: 'Zaman'
        },
        ticks: {
          padding: 10,
          maxRotation: 45,
          minRotation: 0
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.parsed.y} cihaz`;
          }
        }
      }
    }
  }), [showGrid, showLegend]);

  // Kategori analizi grafik ayarları
  const groupChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 2,
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Cihaz Sayısı'
        },
        stacked: true,
        grid: {
          display: showGrid
        }
      },
      x: {
        title: {
          display: true,
          text: 'Cihaz Kategorileri'
        },
        stacked: true,
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y + ' cihaz';
          }
        }
      }
    }
  }), [showGrid, showLegend]);

  return {
    pieChartOptions,
    doughnutChartOptions,
    polarAreaChartOptions,
    responseLineChartOptions,
    responseBarChartOptions,
    statusHistoryChartOptions,
    groupChartOptions
  };
};

// Grafik türü seçenekleri
export const chartTypeOptions = {
  status: [
    { value: 'pie', label: 'Pasta Grafik' },
    { value: 'doughnut', label: 'Halka Grafik' },
    { value: 'polarArea', label: 'Polar Alan' }
  ],
  response: [
    { value: 'line', label: 'Çizgi Grafik' },
    { value: 'bar', label: 'Sütun Grafik' }
  ]
};

// Zaman aralığı seçenekleri
export const timeRangeOptions = [
  { value: '1h', label: 'Son 1 Saat' },
  { value: '6h', label: 'Son 6 Saat' },
  { value: '12h', label: 'Son 12 Saat' },
  { value: '24h', label: 'Son 24 Saat' },
  { value: '3d', label: 'Son 3 Gün' },
  { value: '7d', label: 'Son 7 Gün' },
  { value: '30d', label: 'Son 30 Gün' }
];
