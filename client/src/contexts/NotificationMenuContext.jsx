import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { useSocket } from './SocketContext';
import { useAuth } from './AuthContext';
import enhancedToastService from '../services/enhanced-toast-service';
import { useToastSettings } from './ToastSettingsContext';

// Bildirim menüsü context'i oluştur
const NotificationMenuContext = createContext();

// Bildirim menüsü provider bileşeni
export const NotificationMenuProvider = ({ children }) => {
  // Toast ayarları hook'u
  const { toastSettings } = useToastSettings();

  // State tanımlamaları - Sadece okunmamış bildirimler için
  const [unreadNotifications, setUnreadNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Axios istek kontrolcüsü
  const abortControllerRef = useRef(null);

  // Socket.io ve User context
  const socket = useSocket();
  const { user } = useAuth();

  // Okunmamış bildirimleri yükle
  const loadUnreadNotifications = useCallback(async () => {
    // Eğer aktif bir istek varsa iptal et
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Yeni bir AbortController oluştur
    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;

    setLoading(true);
    setError(null);

    try {
      // Sadece okunmamış bildirimleri al - limit 10, zaman sırasına göre
      const response = await axios.get('/api/notifications', {
        params: {
          status: 'new', // Sadece okunmamış
          limit: 10,     // Son 10 bildirim
          offset: 0,
          sortBy: 'createdAt',
          sortOrder: 'desc' // En yeni üstte
        },
        timeout: 30000,
        signal
      });

      if (response.data) {
        setUnreadNotifications(response.data);
        setUnreadCount(response.data.length);
      }
    } catch (error) {
      // İstek iptal edildiyse, hata mesajı gösterme
      if (error.name === 'AbortError' || error.name === 'CanceledError') {
        console.log('Okunmamış bildirimler isteği iptal edildi');
        return;
      }

      console.error('Okunmamış bildirimler yüklenirken hata:', error);
      setError('Bildirimler yüklenirken bir hata oluştu.');
      setUnreadNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
      // İstek tamamlandığında referansı temizle
      abortControllerRef.current = null;
    }
  }, []);

  // Sayfa yüklendiğinde okunmamış bildirimleri yükle
  useEffect(() => {
    loadUnreadNotifications();
  }, [loadUnreadNotifications]);

  // Bildirimi okundu olarak işaretle
  const markAsRead = useCallback(async (notificationId, showToast = false) => {
    // Optimistic update: Önce UI'ı güncelle
    setUnreadNotifications(prev => prev.filter(n => n.id !== notificationId));
    setUnreadCount(prev => Math.max(0, prev - 1));

    try {
      // ✅ markAsRead endpoint'i body beklemez
      const response = await axios.post(`/api/notifications/${notificationId}/read`);

      // API başarılı olursa, zaten UI güncellenmiş durumda
      if (!response.data) {
        // Eğer API başarısız olursa, verileri yeniden yükle
        loadUnreadNotifications();
      }
    } catch (error) {
      console.error(`Bildirim okundu olarak işaretlenirken hata (ID: ${notificationId}):`, error);
      // Hata durumunda verileri yeniden yükle
      loadUnreadNotifications();
    }
  }, [loadUnreadNotifications]);

  // Tüm bildirimleri okundu olarak işaretle
  const markAllAsRead = useCallback(async () => {
    if (unreadNotifications.length === 0) return;

    try {
      // ✅ markAllAsRead endpoint'i body beklemez
      const response = await axios.post('/api/notifications/read-all');

      if (response.data) {
        // Tüm bildirimleri temizle
        setUnreadNotifications([]);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('Tüm bildirimler okundu olarak işaretlenirken hata:', error);
    }
  }, [unreadNotifications.length]);

  // Yeni bildirim işleme
  const handleNewNotification = useCallback((data) => {
    // Socket'ten gelen veri formatı: {notification: {...}, timestamp: ...}
    const notification = data.notification || data;

    if (!notification || !notification.id) {
      console.warn('❌ Geçersiz bildirim verisi:', data);
      return;
    }

    // Sadece okunmamış bildirimleri ekliyoruz
    if (notification.status === 'new') {
      setUnreadNotifications(prev => {
        // Aynı bildirim zaten varsa ekleme
        if (prev.some(n => n.id === notification.id)) {
          return prev;
        }

        // Yeni bildirimi en üste ekle ve 10 ile sınırla
        const newList = [notification, ...prev].slice(0, 10);
        return newList;
      });

      setUnreadCount(prev => prev + 1);
    }

    // Toast bildirimi göster
    if (toastSettings) {
      enhancedToastService.showNotificationToast(notification, toastSettings);
    }
  }, [toastSettings]);

  // Bildirim güncelleme işleme
  const handleUpdateNotification = useCallback((data) => {
    // Socket'ten gelen veri formatı: {notification: {...}, timestamp: ...}
    const updatedNotification = data.notification || data;

    if (!updatedNotification || !updatedNotification.id) {
      console.warn('❌ Geçersiz güncelleme verisi:', data);
      return;
    }

    if (updatedNotification.status !== 'new') {
      // Bildirim okundu olarak işaretlendiyse listeden kaldır
      setUnreadNotifications(prev => prev.filter(n => n.id !== updatedNotification.id));
      setUnreadCount(prev => Math.max(0, prev - 1));
    } else {
      // Hala okunmamışsa güncelle
      setUnreadNotifications(prev =>
        prev.map(n => n.id === updatedNotification.id ? updatedNotification : n)
      );
    }
  }, []);

  // Toplu okundu işaretleme işleme
  const handleMarkAllAsRead = useCallback(() => {
    // Tüm bildirimleri temizle
    setUnreadNotifications([]);
    setUnreadCount(0);
  }, []);

  // 🔢 Real-time: Bildirim sayıları güncellendiğinde (sayı ve liste güncelleme)
  const handleCountsUpdated = useCallback((data) => {
    console.log('📊 Real-time unread count updated:', data.counts.unread);
    const newUnreadCount = data.counts.unread || 0;

    // Sayıyı güncelle
    setUnreadCount(newUnreadCount);

    // Eğer sayı değiştiyse ve mevcut liste ile uyumsuzsa, listeyi yeniden yükle
    setUnreadNotifications(prev => {
      if (prev.length !== newUnreadCount) {
        // Sayı uyumsuzsa listeyi yeniden yükle
        console.log('📊 Bildirim listesi sayı ile uyumsuz, yeniden yükleniyor...');
        loadUnreadNotifications();
      }
      return prev;
    });
  }, [loadUnreadNotifications]);

  // Socket.io olaylarını dinle
  useEffect(() => {
    if (!socket) return;

    // Olay dinleyicilerini ekle
    socket.on('notification:new', handleNewNotification);
    socket.on('notification:update', handleUpdateNotification);
    socket.on('notifications:mark-all-read', handleMarkAllAsRead);
    socket.on('notification:counts-updated', handleCountsUpdated); // 🔢 Real-time sayı güncellemeleri

    // Temizlik fonksiyonu
    return () => {
      socket.off('notification:new', handleNewNotification);
      socket.off('notification:update', handleUpdateNotification);
      socket.off('notifications:mark-all-read', handleMarkAllAsRead);
      socket.off('notification:counts-updated', handleCountsUpdated); // 🔢 Temizlik
    };
  }, [socket, handleNewNotification, handleUpdateNotification, handleMarkAllAsRead, handleCountsUpdated]);

  // Context değeri
  const value = {
    unreadNotifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refreshNotifications: loadUnreadNotifications
  };

  return (
    <NotificationMenuContext.Provider value={value}>
      {children}
    </NotificationMenuContext.Provider>
  );
};

// Bildirim menüsü hook'u
export const useNotificationMenu = () => {
  const context = useContext(NotificationMenuContext);
  if (context === undefined) {
    throw new Error('useNotificationMenu must be used within a NotificationMenuProvider');
  }
  return context;
};

export default NotificationMenuContext;
