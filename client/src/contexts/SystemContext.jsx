import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { systemService } from '../services/api';
import { useSocket } from './SocketContext';

// Context oluştur
const SystemContext = createContext();

// Context hook'u
export const useSystem = () => {
  const context = useContext(SystemContext);
  if (!context) {
    throw new Error('useSystem must be used within a SystemProvider');
  }
  return context;
};

// Provider bileşeni
export const SystemProvider = ({ children }) => {
  const socket = useSocket();
  const [systemHealth, setSystemHealth] = useState({
    cpu: { usage: 0 },
    memory: { usage: 0 },
    disk: { usage: 0 },
    services: {}
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [socketConnected, setSocketConnected] = useState(false);

  // Sistem durumunu yükle
  const loadSystemStatus = useCallback(async () => {
    try {
      setLoading(true);
      const data = await systemService.getStatus();

      // Backend'den gelen veriyi frontend'in beklediği formata dönüştür
      const memoryTotal = data.systemInfo.memory.total;
      const memoryUsed = data.systemInfo.memory.used;
      const memoryUsagePercent = Math.round((memoryUsed / memoryTotal) * 100);

      // CPU yükünü hesapla (loadAvg[0] 1 dakikalık ortalama yük)
      const cpuCount = data.systemInfo.cpu.length;
      const loadAvg = data.systemInfo.loadAvg[0];
      const cpuUsagePercent = Math.min(Math.round((loadAvg / cpuCount) * 100), 100);

      // Disk kullanımı bilgisini al
      const diskUsagePercent = data.systemInfo.disk?.total?.pcent || 0;

      // Servis durumlarını dönüştür - tutarlı format kullan
      const services = {};
      Object.keys(data.services).forEach(key => {
        services[key] = {
          status: data.services[key].status, // 'up', 'down' veya 'warning' olarak kullan
          name: data.services[key].name,
          description: data.services[key].description
        };
      });

      setSystemHealth({
        cpu: { usage: cpuUsagePercent },
        memory: { usage: memoryUsagePercent },
        disk: { usage: diskUsagePercent },
        services: services,
        uptime: data.systemInfo.uptime
      });

      setLastUpdated(new Date());
      setLoading(false);
      setError(null);
    } catch (err) {
      console.error('Error loading system status:', err);
      setError('Sistem durumu yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, []);

  // Sayfa yüklendiğinde sistem durumunu yükle
  useEffect(() => {
    loadSystemStatus();

    // Her 60 saniyede bir manuel güncelleme (socket bağlantısı yoksa yedek olarak)
    const interval = setInterval(() => {
      if (!socket || !socket.connected) {
        loadSystemStatus();
      }
    }, 60000);

    return () => clearInterval(interval);
  }, [loadSystemStatus, socket]);

  // Socket.io bağlantı durumunu izle
  useEffect(() => {
    if (!socket) return;

    // Bağlantı durumunu izle
    const handleConnect = () => {
      console.log('SystemContext: Socket.io bağlantısı kuruldu');
      setSocketConnected(true);
    };

    const handleDisconnect = () => {
      console.log('SystemContext: Socket.io bağlantısı kesildi');
      setSocketConnected(false);
    };

    // Başlangıç durumunu ayarla
    setSocketConnected(socket.connected);

    // Olay dinleyicilerini ekle
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);

    // Temizlik fonksiyonu
    return () => {
      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
    };
  }, [socket]);

  // Socket.io olaylarını dinle
  useEffect(() => {
    if (!socket) return;

    // Sistem sağlığı güncellemelerini dinle
    socket.on('system:health:update', (data) => {
      console.log('SystemContext - system:health:update olayı alındı:', data);
      if (data && data.data) {
        // Backend'den gelen veriyi frontend'in beklediği formata dönüştür
        const healthData = data.data;

        // Bellek kullanımını hesapla
        const memoryTotal = healthData.systemInfo.memory.total;
        const memoryUsed = healthData.systemInfo.memory.used;
        const memoryUsagePercent = Math.round((memoryUsed / memoryTotal) * 100);

        // CPU yükünü hesapla
        const cpuCount = healthData.systemInfo.cpu.length;
        const loadAvg = healthData.systemInfo.loadAvg[0];
        const cpuUsagePercent = Math.min(Math.round((loadAvg / cpuCount) * 100), 100);

        // Disk kullanımı bilgisini al
        const diskUsagePercent = healthData.systemInfo.disk?.total?.pcent || 0;

        // Servis durumlarını dönüştür - tutarlı format kullan
        const services = {};
        Object.keys(healthData.services).forEach(key => {
          services[key] = {
            status: healthData.services[key].status, // 'up', 'down' veya 'warning' olarak kullan
            name: healthData.services[key].name,
            description: healthData.services[key].description
          };
        });

        // Sistem sağlığını güncelle
        setSystemHealth({
          cpu: { usage: cpuUsagePercent },
          memory: { usage: memoryUsagePercent },
          disk: { usage: diskUsagePercent },
          services: services,
          uptime: healthData.systemInfo.uptime
        });

        // Son güncelleme zamanını güncelle
        setLastUpdated(new Date(data.timestamp));
        setError(null);
      }
    });

    return () => {
      socket.off('system:health:update');
    };
  }, [socket]);

  // Socket.io bağlantısını yeniden kurma fonksiyonu
  const reconnectSocket = useCallback(() => {
    if (socket) {
      console.log('Manuel yeniden bağlanma başlatılıyor...');

      // Mevcut bağlantıyı kapat
      socket.disconnect();

      // Kısa bir gecikme sonra yeniden bağlan
      setTimeout(() => {
        socket.connect();
      }, 1000);
    }
  }, [socket]);

  // Context değerlerini sağla
  const value = {
    systemHealth,
    loading,
    error,
    lastUpdated,
    refreshSystemStatus: loadSystemStatus,
    reconnectSocket, // Manuel yeniden bağlanma fonksiyonu
    socketConnected // Socket bağlantı durumu
  };

  return (
    <SystemContext.Provider value={value}>
      {children}
    </SystemContext.Provider>
  );
};
