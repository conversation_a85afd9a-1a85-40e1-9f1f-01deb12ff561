import React, { createContext, useContext, useState, useEffect } from 'react';
import { settingsService } from '../services/api';

// Toast ayarları context'i
const ToastSettingsContext = createContext();

// Varsayılan toast ayarları
const DEFAULT_TOAST_SETTINGS = {
  toastsEnabled: true,
  toastDuration: 5000,
  toastDeduplication: true,
  toastGrouping: true,
  toastSounds: true // Varsayılan olarak açık
};

// Toast ayarları provider
export const ToastSettingsProvider = ({ children }) => {
  const [toastSettings, setToastSettings] = useState(DEFAULT_TOAST_SETTINGS);
  const [loading, setLoading] = useState(true);

  // Ayarları yükle
  const loadToastSettings = async () => {
    try {
      setLoading(true);
      const settings = await settingsService.getAll();

      // Toast ayarlarını filtrele ve varsayılanlarla birleştir
      const toastKeys = Object.keys(DEFAULT_TOAST_SETTINGS);
      const toastData = {};

      toastKeys.forEach(key => {
        if (settings[key] !== undefined) {
          // String değerleri uygun tiplere dönüştür
          if (key === 'toastDuration') {
            toastData[key] = parseInt(settings[key]) || DEFAULT_TOAST_SETTINGS[key];
          } else {
            toastData[key] = settings[key];
          }
        } else {
          toastData[key] = DEFAULT_TOAST_SETTINGS[key];
        }
      });

      setToastSettings(toastData);
    } catch (error) {
      console.error('Toast ayarları yüklenirken hata:', error);
      setToastSettings(DEFAULT_TOAST_SETTINGS);
    } finally {
      setLoading(false);
    }
  };

  // Sayfa yüklendiğinde ayarları getir
  useEffect(() => {
    loadToastSettings();
  }, []);

  // Ayarları güncelle
  const updateToastSettings = (newSettings) => {
    setToastSettings(prev => ({
      ...prev,
      ...newSettings
    }));
  };

  // Toast deduplication için son toast'ları takip et
  const [recentToasts, setRecentToasts] = useState(new Map());

  // Toast'ın gösterilip gösterilmeyeceğini kontrol et
  const shouldShowToast = (notification) => {
    if (!toastSettings.toastsEnabled) {
      return false;
    }

    if (!toastSettings.toastDeduplication) {
      return true;
    }

    // Deduplication kontrolü
    const key = `${notification.category}-${notification.title}`;
    const now = Date.now();
    const lastShown = recentToasts.get(key);

    // Son 30 saniye içinde aynı toast gösterilmişse, gösterme
    if (lastShown && (now - lastShown) < 30000) {
      return false;
    }

    // Toast'ı kaydet
    setRecentToasts(prev => {
      const newMap = new Map(prev);
      newMap.set(key, now);

      // 5 dakikadan eski kayıtları temizle
      for (const [k, v] of newMap.entries()) {
        if (now - v > 300000) {
          newMap.delete(k);
        }
      }

      return newMap;
    });

    return true;
  };

  // Gruplama için bekleyen toast'lar
  const [pendingToasts, setPendingToasts] = useState(new Map());

  // Gruplu toast göster
  const showGroupedToast = (notifications) => {
    if (!toastSettings.toastGrouping || notifications.length === 1) {
      return notifications[0];
    }

    // Kategori bazlı gruplama
    const categories = [...new Set(notifications.map(n => n.category))];

    if (categories.length === 1) {
      const category = categories[0];
      const count = notifications.length;

      return {
        ...notifications[0],
        title: `${count} yeni ${getCategoryDisplayName(category)} bildirimi`,
        message: `${count} yeni bildirim alındı. Detayları görmek için tıklayın.`,
        isGrouped: true,
        groupedNotifications: notifications
      };
    }

    // Karışık kategoriler
    return {
      ...notifications[0],
      title: `${notifications.length} yeni bildirim`,
      message: `Farklı kategorilerden ${notifications.length} yeni bildirim alındı.`,
      isGrouped: true,
      groupedNotifications: notifications
    };
  };

  // Kategori görünen adını al
  const getCategoryDisplayName = (category) => {
    const names = {
      status: 'durum',
      performance: 'performans',
      security: 'güvenlik',
      maintenance: 'bakım',
      system: 'sistem'
    };
    return names[category] || category;
  };

  const value = {
    toastSettings,
    loading,
    updateToastSettings,
    loadToastSettings,
    shouldShowToast,
    showGroupedToast,
    recentToasts,
    pendingToasts,
    setPendingToasts
  };

  return (
    <ToastSettingsContext.Provider value={value}>
      {children}
    </ToastSettingsContext.Provider>
  );
};

// Toast ayarları hook'u
export const useToastSettings = () => {
  const context = useContext(ToastSettingsContext);
  if (context === undefined) {
    throw new Error('useToastSettings must be used within a ToastSettingsProvider');
  }
  return context;
};

export default ToastSettingsContext;
