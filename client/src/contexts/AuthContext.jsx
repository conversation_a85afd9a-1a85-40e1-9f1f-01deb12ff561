import React, { createContext, useState, useEffect, useContext } from 'react';
import authService from '../services/auth';
import { settingsService } from '../services/api';
import { notificationService } from '../services/notification-service';

// Context oluştur
const AuthContext = createContext();

// Context Provider bileşeni
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Sayfa yüklendiğinde kullanıcı durumunu kontrol et
  useEffect(() => {
    const initAuth = async () => {
      try {
        // Local storage'dan kullanıcıyı al
        const currentUser = authService.getCurrentUser();

        if (currentUser) {
          setUser(currentUser);
        }
      } catch (err) {
        console.error('<PERSON><PERSON> doğrulama hatası:', err);
        setError('Oturum bilgileri yüklenirken bir hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // Kullanıcı çıkışı
  const logout = async () => {
    setLoading(true);

    try {
      await authService.logout();
      setUser(null);
    } catch (err) {
      console.error('Çıkış hatası:', err);
    } finally {
      setLoading(false);
    }
  };

  // Kullanıcı aktivitesini izle ve oturum zaman aşımını kontrol et
  useEffect(() => {
    if (!user) return; // Kullanıcı giriş yapmamışsa işlem yapma

    let sessionTimeoutId;
    let setupTimeoutId;
    let lastActivityTime = Date.now();

    // Kullanıcı aktivitesini izle
    const resetTimer = () => {
      lastActivityTime = Date.now();
      // Debug log kaldırıldı - çok sık tetikleniyor
    };

    // Ayarları yükle ve oturum zaman aşımını ayarla
    const setupSessionTimeout = async () => {
      try {
        const settings = await settingsService.getAll();

        // Otomatik çıkış devre dışı bırakılmışsa işlem yapma
        if (!settings.autoLogout) {
          console.log('Otomatik çıkış devre dışı bırakıldı');
          return;
        }

        // Oturum zaman aşımı süresini al (dakika)
        const sessionTimeout = parseInt(settings.sessionTimeout) || 60;
        const timeoutMs = sessionTimeout * 60 * 1000; // Dakika -> milisaniye

        console.log(`Oturum zaman aşımı süresi: ${sessionTimeout} dakika`);

        // Oturum zaman aşımı kontrolü
        const checkSessionTimeout = () => {
          const currentTime = Date.now();
          const inactiveTime = currentTime - lastActivityTime;

          // Debug log kaldırıldı - çok sık tetikleniyor

          if (inactiveTime >= timeoutMs) {
            // Oturum süresi dolmuşsa otomatik çıkış yap
            console.log(`Oturum zaman aşımı: ${sessionTimeout} dakika boyunca aktivite yok`);
            logout();

            // Kullanıcıya bilgi ver - Notification Service kullan
            notificationService.error('Oturum süreniz doldu. Güvenlik nedeniyle otomatik olarak çıkış yapıldı.', {
              description: 'Lütfen tekrar giriş yapın.',
              persistent: true, // Bildirim panelinde de göster
              category: 'security' // Güvenlik kategorisi
            });
          } else {
            // Hala aktif, timer'ı yeniden ayarla
            sessionTimeoutId = setTimeout(checkSessionTimeout, 60000); // Her dakika kontrol et
          }
        };

        // Kullanıcı giriş yaptıktan 5 saniye sonra ilk kontrolü başlat
        // Bu, giriş yapar yapmaz oturum zaman aşımı uyarısı alınmasını önler
        console.log('Oturum zaman aşımı kontrolü 5 saniye sonra başlayacak');
        sessionTimeoutId = setTimeout(checkSessionTimeout, 5000);
      } catch (error) {
        console.error('Oturum zaman aşımı ayarları yüklenirken hata:', error);
      }
    };

    // Kullanıcı aktivitesi olaylarını dinle
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, resetTimer);
    });

    // Kullanıcı giriş yaptıktan 2 saniye sonra oturum zaman aşımı kontrolünü başlat
    // Bu, sayfa yüklenirken gereksiz API çağrılarını önler
    console.log('Oturum zaman aşımı ayarları 2 saniye sonra yüklenecek');
    setupTimeoutId = setTimeout(setupSessionTimeout, 2000);

    // Temizlik fonksiyonu
    return () => {
      // Timer'ları temizle
      if (sessionTimeoutId) {
        clearTimeout(sessionTimeoutId);
      }

      if (setupTimeoutId) {
        clearTimeout(setupTimeoutId);
      }

      // Olay dinleyicilerini kaldır
      events.forEach(event => {
        document.removeEventListener(event, resetTimer);
      });
    };
  }, [user]);

  // Kullanıcı kaydı
  const register = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.register(userData);
      setUser(response.user);
      return response;
    } catch (err) {
      setError(err.response?.data?.error || 'Kayıt işlemi başarısız oldu');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Kullanıcı girişi
  const login = async (username, password) => {
    setLoading(true);
    setError(null);

    try {
      console.log('AuthContext login attempt with:', { username, password });
      const response = await authService.login(username, password);
      console.log('AuthContext login response:', response);
      setUser(response.user);

      // Socket.io ile kullanıcı girişi olayını gönder
      // Artık cihazları kontrol etmek yerine sadece mevcut durumları alacağız
      try {
        const socket = window.socket; // App.js'de tanımlanan global socket nesnesine eriş
        if (socket && socket.connected) {
          console.log('AuthContext: Kullanıcı girişi olayı gönderiliyor');

          // Yanıt için bir kerelik olay dinleyicisi ekle
          const responseHandler = (response) => {
            console.log('AuthContext: Kullanıcı girişi yanıtı alındı:', response);
            socket.off('user:login:response', responseHandler);

            if (response.success) {
              console.log(`Kullanıcı girişi başarılı: ${response.message}`);
            } else {
              console.error('Kullanıcı girişi işlemi başarısız:', response.error);
            }
          };

          // Yanıtı dinle
          socket.on('user:login:response', responseHandler);

          // 30 saniye timeout
          setTimeout(() => {
            socket.off('user:login:response', responseHandler);
          }, 30000);

          // İsteği gönder - artık cihazları kontrol etmek yerine sadece mevcut durumları alacağız
          socket.emit('user:login', { userId: response.user.id });

          // Mevcut durumları almak için yeni bir olay gönder
          console.log('AuthContext: Mevcut durumları isteniyor...');
          socket.emit('device:status:get');
        }
      } catch (socketErr) {
        console.error('Socket.io event error:', socketErr);
      }

      return response;
    } catch (err) {
      console.error('AuthContext login error:', err);

      // Şifre süresi dolmuş durumu için özel handling
      if (err.response?.data?.code === 'PASSWORD_EXPIRED') {
        setError('Şifrenizin süresi dolmuş. Şifre sıfırlama sayfasına yönlendiriliyorsunuz...');

        // 2 saniye sonra şifre sıfırlama sayfasına yönlendir
        setTimeout(() => {
          window.location.href = '/forgot-password';
        }, 2000);

        throw err;
      }

      setError(err.response?.data?.error || 'Giriş işlemi başarısız oldu');
      throw err;
    } finally {
      setLoading(false);
    }
  };



  // Kullanıcı bilgilerini güncelle
  const updateUser = (userData) => {
    setUser(userData);

    // Local storage'daki kullanıcı bilgilerini güncelle
    localStorage.setItem('user', JSON.stringify(userData));
  };

  // Context değerleri
  const value = {
    user,
    loading,
    error,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'admin',
    register,
    login,
    logout,
    updateUser
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error('useAuth hook must be used within an AuthProvider');
  }

  return context;
};

export default AuthContext;
