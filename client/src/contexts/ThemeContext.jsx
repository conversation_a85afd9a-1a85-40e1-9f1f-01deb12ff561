import React, { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    // Tarayıcı depolama alanından tema tercihini al
    const savedTheme = localStorage.getItem('theme');

    // Eğer kaydedilmiş bir tema varsa onu kullan
    if (savedTheme) {
      return savedTheme;
    }

    // Sistem tercihini kontrol etmeden varsayılan olarak light tema kullan
    return 'light';
  });

  // Tema değiştirme fonksiyonu
  const toggleTheme = () => {
    setTheme(prevTheme => {
      const newTheme = prevTheme === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', newTheme);
      return newTheme;
    });
  };

  // Te<PERSON> değiştiğinde HTML elementine dark sınıfın<PERSON> ekle veya kaldır
  useEffect(() => {
    const root = window.document.documentElement;

    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Tema hook'u
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
