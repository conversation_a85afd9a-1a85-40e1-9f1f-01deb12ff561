import React from 'react';

// Socket.io context
const SocketContext = React.createContext(null);

// Socket.io provider
export const SocketProvider = SocketContext.Provider;

// Socket.io consumer hook
export const useSocket = () => {
  const socket = React.useContext(SocketContext);
  // ✅ Socket null olabilir (bağlantı koptuğunda), bu normal bir durum
  // Sadece context dışında kullanılırsa hata fırlat
  if (socket === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return socket; // null olabilir, bu normal
};

export default SocketContext;
