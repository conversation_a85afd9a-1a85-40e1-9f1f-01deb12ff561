import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from './ui/button';
import { cn } from '../lib/utils';
import {
  Settings,
  Activity,
  Bell,
  MonitorCog,
  Users,
  Shield
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const SettingsSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';

  // Ayarlar alt menü öğeleri
  const settingsMenuItems = [
    { text: 'Genel', icon: <Settings className="h-5 w-5" />, tab: 'general' },
    { text: 'İzleme', icon: <Activity className="h-5 w-5" />, tab: 'monitoring' },
    { text: 'Bildirimler', icon: <Bell className="h-5 w-5" />, tab: 'notifications' },
    { text: '<PERSON>ü<PERSON><PERSON>', icon: <Shield className="h-5 w-5" />, tab: 'security' },
    { text: 'Sistem', icon: <MonitorCog className="h-5 w-5" />, tab: 'system' },
  ];

  // Admin kullanıcılar için Kullanıcılar menüsünü ekle
  if (isAdmin) {
    settingsMenuItems.push({ text: 'Kullanıcılar', icon: <Users className="h-5 w-5" />, tab: 'users' });
  }

  // URL'den aktif tab'i al
  const pathname = location.pathname;
  let activeTab = 'general';

  // /settings/[tab] formatındaki URL'den tab'i çıkar
  if (pathname.startsWith('/settings/')) {
    activeTab = pathname.split('/')[2];
  }

  // Menü öğesine tıklandığında
  const handleMenuItemClick = (tab) => {
    navigate(`/settings/${tab}`);
  };

  return (
    <div className="flex flex-col h-full bg-card">
      <div className="flex items-center h-14 border-b px-4">
        <h2 className="text-lg font-semibold">Ayarlar</h2>
      </div>
      <nav className="flex-1 overflow-auto p-2">
        <ul className="space-y-2">
          {settingsMenuItems.map((item) => (
            <li key={item.text}>
              <Button
                variant={activeTab === item.tab ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start h-10",
                  activeTab === item.tab && "font-medium"
                )}
                onClick={() => handleMenuItemClick(item.tab)}
              >
                {item.icon}
                <span className="ml-3">{item.text}</span>
              </Button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default SettingsSidebar;
