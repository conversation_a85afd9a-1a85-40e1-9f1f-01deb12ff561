import React from 'react';

import { Activity, AlertTriangle, Wifi, Clock } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';
import { useSystem } from '../contexts/SystemContext';
import { useSocket } from '../contexts/SocketContext';

const SystemStatusIndicator = () => {
  const { systemHealth, loading, error } = useSystem();
  const socket = useSocket();

  // Servis durumlarını al
  const services = systemHealth.services || {};

  // Tüm servislerin durumunu kontrol et
  const allServicesUp = Object.values(services).every(service => service.status === 'up');
  const anyServiceDown = Object.values(services).some(service => service.status === 'down');

  // Sistem çalışma süresini formatla
  const formatUptime = (seconds) => {
    if (!seconds) return 'Bilinmiyor';

    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    let result = '';
    if (days > 0) result += `${days} gün `;
    if (hours > 0) result += `${hours} saat `;
    if (minutes > 0) result += `${minutes} dakika`;

    return result || '1 dakikadan az';
  };

  if (loading || error || Object.keys(services).length === 0) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center justify-center h-8 w-8 rounded-full bg-muted/50">
              <Activity className="h-4 w-4 text-muted-foreground" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{loading ? 'Sistem durumu yükleniyor...' : error ? error : 'Sistem durumu bilinmiyor'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center space-x-1 px-2 py-1 rounded-full border">
            {anyServiceDown ? (
              <AlertTriangle className="h-4 w-4 text-destructive" />
            ) : (
              <Activity className={`h-4 w-4 ${allServicesUp ? 'text-success' : 'text-warning'}`} />
            )}
            <div className="flex items-center space-x-1">
              {Object.keys(services).map(key => (
                <div
                  key={key}
                  className={`h-2 w-2 rounded-full ${
                    services[key].status === 'up'
                      ? 'bg-success'
                      : services[key].status === 'down'
                        ? 'bg-destructive'
                        : 'bg-warning'
                  }`}
                />
              ))}
              {/* Socket bağlantı durumu göstergesi */}
              <div className={`h-2 w-2 rounded-full ml-1 ${socket && socket.connected ? 'bg-success' : 'bg-destructive'}`} title="Socket Bağlantısı"></div>
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent className="w-64">
          <div className="space-y-3">
            {/* Başlık */}
            <div className="border-b pb-1">
              <h3 className="font-medium text-sm">Sistem Durumu</h3>
            </div>

            {/* Sistem Çalışma Süresi */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs">Sistem Çalışma Süresi</span>
              </div>
              <span className="text-xs">
                {systemHealth.uptime ? (
                  formatUptime(systemHealth.uptime)
                ) : (
                  'Bilinmiyor'
                )}
              </span>
            </div>

            {/* Socket.io Bağlantı Durumu */}
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center gap-2">
                <Wifi className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs">Gerçek Zamanlı Bağlantı</span>
              </div>
              {socket ? (
                <span className={`text-xs ${socket.connected ? 'text-success' : 'text-destructive'}`}>
                  {socket.connected ? 'Çalışıyor' : 'Kesildi'}
                </span>
              ) : (
                <span className="text-xs text-destructive">Bağlantı Hatası</span>
              )}
            </div>

            {/* Servis Durumları */}
            <div className="border-t pt-2 mt-2">
              <p className="text-xs font-medium mb-2">Servis Durumları</p>
              <ul className="text-xs space-y-1">
                {Object.keys(services).map(key => (
                  <li key={key} className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <div className={`h-1.5 w-1.5 rounded-full ${
                        services[key].status === 'up'
                          ? 'bg-success'
                          : services[key].status === 'down'
                            ? 'bg-destructive'
                            : 'bg-warning'
                      }`} />
                      <span>{services[key].name}</span>
                    </div>
                    <span className={
                      services[key].status === 'up'
                        ? 'text-success'
                        : services[key].status === 'down'
                          ? 'text-destructive'
                          : 'text-warning'
                    }>
                      {services[key].status === 'up' ? 'Çalışıyor' : services[key].status === 'down' ? 'Çalışmıyor' : 'Uyarı'}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default SystemStatusIndicator;
