import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';

import ThemeToggle from '../ui/theme-toggle';
import NotificationMenu from '../NotificationMenu';
import SystemStatusIndicator from '../SystemStatusIndicator';
import { settingsService } from '../../services/api';
import useDocumentTitle from '../../hooks/useDocumentTitle';

import {
  LayoutDashboard,
  Server,
  Settings,
  Menu,
  ChevronLeft,
  User,
  LogOut,
  UserCog,
  Users,
  LineChart,
  Bell as BellOutlined,
  Activity,
  Bell,
  MonitorCog,
  PieChart,
  Clock,
  BarChart,
  Shield
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';


const MainLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [settingsExpanded, setSettingsExpanded] = useState(false);
  const [graphsExpanded, setGraphsExpanded] = useState(false);
  const [appTitle, setAppTitle] = useState('NetWatch');
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout, isAdmin } = useAuth();

  // Ayarları yükle
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = await settingsService.getAll();
        if (settings.appTitle) {
          setAppTitle(settings.appTitle);
        }
      } catch (error) {
        console.error('Ayarlar yüklenirken hata oluştu:', error);
      }
    };

    loadSettings();
  }, []);

  // Tarayıcı başlığını güncelle
  useDocumentTitle(appTitle);

  // Sayfa ilk yüklendiğinde çalışacak efekt
  useEffect(() => {
    // URL'yi kontrol et ve sidebar durumunu ayarla
    const isGraphsPage = location.pathname.includes('/graphs');
    const isSettingsPage = location.pathname.includes('/settings');

    // Grafikler veya ayarlar sayfasındaysak, sidebar'ı genişlet
    if (isGraphsPage || isSettingsPage) {
      setSidebarCollapsed(false);
    }

    // Grafikler sayfasındaysak, grafikler menüsünü genişlet
    if (isGraphsPage) {
      setGraphsExpanded(true);
    }

    // Ayarlar sayfasındaysak, ayarlar menüsünü genişlet
    if (isSettingsPage) {
      setSettingsExpanded(true);
    }
  }, []); // Sadece bir kez çalışsın

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      setSidebarOpen(!mobile);

      // Mobil olmayan ekranlarda sidebar'ı varsayılan olarak daralt
      // Ancak grafikler veya ayarlar sayfasındaysak daraltma
      if (!mobile && !location.pathname.includes('/graphs') && !location.pathname.includes('/settings')) {
        setSidebarCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, [location.pathname]);

  // Fare olayları için işleyiciler
  const handleMouseEnter = () => {
    if (!isMobile && sidebarCollapsed) {
      setIsHovering(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      setIsHovering(false);
      // Ayarlar menüsü veya grafikler sayfası açık değilse sidebar'ı daralt
      if (!settingsExpanded && !graphsExpanded) {
        setSidebarCollapsed(true);
      }
    }
  };

  // URL değiştiğinde sayfa değişikliklerini kontrol et
  useEffect(() => {
    // Ayarlar sayfasındaysak, ayarlar menüsünü genişlet ve sidebar'ı genişlet
    if (location.pathname.includes('/settings')) {
      setSettingsExpanded(true);
      setSidebarCollapsed(false);
    } else {
      // Ayarlar sayfasında değilsek, ayarlar menüsünü kapat
      setSettingsExpanded(false);
    }

    // Grafikler sayfasındaysak, grafikler menüsünü genişlet ve sidebar'ı genişlet
    if (location.pathname.includes('/graphs')) {
      setGraphsExpanded(true);
      setSidebarCollapsed(false);
    } else {
      // Grafikler sayfasında değilsek, grafikler menüsünü kapat
      setGraphsExpanded(false);
    }

    // Eğer hem grafikler hem de ayarlar sayfasında değilsek ve mobil değilsek, sidebar'ı daralt
    if (!location.pathname.includes('/graphs') && !location.pathname.includes('/settings') && !isMobile) {
      setSidebarCollapsed(true);
    }
  }, [location.pathname, isMobile]);

  // Ayarlar menüsüne tıklandığında
  const handleSettingsClick = () => {
    // Ayarlar menüsünün durumunu tersine çevir
    const newExpandedState = !settingsExpanded;
    setSettingsExpanded(newExpandedState);

    // Eğer ayarlar menüsü açılıyorsa, sidebar'ın genişlediğinden emin ol
    if (newExpandedState) {
      setSidebarCollapsed(false);

      // Eğer ayarlar sayfasında değilsek, ayarlar sayfasına yönlendir
      if (!location.pathname.includes('/settings')) {
        navigate('/settings/general');
      }
    } else {
      // Eğer ayarlar menüsü kapanıyorsa ve ayarlar sayfasındaysak, ana sayfaya dön
      if (location.pathname.includes('/settings')) {
        navigate('/');
      }
    }
  };

  // Grafikler menüsüne tıklandığında
  const handleGraphsClick = () => {
    // Grafikler menüsünün durumunu tersine çevir
    const newExpandedState = !graphsExpanded;
    setGraphsExpanded(newExpandedState);

    // Eğer grafikler menüsü açılıyorsa, sidebar'ın genişlediğinden emin ol
    if (newExpandedState) {
      setSidebarCollapsed(false);

      // Eğer grafikler sayfasında değilsek, ilk alt menü sayfasına yönlendir
      if (!location.pathname.includes('/graphs')) {
        navigate('/graphs/status');
      }
    } else {
      // Eğer grafikler menüsü kapanıyorsa ve grafikler sayfasındaysak, ana sayfaya dön
      if (location.pathname.includes('/graphs')) {
        navigate('/');
      }
    }
  };

  // Ayarlar alt menü öğeleri
  const settingsSubMenuItems = [
    { text: 'Genel', icon: <Settings className="h-5 w-5" />, path: '/settings/general' },
    { text: 'İzleme', icon: <Activity className="h-5 w-5" />, path: '/settings/monitoring' },
    { text: 'Bildirimler', icon: <Bell className="h-5 w-5" />, path: '/settings/notifications' },
    { text: 'Güvenlik', icon: <Shield className="h-5 w-5" />, path: '/settings/security' },
    { text: 'Sistem', icon: <MonitorCog className="h-5 w-5" />, path: '/settings/system' },
  ];

  // Admin kullanıcılar için Kullanıcılar menüsünü ekle
  if (isAdmin) {
    settingsSubMenuItems.push({ text: 'Kullanıcılar', icon: <Users className="h-5 w-5" />, path: '/settings/users' });
  }

  // Grafikler alt menü öğeleri
  const graphsSubMenuItems = [
    { text: 'Durum Dağılımı', icon: <PieChart className="h-5 w-5" />, path: '/graphs/status' },
    { text: 'Durum Geçmişi', icon: <Clock className="h-5 w-5" />, path: '/graphs/history' },
    { text: 'Yanıt Süresi', icon: <LineChart className="h-5 w-5" />, path: '/graphs/response' },
    { text: 'Kategori Analizi', icon: <BarChart className="h-5 w-5" />, path: '/graphs/groups' },
  ];

  const menuItems = [
    { text: 'Kontrol Paneli', icon: <LayoutDashboard className="h-5 w-5" />, path: '/' },
    { text: 'Cihazlar', icon: <Server className="h-5 w-5" />, path: '/devices' },
    {
      text: 'Grafikler',
      icon: <LineChart className="h-5 w-5" />,
      path: '/graphs',
      onClick: handleGraphsClick,
      expanded: graphsExpanded,
      subItems: graphsSubMenuItems
    },
    { text: 'Bildirimler', icon: <BellOutlined className="h-5 w-5" />, path: '/notifications' },
    {
      text: 'Ayarlar',
      icon: <Settings className="h-5 w-5" />,
      path: '/settings',
      onClick: handleSettingsClick,
      expanded: settingsExpanded,
      subItems: settingsSubMenuItems
    }
  ];

  // Not: Kullanıcılar menü öğesi kaldırıldı ve Ayarlar içerisine taşındı

  return (
    <div className="flex h-screen overflow-hidden bg-background max-w-full">
      <div className="flex min-w-0">
        {/* Sidebar */}
        <aside
          className={cn(
            "fixed inset-y-0 left-0 z-40 flex flex-col border-r bg-card shadow-md transition-all duration-200 ease-out md:relative md:translate-x-0",
            (sidebarCollapsed && !isHovering) ? "w-[80px]" : "w-[80%] md:w-[250px]",
            sidebarOpen ? "translate-x-0" : "-translate-x-full"
          )}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
        <div className="flex h-14 items-center border-b px-4">
          <h2 className={cn("text-lg font-semibold", (sidebarCollapsed && !isHovering) && "hidden")}>{appTitle}</h2>
          <Button
            variant="ghost"
            size="icon"
            className={cn("ml-auto md:hidden", (sidebarCollapsed && !isHovering) && "ml-0")}
            onClick={() => setSidebarOpen(false)}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
        </div>
        <nav className="flex-1 overflow-auto p-2">
          <ul className="space-y-2">
            {menuItems.map((item) => (
              <li key={item.text} className="flex flex-col">
                <Button
                  variant={
                    // Ana sayfa için özel kontrol
                    (item.path === '/' && location.pathname === '/') ||
                    // Diğer sayfalar için path ile başlayan kontrolü
                    (item.path !== '/' && location.pathname.startsWith(item.path))
                      ? "secondary"
                      : "ghost"
                  }
                  className={cn(
                    "w-full h-10",
                    (sidebarCollapsed && !isHovering) ? "justify-center px-2" : "justify-start",
                    ((item.path === '/' && location.pathname === '/') ||
                     (item.path !== '/' && location.pathname.startsWith(item.path))) && "font-medium"
                  )}
                  onClick={() => {
                    if (item.onClick) {
                      item.onClick();
                    } else {
                      navigate(item.path);
                      if (isMobile) setSidebarOpen(false);
                    }
                  }}
                >
                  {item.icon}
                  {(!sidebarCollapsed || isHovering) && (
                    <span className="ml-3 flex items-center justify-between w-full">
                      {item.text}
                      {item.subItems && (
                        <ChevronLeft className={cn(
                          "h-4 w-4 transition-transform duration-200 ease-out",
                          item.expanded ? "rotate-90" : "-rotate-90"
                        )} />
                      )}
                    </span>
                  )}
                </Button>

                {/* Alt menü öğeleri */}
                {item.subItems && item.expanded && (
                  <div className={cn(
                    "pl-4 mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-in-out",
                    (sidebarCollapsed && !isHovering) && "hidden",
                    item.expanded ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
                  )}>
                    {item.subItems.map((subItem) => (
                      <Button
                        key={subItem.text}
                        variant={location.pathname.startsWith(subItem.path) ? "secondary" : "ghost"}
                        size="sm"
                        className={cn(
                          "w-full justify-start h-9",
                          location.pathname.startsWith(subItem.path) && "font-medium"
                        )}
                        onClick={() => {
                          navigate(subItem.path);
                          if (isMobile) setSidebarOpen(false);
                        }}
                      >
                        {subItem.icon}
                        <span className="ml-2">{subItem.text}</span>
                      </Button>
                    ))}
                  </div>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </aside>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 flex-col overflow-hidden transition-all duration-200 ease-out min-w-0 max-w-full w-full">
        <header className="flex h-14 items-center border-b px-4 md:px-6">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-5 w-5" />
          </Button>
          <div className="ml-auto flex items-center gap-2">
            <SystemStatusIndicator />
            <ThemeToggle />
            <NotificationMenu />

            {/* Kullanıcı Menüsü */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="flex items-center gap-2 rounded-full border px-3">
                  <div className="relative h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                    {user?.username ? (
                      <span className="text-xs font-medium">{user?.username.charAt(0).toUpperCase()}</span>
                    ) : (
                      <User className="h-4 w-4" />
                    )}
                    <span className="absolute -bottom-0.5 -right-0.5 h-2.5 w-2.5 rounded-full bg-success border-2 border-background"></span>
                  </div>
                  <span className="hidden md:inline-block text-sm font-medium">{user?.username}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    {user?.username ? (
                      <span className="text-lg font-medium">{user?.username.charAt(0).toUpperCase()}</span>
                    ) : (
                      <User className="h-6 w-6" />
                    )}
                  </div>
                  <div className="flex flex-col space-y-0.5">
                    <p className="text-sm font-medium leading-none">{user?.username}</p>
                    <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => navigate('/profile')} className="cursor-pointer">
                  <UserCog className="mr-2 h-4 w-4" />
                  <span>Profil</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate('/settings')} className="cursor-pointer">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Ayarlar</span>
                </DropdownMenuItem>
                {isAdmin && (
                  <DropdownMenuItem onClick={() => navigate('/settings?tab=users')} className="cursor-pointer">
                    <Users className="mr-2 h-4 w-4" />
                    <span>Kullanıcı Yönetimi</span>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={logout} className="cursor-pointer text-destructive focus:text-destructive">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Çıkış Yap</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>
        <main className="flex-1 overflow-hidden min-w-0 max-w-full w-full">
          <div className="h-full w-full overflow-auto">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Mobile Overlay - Ana Menü */}
      {sidebarOpen && isMobile && (
        <div
          className="fixed inset-0 z-30 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile Overlay - Ayarlar Menüsü */}
      {settingsExpanded && isMobile && (
        <div
          className="fixed inset-0 z-30 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={() => {
            setSettingsExpanded(false);
            setSidebarCollapsed(false);
          }}
        />
      )}

      {/* Mobile Overlay - Grafikler Menüsü */}
      {graphsExpanded && isMobile && (
        <div
          className="fixed inset-0 z-30 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={() => {
            setGraphsExpanded(false);
            setSidebarCollapsed(false);
          }}
        />
      )}
    </div>
  );
};

export default MainLayout;
