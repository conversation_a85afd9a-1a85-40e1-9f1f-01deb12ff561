import React from 'react';
import { Document, Page, View, Text, StyleSheet, Image, Font } from '@react-pdf/renderer';

// Türkçe karakter desteği için özel font ekle
Font.register({
  family: 'Open Sans',
  src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-regular.ttf'
});

// PDF için stil tanımlamaları
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#F5F5F5',
    padding: 0,
    fontFamily: 'Open Sans',
  },
  header: {
    flexDirection: 'row',
    backgroundColor: '#3B82F6',
    padding: 15,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  headerLeft: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
    fontFamily: 'Open Sans',
  },
  headerCenter: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
    flex: 1,
    fontFamily: 'Open Sans',
  },
  headerRight: {
    color: 'white',
    fontSize: 10,
    textAlign: 'right',
    fontFamily: 'Open Sans',
  },
  content: {
    margin: 20,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 5,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartImage: {
    width: '90%',
    height: 'auto',
    marginVertical: 20,
  },
  footer: {
    flexDirection: 'row',
    backgroundColor: '#3B82F6',
    padding: 10,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 'auto',
  },
  footerLeft: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'Open Sans',
  },
  footerRight: {
    color: 'white',
    fontSize: 10,
    textAlign: 'right',
    fontFamily: 'Open Sans',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
    padding: 10,
    backgroundColor: '#F9FAFB',
    borderRadius: 5,
    width: '90%',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 15,
  },
  statDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  statText: {
    fontSize: 10,
    color: '#4B5563',
    fontFamily: 'Open Sans',
  },
});

// PDF Doküman Bileşeni
const ChartPDFDocument = ({ title, chartImageDataUrl, dateStr, timeStr, stats, companyName = 'NetWatch' }) => (
  <Document>
    <Page size="A4" orientation="landscape" style={styles.page}>
      {/* Üst Bilgi */}
      <View style={styles.header}>
        <Text style={styles.headerLeft}>{companyName}</Text>
        <Text style={styles.headerCenter}>{title}</Text>
        <Text style={styles.headerRight}>Oluşturulma Tarihi: {dateStr} {timeStr}</Text>
      </View>

      {/* İçerik */}
      <View style={styles.content}>
        {/* Grafik Görüntüsü */}
        <Image src={chartImageDataUrl} style={styles.chartImage} />

        {/* İstatistikler kaldırıldı */}
      </View>

      {/* Alt Bilgi */}
      <View style={styles.footer}>
        <Text style={styles.footerLeft}>{companyName} - Ağ İzleme Sistemi</Text>
        <Text style={styles.footerRight}>Sayfa 1/1</Text>
      </View>
    </Page>
  </Document>
);

export default ChartPDFDocument;
