import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * DNS izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const DNSMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    // Eğer DNS sunucusu değişirse ve özel DNS seçilmezse, özel DNS değerini temizle
    if (name === 'server' && value !== 'custom') {
      onChange({
        [name]: value,
        customDnsServer: ''
      });
    } else {
      onChange({ [name]: value });
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="domain">
          Sorgulanacak Domain <span className="text-destructive">*</span>
        </Label>
        <Input
          id="domain"
          name="domain"
          value={settings.domain || ''}
          onChange={handleInputChange}
          placeholder="Örn: cihazın host adı veya başka bir domain"
          className={errors.domain ? "border-destructive" : ""}
        />
        {errors.domain && (
          <p className="text-xs text-destructive">{errors.domain}</p>
        )}
        <p className="text-xs text-muted-foreground">
          DNS sorgusu yapılacak domain adı (cihazın host adı bir domain ise varsayılan olarak kullanılır, IP adresi ise mutlaka bir domain adı girmelisiniz)
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="server">DNS Sunucusu</Label>
        <Select
          value={settings.server || 'system'}
          onValueChange={(value) => handleSelectChange('server', value)}
        >
          <SelectTrigger id="server">
            <SelectValue placeholder="DNS sunucusu seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">Varsayılan DNS Sunucusu</SelectItem>
            <SelectItem value="system">İşletim Sistemi DNS'i</SelectItem>
            <SelectItem value="*******">Google DNS (*******)</SelectItem>
            <SelectItem value="*******">Cloudflare DNS (*******)</SelectItem>
            <SelectItem value="*******">Quad9 DNS (*******)</SelectItem>
            <SelectItem value="**************">OpenDNS (**************)</SelectItem>
            <SelectItem value="*********">Verisign DNS (*********)</SelectItem>
            <SelectItem value="custom">Özel DNS Sunucusu</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Sorgu yapılacak DNS sunucusu. Varsayılan DNS Sunucusu seçilirse, sistem ayarlarında tanımlanan DNS sunucusu kullanılır. İşletim Sistemi DNS'i seçilirse, sistemin varsayılan DNS sunucusu kullanılır.
        </p>

        {settings.server === 'custom' && (
          <div className="mt-2">
            <Label htmlFor="customDnsServer">Özel DNS Sunucusu</Label>
            <Input
              id="customDnsServer"
              name="customDnsServer"
              value={settings.customDnsServer || ''}
              onChange={handleInputChange}
              placeholder="Örn: *******"
              className={errors.customDnsServer ? "border-destructive" : ""}
            />
            {errors.customDnsServer && (
              <p className="text-xs text-destructive">{errors.customDnsServer}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Özel DNS sunucusunun IP adresi
            </p>
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="timeout">Zaman Aşımı</Label>
        <Select
          value={settings.timeout || '5000'}
          onValueChange={(value) => handleSelectChange('timeout', value)}
        >
          <SelectTrigger id="timeout">
            <SelectValue placeholder="Zaman aşımı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1000">1 saniye</SelectItem>
            <SelectItem value="2000">2 saniye</SelectItem>
            <SelectItem value="5000">5 saniye</SelectItem>
            <SelectItem value="10000">10 saniye</SelectItem>
            <SelectItem value="30000">30 saniye</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          DNS sorgusu için yanıt bekleme süresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '60'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 dakika</SelectItem>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="10">10 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          DNS kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default DNSMonitorSettings;
