import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * HTTP izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const HTTPMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="url">
          URL <span className="text-destructive">*</span>
        </Label>
        <Input
          id="url"
          name="url"
          value={settings.url || ''}
          onChange={handleInputChange}
          placeholder="Örn: https://example.com/status"
          className={errors.url ? "border-destructive" : ""}
        />
        {errors.url && (
          <p className="text-xs text-destructive">{errors.url}</p>
        )}
        <p className="text-xs text-muted-foreground">
          İzlenecek HTTP/HTTPS URL'si
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="method">HTTP Metodu</Label>
        <Select
          value={String(settings.method || 'GET')}
          onValueChange={(value) => handleSelectChange('method', value)}
        >
          <SelectTrigger id="method">
            <SelectValue placeholder="HTTP metodu seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="GET">GET</SelectItem>
            <SelectItem value="POST">POST</SelectItem>
            <SelectItem value="HEAD">HEAD</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          HTTP isteği için kullanılacak metod
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="expectedStatus">Beklenen Durum Kodu</Label>
        <Select
          value={String(settings.expectedStatus || '200')}
          onValueChange={(value) => handleSelectChange('expectedStatus', value)}
        >
          <SelectTrigger id="expectedStatus">
            <SelectValue placeholder="Durum kodu seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="200">200 (OK)</SelectItem>
            <SelectItem value="201">201 (Created)</SelectItem>
            <SelectItem value="204">204 (No Content)</SelectItem>
            <SelectItem value="301">301 (Moved Permanently)</SelectItem>
            <SelectItem value="302">302 (Found)</SelectItem>
            <SelectItem value="any">Herhangi bir başarı kodu</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Başarılı yanıt olarak kabul edilecek HTTP durum kodu
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '5'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 dakika</SelectItem>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="10">10 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          HTTP kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default HTTPMonitorSettings;
