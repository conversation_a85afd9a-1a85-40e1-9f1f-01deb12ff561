import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * SSL izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const SSLMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  // Interval değerini belirle (varsayılan: 60)
  const intervalValue = settings.interval || '60';

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="port">SSL Port</Label>
        <Input
          id="port"
          name="port"
          value={settings.port || '443'}
          onChange={handleInputChange}
          placeholder="Örn: 443"
        />
        <p className="text-xs text-muted-foreground">
          SSL sertifikasının kontrol edileceği port
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={intervalValue}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="60">1 saat</SelectItem>
            <SelectItem value="360">6 saat</SelectItem>
            <SelectItem value="720">12 saat</SelectItem>
            <SelectItem value="1440">24 saat</SelectItem>
            <SelectItem value="10080">7 gün</SelectItem>
            <SelectItem value="43200">30 gün</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          SSL sertifikası kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default SSLMonitorSettings;
