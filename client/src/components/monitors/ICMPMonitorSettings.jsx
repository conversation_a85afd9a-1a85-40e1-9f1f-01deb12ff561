import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * ICMP izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const ICMPMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="retries">Deneme Sayısı</Label>
        <Select
          value={settings.retries || '2'}
          onValueChange={(value) => handleSelectChange('retries', value)}
        >
          <SelectTrigger id="retries">
            <SelectValue placeholder="Deneme sayısı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 deneme</SelectItem>
            <SelectItem value="2">2 deneme</SelectItem>
            <SelectItem value="3">3 deneme</SelectItem>
            <SelectItem value="5">5 deneme</SelectItem>
            <SelectItem value="10">10 deneme</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Yanıt alınamadığında yapılacak deneme sayısı
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="timeout">Zaman Aşımı</Label>
        <Select
          value={settings.timeout || '3000'}
          onValueChange={(value) => handleSelectChange('timeout', value)}
        >
          <SelectTrigger id="timeout">
            <SelectValue placeholder="Zaman aşımı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1000">1 saniye</SelectItem>
            <SelectItem value="2000">2 saniye</SelectItem>
            <SelectItem value="3000">3 saniye</SelectItem>
            <SelectItem value="5000">5 saniye</SelectItem>
            <SelectItem value="10000">10 saniye</SelectItem>
            <SelectItem value="30000">30 saniye</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Yanıt bekleme süresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '5'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 dakika</SelectItem>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="10">10 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          ICMP kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default ICMPMonitorSettings;
