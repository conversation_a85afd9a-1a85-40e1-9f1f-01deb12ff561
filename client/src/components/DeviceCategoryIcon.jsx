import React from 'react';
import { getIconComponent } from '../utils/categoryUtils';

/**
 * Cihaz kategorisi için ikon bileşeni
 * @param {string} category - C<PERSON>az kategorisi
 * @param {string} size - <PERSON><PERSON> boyutu (sm, md, lg)
 * @param {boolean} useInheritedColor - Badge içinde kullanıldığında true olmalı
 * @param {string} className - Ek CSS sınıfları
 */
const DeviceCategoryIcon = ({ category, size = 'sm', useInheritedColor = false, className = '' }) => {
  // Boyut sınıfını belirle
  const sizeClass = size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5';

  // Renk sınıfı (badge içinde kullanıldığında boş olmalı)
  const colorClass = useInheritedColor ? '' : 'text-muted-foreground';

  // İkon sınıfı
  const iconClass = `${sizeClass} ${colorClass} ${className}`;

  // Kategori türüne göre ikon belirle
  let iconName = 'Server'; // Varsayılan ikon

  if (category) {
    if (category.includes('/')) {
      // Alt kategori için ikon
      const [mainCategory, subCategory] = category.split('/');

      // Alt kategori bazlı ikon mapping
      const subCategoryIconMap = {
        // Ağ Cihazları
        'Router': 'Router',
        'Switch': 'Share2',
        'Firewall': 'Shield',
        'AccessPoint': 'Wifi',
        'Modem': 'Radio',

        // Sunucular
        'Fiziksel': 'ServerCrash',
        'Sanal': 'Cloud',
        'Container': 'Package',
        'Veritabanı': 'Database',
        'Depolama': 'HardDrive',

        // Web
        'WebServer': 'Globe2',
        'API': 'Webhook',
        'Mail': 'Mail',
        'CDN': 'Network',
        'DNS': 'Search',

        // IoT
        'Sensör': 'Activity',
        'PLC': 'Cog',
        'BinaSistemi': 'Building2',
        'UPS': 'Battery'
      };

      iconName = subCategoryIconMap[subCategory] || 'Server';
    } else {
      // Ana kategori için ikon
      const mainCategoryIconMap = {
        'Ağ Cihazları': 'Network',
        'Sunucular': 'Server',
        'Web': 'Globe',
        'IoT': 'Cpu'
      };

      iconName = mainCategoryIconMap[category] || 'Server';
    }
  }

  // İkon komponenti al
  const IconComponent = getIconComponent(iconName);

  return <IconComponent className={iconClass} />;
};

export default DeviceCategoryIcon;
