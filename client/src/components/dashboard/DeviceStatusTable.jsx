import React from 'react';
import { useNavigate } from 'react-router-dom';

import { Button } from '../ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '../ui/table';
import { AlertCircle, ExternalLink } from 'lucide-react';
import StatusIndicator from '../StatusIndicator';
import { formatLastCheckTime, getDefaultReasonForStatus } from '../../utils/statusUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';

const DeviceStatusTable = ({ devices, statuses }) => {
  const navigate = useNavigate();

  const getDeviceStatus = (device) => {
    const deviceStatus = statuses[device.id];
    if (!deviceStatus) return 'unknown';

    // Backend'den hesaplanmış durum varsa, onu kullan
    if (deviceStatus.calculatedStatus) {
      return deviceStatus.calculatedStatus;
    }

    // Backend'den henüz hesaplanmış durum gelmemişse 'unknown' döndür
    return 'unknown';
  };



  const getResponseTime = (device) => {
    const deviceStatus = statuses[device.id];
    if (!deviceStatus) return 'N/A';

    // ICMP yanıt süresini öncelikle kullan
    if (deviceStatus.icmp?.responseTime) {
      return `${deviceStatus.icmp.responseTime} ms`;
    }

    // ICMP yoksa HTTP yanıt süresini kullan
    if (deviceStatus.http?.responseTime) {
      return `${deviceStatus.http.responseTime} ms`;
    }

    return 'N/A';
  };

  const getLastChecked = (device) => {
    const deviceStatus = statuses[device.id];
    if (!deviceStatus) return formatLastCheckTime(null);

    // En son kontrol edilen zamanı bul
    let lastCheck = 0;
    const monitorTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl', 'database', 'api', 'snmp', 'smtp', 'windows', 'linux', 'ipmi', 'docker'];

    monitorTypes.forEach(type => {
      if (deviceStatus[type]?.lastCheck) {
        const checkTime = parseInt(deviceStatus[type].lastCheck);
        if (checkTime > lastCheck) {
          lastCheck = checkTime;
        }
      }
    });

    return formatLastCheckTime(lastCheck || null);
  };

  // StatusBadge bileşeni - tooltip ile açıklama gösterir
  const StatusBadge = ({ status, reason }) => {
    const tooltipContent = reason || getDefaultReasonForStatus(status);

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center justify-center">
              <StatusIndicator status={status} type="icon" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipContent}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  if (!devices || devices.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
        <AlertCircle className="h-8 w-8 mb-2" />
        <p>Cihaz bulunamadı</p>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]">Durum</TableHead>
            <TableHead>Cihaz Adı</TableHead>
            <TableHead>IP/Host</TableHead>
            <TableHead>Tür</TableHead>
            <TableHead className="text-right">Yanıt Süresi</TableHead>
            <TableHead className="text-right">Son Kontrol</TableHead>
            <TableHead className="w-[100px] text-right">İşlem</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {devices.map((device) => (
            <TableRow key={device.id}>
              <TableCell>
                <StatusBadge
                  status={getDeviceStatus(device)}
                  reason={statuses[device.id]?.reason}
                />
              </TableCell>
              <TableCell className="font-medium">{device.name}</TableCell>
              <TableCell>{device.host}</TableCell>
              <TableCell>{device.group || 'Varsayılan'}</TableCell>
              <TableCell className="text-right">{getResponseTime(device)}</TableCell>
              <TableCell className="text-right">{getLastChecked(device)}</TableCell>
              <TableCell className="text-right">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => navigate(`/devices/${device.id}`)}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default DeviceStatusTable;
