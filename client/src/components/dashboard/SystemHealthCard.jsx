import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '../ui/card';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Cpu, HardDrive, Database, Wifi, Clock, MemoryStick } from 'lucide-react';
import { useSocket } from '../../contexts/SocketContext';
import { useSystem } from '../../contexts/SystemContext';
import StatusIndicator from '../StatusIndicator';

const SystemHealthCard = () => {
  const { systemHealth } = useSystem();
  const socket = useSocket();
  const getStatusBadge = (status) => {
    return <StatusIndicator status={status} type="badge" size="sm" />;
  };



  // Sistem çalışma süresini formatla
  const formatUptime = (seconds) => {
    if (!seconds) return 'Bilinmiyor';

    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    let result = '';
    if (days > 0) result += `${days} gün `;
    if (hours > 0) result += `${hours} saat `;
    if (minutes > 0) result += `${minutes} dakika`;

    return result || '1 dakikadan az';
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle>Sistem Sağlığı</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* CPU Kullanımı */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Cpu className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">CPU</span>
            </div>
            <span className="text-sm">{systemHealth.cpu?.usage || 0}%</span>
          </div>
          <Progress
            value={systemHealth.cpu?.usage || 0}
            className="h-2"
          />
        </div>

        {/* Bellek Kullanımı */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MemoryStick className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Bellek</span>
            </div>
            <span className="text-sm">{systemHealth.memory?.usage || 0}%</span>
          </div>
          <Progress
            value={systemHealth.memory?.usage || 0}
            className="h-2"
          />
        </div>

        {/* Disk Kullanımı */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Disk</span>
            </div>
            <span className="text-sm">{systemHealth.disk?.usage || 0}%</span>
          </div>
          <Progress
            value={systemHealth.disk?.usage || 0}
            className="h-2"
          />
        </div>

        {/* Sistem Çalışma Süresi */}
        <div className="pt-2 border-t">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Sistem Çalışma Süresi</span>
            </div>
            <span className="text-sm">{formatUptime(systemHealth.uptime)}</span>
          </div>
        </div>

        {/* Socket.io Bağlantı Durumu */}
        <div className="pt-2 border-t">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Wifi className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Gerçek Zamanlı Bağlantı</span>
            </div>
            {socket ? (
              socket.connected ? (
                <StatusIndicator status="up" type="badge" size="sm" />
              ) : (
                <StatusIndicator status="down" type="badge" size="sm" />
              )
            ) : (
              <StatusIndicator status="unknown" type="badge" size="sm" />
            )}
          </div>
        </div>

        {/* Servis Durumları */}
        <div className="pt-2 border-t">
          <h4 className="text-sm font-medium mb-3">Servis Durumları</h4>
          <div className="space-y-2">
            {systemHealth.services && Object.keys(systemHealth.services).map(key => (
              <div key={key} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{systemHealth.services[key].name || key}</span>
                </div>
                {getStatusBadge(systemHealth.services[key].status || 'unknown')}
              </div>
            ))}

            {/* Servis yoksa veya boşsa */}
            {(!systemHealth.services || Object.keys(systemHealth.services).length === 0) && (
              <div className="text-center text-muted-foreground py-2">
                <p className="text-sm">Servis bilgisi bulunamadı</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SystemHealthCard;
