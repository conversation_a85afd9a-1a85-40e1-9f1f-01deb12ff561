import React, { useState, useEffect } from 'react';
import { Badge } from '../../components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { Server, Database, Clock, CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react';
import { useSystem } from '../../contexts/SystemContext';

const SystemStatus = () => {
  const { systemHealth, loading, error } = useSystem();
  const [services, setServices] = useState({});

  // SystemContext'ten gelen verileri kullan
  useEffect(() => {
    if (systemHealth && systemHealth.services) {
      setServices(systemHealth.services);
    }
  }, [systemHealth]);

  const formatUptime = (seconds) => {
    if (!seconds) return 'Bilinmiyor';

    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days} gün ${hours} saat`;
    } else if (hours > 0) {
      return `${hours} saat ${minutes} dakika`;
    } else {
      return `${minutes} dakika`;
    }
  };

  // Durum ikonu (şu anda kullanılmıyor ama ileride kullanılabilir)
  // eslint-disable-next-line no-unused-vars
  const getStatusIcon = (status) => {
    switch (status) {
      case 'up':
        return <CheckCircle className="h-5 w-5 text-success" />;
      case 'down':
        return <XCircle className="h-5 w-5 text-destructive" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-warning" />;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'up':
        return <Badge variant="success">Çalışıyor</Badge>;
      case 'down':
        return <Badge variant="destructive">Çalışmıyor</Badge>;
      default:
        return <Badge variant="warning">Bilinmiyor</Badge>;
    }
  };

  const getServiceIcon = (service) => {
    switch (service) {
      case 'backend':
        return <Server className="h-5 w-5" />;
      case 'redis':
        return <Database className="h-5 w-5" />;
      case 'scheduler':
        return <Clock className="h-5 w-5" />;
      default:
        return <Info className="h-5 w-5" />;
    }
  };

  return (
    <div>
      {error ? (
        <div className="flex flex-col items-center justify-center h-[200px] text-muted-foreground border border-dashed border-muted-foreground/25 rounded-lg bg-muted/10">
          <AlertTriangle className="h-10 w-10 mb-3 text-warning" />
          <p>{error}</p>
        </div>
      ) : loading ? (
        <div className="flex flex-col items-center justify-center h-[200px] text-muted-foreground border border-dashed border-muted-foreground/25 rounded-lg bg-muted/10">
          <p>Yükleniyor...</p>
        </div>
      ) : (
        <div className="space-y-4">
          {Object.keys(services).map((key) => (
            <div key={key} className="flex items-center justify-between p-3 rounded-lg border bg-card">
              <div className="flex items-center space-x-3">
                {getServiceIcon(key)}
                <div>
                  <h3 className="font-medium">{services[key].name}</h3>
                  <p className="text-sm text-muted-foreground">{services[key].description}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="text-xs text-muted-foreground">
                        {key === 'backend' && services[key].uptime && (
                          <span>Çalışma Süresi: {formatUptime(services[key].uptime)}</span>
                        )}
                        {key === 'redis' && services[key].uptime && (
                          <span>Çalışma Süresi: {formatUptime(services[key].uptime)}</span>
                        )}
                        {key === 'scheduler' && services[key].uptime && (
                          <span>Çalışma Süresi: {formatUptime(services[key].uptime)}</span>
                        )}
                        {key === 'scheduler' && !services[key].uptime && services[key].lastRun && (
                          <span>Son Çalışma: {new Date(services[key].lastRun).toLocaleTimeString()}</span>
                        )}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      {key === 'backend' && (
                        <p>Node.js {services[key].version}</p>
                      )}
                      {key === 'redis' && services[key].uptime && (
                        <p>Redis veritabanı {formatUptime(services[key].uptime)} süredir çalışıyor</p>
                      )}
                      {key === 'scheduler' && services[key].uptime && (
                        <p>Zamanlayıcı {formatUptime(services[key].uptime)} süredir çalışıyor</p>
                      )}
                      {key === 'scheduler' && services[key].lastRun && (
                        <p>Son çalışma zamanı: {new Date(services[key].lastRun).toLocaleString()}</p>
                      )}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                {getStatusBadge(services[key].status)}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SystemStatus;
