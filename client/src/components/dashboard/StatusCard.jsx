import React from 'react';
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import StatusIndicator from '../StatusIndicator';
import { STATUS_TYPES } from '../../lib/theme';

const StatusCard = ({ title, value, icon, description, status, className = '' }) => {
  // Durum değerini tema dosyasındaki STATUS_TYPES değerlerine dönüştür
  const mapStatusToStatusType = () => {
    switch (status) {
      case 'success':
        return STATUS_TYPES.UP;
      case 'warning':
        return STATUS_TYPES.WARNING;
      case 'error':
        return STATUS_TYPES.DOWN;
      case 'critical':
        return STATUS_TYPES.CRITICAL;
      case 'degraded':
        return STATUS_TYPES.DEGRADED;
      case 'flapping':
        return STATUS_TYPES.FLAPPING;
      case 'partial':
        return STATUS_TYPES.PARTIAL;
      default:
        return STATUS_TYPES.UNKNOWN;
    }
  };

  // <PERSON><PERSON><PERSON><PERSON>k renklendirme kaldırıldı

  return (
    <Card className={`h-full transition-all hover:shadow-md ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-base font-semibold">
          {title}
        </CardTitle>
        <div className="h-5 w-5">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <div className="flex items-center justify-between mt-2">
          <p className="text-xs text-muted-foreground">{description}</p>
          {status && (
            <div className="ml-auto">
              <StatusIndicator
                status={mapStatusToStatusType()}
                type="badge"
                size="sm"
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default StatusCard;
