import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';

const QuickActionCard = ({ title, description, icon, onClick }) => {
  return (
    <Card className="hover:shadow-md transition-all h-full">
      <CardContent className="p-4">
        <Button
          variant="ghost"
          className="w-full h-auto p-0 flex flex-col items-center justify-center text-center"
          onClick={onClick}
        >
          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-3">
            {icon}
          </div>
          <div>
            <h3 className="font-semibold text-base">{title}</h3>
            <p className="text-sm text-muted-foreground mt-1">{description}</p>
          </div>
        </Button>
      </CardContent>
    </Card>
  );
};

export default QuickActionCard;
