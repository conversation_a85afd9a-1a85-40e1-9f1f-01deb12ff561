import React from 'react';
import { Button } from './button';
import { X } from 'lucide-react';
import { cn } from '../../lib/utils';

const FloatingActionBar = ({ 
  selectedCount, 
  selectedLabel = "öğe", 
  onClearSelection, 
  children, 
  className 
}) => {
  if (selectedCount === 0) return null;

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
      <div className={cn(
        "bg-background border border-border rounded-xl shadow-lg px-6 py-4 flex items-center gap-4 min-w-max",
        "dark:bg-card dark:border-border",
        className
      )}>
        {/* Seçim bilgisi */}
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-foreground">
            {selectedCount} {selectedLabel} seçildi
          </span>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClearSelection}
            className="h-8 w-8 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Dikey ayırıcı */}
        <div className="w-px h-6 bg-border"></div>

        {/* İşlem butonları */}
        <div className="flex items-center gap-2">
          {children}
        </div>
      </div>
    </div>
  );
};

export default FloatingActionBar;
