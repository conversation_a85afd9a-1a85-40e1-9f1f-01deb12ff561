import * as React from "react";
import { X } from "lucide-react";
import { cn } from "../../lib/utils";

const Dialog = ({ children, open, onOpenChange }) => {
  const [isVisible, setIsVisible] = React.useState(open);
  const [isClosing, setIsClosing] = React.useState(false);

  React.useEffect(() => {
    if (open) {
      setIsVisible(true);
      setIsClosing(false);
    } else if (isVisible) {
      setIsClosing(true);
      // Animasyon tamamlandıktan sonra dialog'u gizle
      setTimeout(() => {
        setIsVisible(false);
        setIsClosing(false);
      }, 200); // duration-200 ile eşleşiyor
    }
  }, [open, isVisible]);

  const handleClose = () => {
    if (onOpenChange) {
      onOpenChange(false);
    }
  };

  if (!isVisible) return null;

  // Çocuk bileşenlere animasyon state'ini geçir
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { onOpenChange, isOpening: open && !isClosing, isClosing });
    }
    return child;
  });

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className={cn(
          "fixed inset-0 bg-background/80 backdrop-blur-sm transition-opacity duration-200",
          open && !isClosing ? "opacity-100" : "opacity-0"
        )}
        onClick={handleClose}
      />
      <div className="z-50">{childrenWithProps}</div>
    </div>
  );
};

const DialogContent = React.forwardRef(({ className, children, onEscapeKeyDown, ...props }, ref) => {
  // Dialog'un üst bileşenine erişim
  const handleClose = () => {
    // Dialog'un üst bileşenindeki onOpenChange fonksiyonunu çağır
    if (props.onOpenChange) {
      props.onOpenChange(false);
    }
    // onEscapeKeyDown prop'u varsa çağır
    if (onEscapeKeyDown) {
      onEscapeKeyDown();
    }
  };

  // ESC tuşuna basıldığında dialog'u kapat
  React.useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Custom prop'ları temizle (DOM'a geçirilmemeli)
  const cleanProps = { ...props };
  delete cleanProps.onEscapeKeyDown;
  delete cleanProps.onOpenChange;
  delete cleanProps.isOpening;
  delete cleanProps.isClosing;

  return (
    <div
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg transition-all duration-200 ease-out sm:rounded-lg",
        // Yukarıdan slide animasyonu
        props.isOpening ? "animate-in fade-in-0 slide-in-from-top-4" :
        props.isClosing ? "animate-out fade-out-0 slide-out-to-top-4" : "",
        className
      )}
      {...cleanProps}
    >
      {children}
      <button
        className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        onClick={handleClose}
        type="button"
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </button>
    </div>
  );
});
DialogContent.displayName = "DialogContent";

const DialogHeader = ({ className, ...props }) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
);
DialogHeader.displayName = "DialogHeader";

const DialogFooter = ({ className, ...props }) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
);
DialogFooter.displayName = "DialogFooter";

const DialogTitle = React.forwardRef(({ className, children, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  >
    {children}
  </h3>
));
DialogTitle.displayName = "DialogTitle";

const DialogDescription = React.forwardRef(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
DialogDescription.displayName = "DialogDescription";

export {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
};
