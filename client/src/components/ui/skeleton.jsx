import React from 'react';

/**
 * Temel Skeleton Component
 * Loading durumlarında placeholder gösterir
 */
const Skeleton = ({ 
  className = '',
  width = 'w-full',
  height = 'h-4',
  rounded = 'rounded',
  animate = true,
  ...props 
}) => {
  const animationClass = animate ? 'animate-pulse' : '';
  
  return (
    <div 
      className={`bg-muted ${width} ${height} ${rounded} ${animationClass} ${className}`}
      {...props}
    />
  );
};

/**
 * Tablo Skeleton Component
 * Tablo loading durumları için
 */
const TableSkeleton = ({ 
  rows = 5, 
  columns = 4,
  showHeader = true 
}) => {
  return (
    <div className="space-y-3">
      {/* Header Skeleton */}
      {showHeader && (
        <div className="flex space-x-4 pb-2 border-b">
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton 
              key={`header-${index}`}
              width="flex-1" 
              height="h-4" 
              className="bg-muted/70"
            />
          ))}
        </div>
      )}
      
      {/* Rows Skeleton */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={`row-${rowIndex}`} className="flex space-x-4 py-2">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton 
              key={`cell-${rowIndex}-${colIndex}`}
              width="flex-1" 
              height="h-4"
            />
          ))}
        </div>
      ))}
    </div>
  );
};

/**
 * Kart Skeleton Component
 * Kart loading durumları için
 */
const CardSkeleton = ({ 
  showHeader = true,
  showContent = true,
  showFooter = false,
  className = ''
}) => {
  return (
    <div className={`border rounded-lg p-6 space-y-4 ${className}`}>
      {/* Header */}
      {showHeader && (
        <div className="space-y-2">
          <Skeleton width="w-1/3" height="h-6" />
          <Skeleton width="w-2/3" height="h-4" className="bg-muted/70" />
        </div>
      )}
      
      {/* Content */}
      {showContent && (
        <div className="space-y-3">
          <Skeleton width="w-full" height="h-4" />
          <Skeleton width="w-5/6" height="h-4" />
          <Skeleton width="w-4/6" height="h-4" />
        </div>
      )}
      
      {/* Footer */}
      {showFooter && (
        <div className="flex justify-between items-center pt-4">
          <Skeleton width="w-20" height="h-8" rounded="rounded-md" />
          <Skeleton width="w-16" height="h-8" rounded="rounded-md" />
        </div>
      )}
    </div>
  );
};

/**
 * Form Skeleton Component
 * Form loading durumları için
 */
const FormSkeleton = ({ 
  fields = 3,
  showButtons = true,
  className = ''
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Form Fields */}
      {Array.from({ length: fields }).map((_, index) => (
        <div key={`field-${index}`} className="space-y-2">
          <Skeleton width="w-24" height="h-4" className="bg-muted/70" />
          <Skeleton width="w-full" height="h-10" rounded="rounded-md" />
        </div>
      ))}
      
      {/* Buttons */}
      {showButtons && (
        <div className="flex justify-end space-x-2 pt-4">
          <Skeleton width="w-20" height="h-10" rounded="rounded-md" />
          <Skeleton width="w-24" height="h-10" rounded="rounded-md" />
        </div>
      )}
    </div>
  );
};

/**
 * List Skeleton Component
 * Liste loading durumları için
 */
const ListSkeleton = ({ 
  items = 5,
  showAvatar = false,
  className = ''
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={`item-${index}`} className="flex items-center space-x-3 p-3 border rounded-lg">
          {/* Avatar */}
          {showAvatar && (
            <Skeleton width="w-10" height="h-10" rounded="rounded-full" />
          )}
          
          {/* Content */}
          <div className="flex-1 space-y-2">
            <Skeleton width="w-1/3" height="h-4" />
            <Skeleton width="w-2/3" height="h-3" className="bg-muted/70" />
          </div>
          
          {/* Action */}
          <Skeleton width="w-8" height="h-8" rounded="rounded-md" />
        </div>
      ))}
    </div>
  );
};

/**
 * Stats Skeleton Component
 * İstatistik kartları için
 */
const StatsSkeleton = ({ 
  cards = 4,
  className = ''
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {Array.from({ length: cards }).map((_, index) => (
        <div key={`stat-${index}`} className="border rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton width="w-16" height="h-4" className="bg-muted/70" />
              <Skeleton width="w-12" height="h-8" />
            </div>
            <Skeleton width="w-8" height="h-8" rounded="rounded-md" />
          </div>
        </div>
      ))}
    </div>
  );
};

// Export all components
export {
  Skeleton,
  TableSkeleton,
  CardSkeleton,
  FormSkeleton,
  ListSkeleton,
  StatsSkeleton
};

export default Skeleton;
