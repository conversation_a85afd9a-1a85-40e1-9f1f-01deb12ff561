import * as React from "react";
import { cva } from "class-variance-authority";
import { cn } from "../../lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80 dark:bg-primary/90 dark:hover:bg-primary/70",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 dark:bg-secondary/90 dark:hover:bg-secondary/70",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80 dark:bg-destructive/90 dark:hover:bg-destructive/70",
        outline: "text-foreground dark:text-foreground/90",
        success:
          "border-transparent bg-success text-success-foreground shadow hover:bg-success/80 dark:bg-green-600 dark:hover:bg-green-700 dark:text-white",
        warning:
          "border-transparent bg-warning text-warning-foreground shadow hover:bg-warning/80 dark:bg-yellow-600 dark:hover:bg-yellow-700 dark:text-white",
        critical:
          "border-transparent bg-orange-500 dark:bg-orange-600 text-white shadow hover:bg-orange-600 dark:hover:bg-orange-700",
        degraded:
          "border-transparent bg-purple-500 dark:bg-purple-600 text-white shadow hover:bg-purple-600 dark:hover:bg-purple-700",
        flapping:
          "border-transparent bg-pink-500 dark:bg-pink-600 text-white shadow hover:bg-pink-600 dark:hover:bg-pink-700",
        partial:
          "border-transparent bg-blue-500 dark:bg-blue-600 text-white shadow hover:bg-blue-600 dark:hover:bg-blue-700",
        unknown:
          "border-transparent bg-gray-500 dark:bg-gray-600 text-white shadow hover:bg-gray-600 dark:hover:bg-gray-700",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const Badge = React.forwardRef(({ className, variant, ...props }, ref) => {
  return (
    <div ref={ref} className={cn(badgeVariants({ variant }), className)} {...props} />
  );
});

Badge.displayName = "Badge";

export { Badge, badgeVariants };
