import * as React from "react";
import { ChevronRight } from "lucide-react";
import { cn } from "../../lib/utils";
import { Link } from "react-router-dom";

const Breadcrumb = React.forwardRef(
  ({ className, ...props }, ref) => {
    return (
      <nav
        ref={ref}
        aria-label="breadcrumb"
        className={cn("flex items-center text-sm", className)}
        {...props}
      />
    );
  }
);
Breadcrumb.displayName = "Breadcrumb";

const BreadcrumbList = React.forwardRef(
  ({ className, ...props }, ref) => {
    return (
      <ol
        ref={ref}
        className={cn("flex items-center gap-1.5", className)}
        {...props}
      />
    );
  }
);
BreadcrumbList.displayName = "BreadcrumbList";

const BreadcrumbItem = React.forwardRef(
  ({ className, ...props }, ref) => {
    return (
      <li
        ref={ref}
        className={cn("flex items-center gap-1.5", className)}
        {...props}
      />
    );
  }
);
BreadcrumbItem.displayName = "BreadcrumbItem";

const BreadcrumbSeparator = React.forwardRef(
  ({ children, className, ...props }, ref) => {
    return (
      <li
        ref={ref}
        className={cn("flex items-center text-muted-foreground", className)}
        {...props}
      >
        {children || <ChevronRight className="h-3 w-3" />}
      </li>
    );
  }
);
BreadcrumbSeparator.displayName = "BreadcrumbSeparator";

const BreadcrumbLink = React.forwardRef(
  ({ className, asChild = false, to, ...props }, ref) => {
    return (
      <Link
        ref={ref}
        to={to}
        className={cn("text-muted-foreground hover:text-foreground transition-colors", className)}
        {...props}
      />
    );
  }
);
BreadcrumbLink.displayName = "BreadcrumbLink";

const BreadcrumbPage = React.forwardRef(
  ({ className, ...props }, ref) => {
    return (
      <span
        ref={ref}
        className={cn("font-medium text-foreground", className)}
        aria-current="page"
        {...props}
      />
    );
  }
);
BreadcrumbPage.displayName = "BreadcrumbPage";

export {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbSeparator,
  BreadcrumbLink,
  BreadcrumbPage,
};
