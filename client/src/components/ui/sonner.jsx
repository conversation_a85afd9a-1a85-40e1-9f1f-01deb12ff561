import { useTheme } from "../../contexts/ThemeContext"
import { Toaster as Sonner } from "sonner"
import { useEffect } from "react"

const Toaster = ({
  ...props
}) => {
  const { theme } = useTheme()

  // Toast stillerini özelleştirmek için CSS ekle
  useEffect(() => {
    // Özel stil ekle
    const styleEl = document.createElement('style')
    styleEl.textContent = `
      /* Toast container'ı düzenle - minimal stil */
      [data-sonner-toast] {
        padding: 0.75rem 1rem !important;
      }
    `
    document.head.appendChild(styleEl)

    // Temizlik fonksiyonu
    return () => {
      document.head.removeChild(styleEl)
    }
  }, [])

  return (
    <Sonner
      theme={theme === 'dark' ? 'dark' : 'light'}
      className="toaster group"
      position={props.position || "bottom-right"} // Varsayılan konum sağ alt köşe
      expand={false} // Bildirimleri genişletme
      richColors={true} // Zengin renkler kullan
      closeButton={false} // Kapatma düğmesi gösterme
      offset="1.5rem" // Ekran kenarından uzaklık
      // Swipe ayarlarını kaldırdık - Sonner varsayılanları kullanacak
      toastOptions={{
        duration: 5000, // Varsayılan gösterim süresi
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
        },
      }}
      {...props}
    />
  )
}

export { Toaster }
