import React from 'react';
import { RefreshCw } from 'lucide-react';

/**
 * Merkezi Loading Component
 * Tüm uygulama genelinde tutarlı loading göstergeleri için
 */
const Loading = ({ 
  size = 'md',           // sm, md, lg, xl
  type = 'page',         // page, inline, table, fullscreen
  message = 'Yükleniyor...',
  description = null,
  className = ''
}) => {
  // İkon boyutları
  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8', 
    lg: 'h-10 w-10',
    xl: 'h-12 w-12'
  };

  // Metin boyutları
  const textSizes = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl'
  };

  // Container stilleri
  const containers = {
    page: 'flex flex-col items-center justify-center h-[calc(100vh-4rem)]',
    inline: 'flex flex-col items-center justify-center p-8',
    table: 'flex flex-col items-center justify-center h-24',
    fullscreen: 'flex items-center justify-center min-h-screen bg-background'
  };

  return (
    <div className={`${containers[type]} ${className}`}>
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <RefreshCw className={`${sizes[size]} animate-spin text-primary`} />
        </div>
        <p className={`${textSizes[size]} font-medium`}>{message}</p>
        {description && (
          <p className="text-sm text-muted-foreground mt-2">{description}</p>
        )}
      </div>
    </div>
  );
};

export default Loading;
