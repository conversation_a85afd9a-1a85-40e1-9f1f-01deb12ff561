import React from 'react';
import Loading from './loading';
import { 
  Skeleton, 
  TableSkeleton, 
  CardSkeleton, 
  FormSkeleton, 
  StatsSkeleton 
} from './skeleton';

/**
 * Smart Loading Component
 * Loading durumuna göre otomatik olarak doğru loading component'ini gösterir
 */
const SmartLoading = ({
  loadingType,
  skeletonType = 'card',
  skeletonProps = {},
  loadingProps = {},
  children,
  className = ''
}) => {
  // Loading tipine göre component seç
  const renderLoading = () => {
    switch (loadingType) {
      case 'initial':
        return (
          <Loading
            type="page"
            size="lg"
            message="Yükleniyor..."
            {...loadingProps}
          />
        );
        
      case 'refresh':
      case 'loading':
        return renderSkeleton();
        
      case 'inline':
        return (
          <Loading
            type="inline"
            size="md"
            message="Yükleniyor..."
            {...loadingProps}
          />
        );
        
      default:
        return children;
    }
  };

  // Skeleton tipine göre component seç
  const renderSkeleton = () => {
    switch (skeletonType) {
      case 'table':
        return <TableSkeleton {...skeletonProps} />;
        
      case 'card':
        return <CardSkeleton {...skeletonProps} />;
        
      case 'form':
        return <FormSkeleton {...skeletonProps} />;
        
      case 'stats':
        return <StatsSkeleton {...skeletonProps} />;
        
      case 'custom':
        return skeletonProps.render ? skeletonProps.render() : <Skeleton {...skeletonProps} />;
        
      default:
        return <CardSkeleton {...skeletonProps} />;
    }
  };

  return (
    <div className={className}>
      {renderLoading()}
    </div>
  );
};

/**
 * Smart Table Loading Component
 * Tablo için özelleştirilmiş smart loading
 */
export const SmartTableLoading = ({
  loadingType,
  data = [],
  columns = 4,
  rows = 5,
  children,
  emptyMessage = "Veri bulunamadı",
  className = ''
}) => {
  if (loadingType === 'initial') {
    return (
      <Loading
        type="page"
        size="lg"
        message="Veriler yükleniyor..."
        description="Lütfen bekleyin..."
      />
    );
  }

  if (loadingType === 'refresh' || loadingType === 'loading') {
    return (
      <div className={className}>
        {/* Tablo skeleton'ı burada render edilecek */}
        {children}
      </div>
    );
  }

  if (loadingType === 'idle' && data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-32 text-center">
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }

  return <div className={className}>{children}</div>;
};

/**
 * Smart Page Loading Component
 * Sayfa için özelleştirilmiş smart loading
 */
export const SmartPageLoading = ({
  loadingType,
  pageTitle = "Sayfa",
  skeletonLayout = 'default',
  children,
  className = ''
}) => {
  if (loadingType === 'initial') {
    return (
      <Loading
        type="page"
        size="lg"
        message={`${pageTitle} yükleniyor...`}
        description="Veriler getiriliyor, lütfen bekleyin..."
      />
    );
  }

  if (loadingType === 'refresh' || loadingType === 'loading') {
    return (
      <div className={`p-6 space-y-6 ${className}`}>
        {renderPageSkeleton(skeletonLayout)}
      </div>
    );
  }

  return <div className={className}>{children}</div>;
};

// Sayfa skeleton layout'ları
const renderPageSkeleton = (layout) => {
  switch (layout) {
    case 'dashboard':
      return (
        <>
          {/* Başlık Skeleton */}
          <div className="space-y-2">
            <Skeleton width="w-48" height="h-8" />
            <Skeleton width="w-96" height="h-4" />
          </div>
          
          {/* Stats Skeleton */}
          <StatsSkeleton cards={4} />
          
          {/* Grafik Kartları */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <CardSkeleton showHeader={true} showContent={true} className="h-[400px]" />
            <CardSkeleton showHeader={true} showContent={true} className="h-[400px]" />
          </div>
        </>
      );
      
    case 'table':
      return (
        <>
          {/* Başlık ve Butonlar */}
          <div className="flex justify-between items-center">
            <div className="space-y-2">
              <Skeleton width="w-48" height="h-8" />
              <Skeleton width="w-96" height="h-4" />
            </div>
            <Skeleton width="w-32" height="h-9" rounded="rounded-md" />
          </div>
          
          {/* Tablo Kartı */}
          <CardSkeleton showHeader={true} showContent={true} className="h-[600px]" />
        </>
      );
      
    case 'detail':
      return (
        <>
          {/* Başlık ve Butonlar */}
          <div className="flex justify-between items-center">
            <Skeleton width="w-64" height="h-8" />
            <div className="flex gap-2">
              <Skeleton width="w-20" height="h-9" rounded="rounded-md" />
              <Skeleton width="w-24" height="h-9" rounded="rounded-md" />
            </div>
          </div>
          
          {/* İçerik Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="space-y-6">
              <CardSkeleton showHeader={true} showContent={true} className="h-64" />
              <CardSkeleton showHeader={true} showContent={true} className="h-80" />
            </div>
            <div className="lg:col-span-2 space-y-6">
              <CardSkeleton showHeader={true} showContent={true} className="h-96" />
            </div>
          </div>
        </>
      );
      
    default:
      return (
        <>
          <div className="space-y-2">
            <Skeleton width="w-48" height="h-8" />
            <Skeleton width="w-96" height="h-4" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <CardSkeleton showHeader={true} showContent={true} />
            <CardSkeleton showHeader={true} showContent={true} />
          </div>
        </>
      );
  }
};

export default SmartLoading;
