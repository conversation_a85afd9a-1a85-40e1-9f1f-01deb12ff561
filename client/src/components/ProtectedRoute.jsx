import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

/**
 * Korumalı rota bileşeni
 * Kimlik doğrulama gerektiren sayfalar için kullanılır
 */
const ProtectedRoute = ({ children, adminOnly = false }) => {
  const { isAuthenticated, isAdmin, loading } = useAuth();
  const location = useLocation();

  // Kimlik doğrulama durumu yükleniyorsa bekle
  if (loading) {
    return <div>Yükleniyor...</div>;
  }

  // Kullanıcı giriş yapmamışsa login sayfasına yönlendir
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Sadece admin erişimi gerektiren sayfa ve kullanıcı admin değilse ana sayfaya yönlendir
  if (adminOnly && !isAdmin) {
    return <Navigate to="/" replace />;
  }

  // Kimlik doğrulama başarılıysa içeriği göster
  return children;
};

export default ProtectedRoute;
