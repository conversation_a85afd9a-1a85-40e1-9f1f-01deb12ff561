import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from './ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, List } from 'lucide-react';

/**
 * Merkezi sayfalandırma bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {number} props.currentPage - Mevcut sayfa numarası
 * @param {number} props.totalPages - Toplam sayfa sayısı
 * @param {number} props.totalItems - Toplam öğe sayısı
 * @param {Function} props.onPageChange - Sayfa değiştiğinde çağrılacak fonksiyon
 * @param {number} props.pageSize - <PERSON><PERSON> başına öğe sayısı
 * @param {Function} props.onPageSizeChange - Sayfa başına öğe sayısı değiştiğinde çağrılacak fonksiyon
 * @param {Array} props.pageSizeOptions - Sayfa başına öğe sayısı seçenekleri
 * @param {boolean} props.showPageSizeOptions - Sayfa başına öğe sayısı seçeneklerini göster/gizle
 * @returns {JSX.Element} Sayfalandırma bileşeni
 */
const PaginationComponent = ({
  currentPage,
  totalPages,
  totalItems,
  currentPageItems,
  onPageChange,
  pageSize = 10,
  onPageSizeChange,
  pageSizeOptions = [5, 10, 25, 50, 100],
  showPageSizeOptions = true
}) => {
  // Toplam sayfa sayısı 1 veya daha az ise ve sayfa boyutu seçeneği gösterilmiyorsa null döndür
  if (totalPages <= 1 && !showPageSizeOptions) return null;

  // İlk ve son öğe indeksleri
  const indexOfFirstItem = (currentPage - 1) * pageSize + 1;
  // Eğer mevcut sayfadaki öğe sayısı verilmişse, onu da dikkate al
  const actualLastItem = currentPageItems !== undefined
    ? (currentPage - 1) * pageSize + currentPageItems
    : Math.min(currentPage * pageSize, totalItems);
  const indexOfLastItem = Math.min(actualLastItem, totalItems);

  // Sayfa numaralarını hesapla
  const getPageNumbers = () => {
    const pageNumbers = [];

    if (totalPages <= 7) {
      // 7 veya daha az sayfa varsa, tüm sayfaları göster
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // 7'den fazla sayfa varsa, akıllı sayfalama yap
      if (currentPage <= 4) {
        // İlk sayfalar
        for (let i = 1; i <= 5; i++) {
          pageNumbers.push(i);
        }
        pageNumbers.push('ellipsis');
        pageNumbers.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // Son sayfalar
        pageNumbers.push(1);
        pageNumbers.push('ellipsis');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        // Orta sayfalar
        pageNumbers.push(1);
        pageNumbers.push('ellipsis');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pageNumbers.push(i);
        }
        pageNumbers.push('ellipsis');
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers;
  };

  return (
    <div className="py-4">
      <div className="flex flex-col md:flex-row items-center justify-between gap-4">
        {/* Sol taraf: Öğe gösterim bilgisi */}
        {totalItems > 0 && (
          <div className="text-sm text-muted-foreground order-3 md:order-1">
            {indexOfFirstItem}-{indexOfLastItem} / {totalItems} öğe gösteriliyor
          </div>
        )}

        {/* Orta kısım: Sayfalama kontrolleri */}
        <div className="order-1 md:order-2">
          <Pagination>
            <PaginationContent>
              {/* İlk sayfa */}
              <PaginationItem>
                <PaginationLink
                  onClick={() => onPageChange(1)}
                  isActive={false}
                  className="cursor-pointer"
                  disabled={currentPage === 1}
                >
                  <ChevronsLeft className="h-4 w-4" />
                </PaginationLink>
              </PaginationItem>

              {/* Önceki sayfa */}
              <PaginationItem>
                <PaginationLink
                  onClick={() => onPageChange(currentPage > 1 ? currentPage - 1 : 1)}
                  isActive={false}
                  className="cursor-pointer"
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </PaginationLink>
              </PaginationItem>

              {/* Sayfa numaraları */}
              {getPageNumbers().map((pageNumber, index) => (
                <PaginationItem key={index} className="hidden sm:inline-flex">
                  {pageNumber === 'ellipsis' ? (
                    <PaginationEllipsis />
                  ) : (
                    <PaginationLink
                      onClick={() => onPageChange(pageNumber)}
                      isActive={currentPage === pageNumber}
                      className="cursor-pointer"
                    >
                      {pageNumber}
                    </PaginationLink>
                  )}
                </PaginationItem>
              ))}

              {/* Sonraki sayfa */}
              <PaginationItem>
                <PaginationLink
                  onClick={() => onPageChange(currentPage < totalPages ? currentPage + 1 : totalPages)}
                  isActive={false}
                  className="cursor-pointer"
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </PaginationLink>
              </PaginationItem>

              {/* Son sayfa */}
              <PaginationItem>
                <PaginationLink
                  onClick={() => onPageChange(totalPages)}
                  isActive={false}
                  className="cursor-pointer"
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className="h-4 w-4" />
                </PaginationLink>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>

        {/* Sağ taraf: Sayfa başına öğe sayısı seçeneği */}
        {showPageSizeOptions && onPageSizeChange && (
          <div className="flex items-center gap-2 text-sm order-2 md:order-3">
            <div className="flex items-center gap-2">
              <List className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Sayfa başına:</span>
            </div>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => {
                const newPageSize = parseInt(value, 10);
                onPageSizeChange(newPageSize);
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={pageSize} />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map((option) => (
                  <SelectItem key={option} value={option.toString()}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaginationComponent;
