import React from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

/**
 * React Error Boundary Component
 * React component hatalarını yakalar ve kullanıcı dostu hata ekranı gösterir
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Hata oluştuğunda state'i güncelle
    return { 
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Hata detaylarını state'e kaydet
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Hata loglaması
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Production'da hata raporlama servisi kullanılabilir
    if (process.env.NODE_ENV === 'production') {
      // Örnek: Sentry, LogRocket, vb.
      // errorReportingService.captureException(error, { extra: errorInfo });
    }
  }

  handleReload = () => {
    // Sayfayı yenile
    window.location.reload();
  };

  handleGoHome = () => {
    // Ana sayfaya git
    window.location.href = '/';
  };

  handleReset = () => {
    // Error boundary'yi sıfırla
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    });
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, errorId } = this.state;
      const isDevelopment = process.env.NODE_ENV === 'development';

      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-destructive/10 flex items-center justify-center">
                <AlertTriangle className="h-8 w-8 text-destructive" />
              </div>
              <CardTitle className="text-2xl text-destructive">
                Bir Hata Oluştu
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center text-muted-foreground">
                <p className="text-lg mb-2">
                  Üzgünüz, beklenmeyen bir hata oluştu.
                </p>
                <p className="text-sm">
                  Lütfen sayfayı yenileyin veya ana sayfaya dönün.
                </p>
                {errorId && (
                  <p className="text-xs mt-2 font-mono bg-muted p-2 rounded">
                    Hata ID: {errorId}
                  </p>
                )}
              </div>

              {/* Geliştirme ortamında hata detayları */}
              {isDevelopment && error && (
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-semibold mb-2 text-sm">Hata Detayları (Geliştirme):</h4>
                  <div className="text-xs font-mono space-y-2">
                    <div>
                      <strong>Hata:</strong>
                      <pre className="mt-1 whitespace-pre-wrap text-destructive">
                        {error.toString()}
                      </pre>
                    </div>
                    {errorInfo && errorInfo.componentStack && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 whitespace-pre-wrap text-muted-foreground">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Aksiyon butonları */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button onClick={this.handleReload} className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Sayfayı Yenile
                </Button>
                <Button onClick={this.handleGoHome} variant="outline" className="flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Ana Sayfaya Dön
                </Button>
                {isDevelopment && (
                  <Button onClick={this.handleReset} variant="secondary" className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Hatayı Sıfırla
                  </Button>
                )}
              </div>

              {/* Yardım metni */}
              <div className="text-center text-xs text-muted-foreground border-t pt-4">
                <p>
                  Bu hata devam ederse, lütfen sistem yöneticisi ile iletişime geçin.
                </p>
                {isDevelopment && (
                  <p className="mt-1">
                    Geliştirme ortamında çalışıyorsunuz. Hata detayları yukarıda gösterilmektedir.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    // Hata yoksa children'ı render et
    return this.props.children;
  }
}

/**
 * HOC (Higher Order Component) - Component'leri Error Boundary ile sarar
 */
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

/**
 * Hook - Functional component'lerde error boundary kullanımı için
 */
export const useErrorHandler = () => {
  return (error, errorInfo) => {
    console.error('Manual error caught:', error, errorInfo);
    
    // Production'da hata raporlama
    if (process.env.NODE_ENV === 'production') {
      // errorReportingService.captureException(error, { extra: errorInfo });
    }
    
    // Hata fırlatarak Error Boundary'nin yakalamasını sağla
    throw error;
  };
};

export default ErrorBoundary;
