import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import {
  Activity,
  AlertCircle,
  Battery,
  Building2,
  Cloud,
  Cog,
  Cpu,
  Database,
  Globe,
  Globe2,
  HardDrive,
  Mail,
  Monitor,
  Network,
  Package,
  Plus,
  Radio,
  RefreshCw,
  Router,
  Search,
  Server,
  ServerCrash,
  Share2,
  Shield,
  Terminal,
  Webhook,
  Wifi
} from 'lucide-react';
import { deviceService, settingsService, categoryService } from '../services/api';
import { getFormattedSubCategory } from '../utils/categoryUtils';
import { notificationService } from '../services/notification-service';

/**
 * <PERSON>ni cihaz ekleme dialog bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.open - Dialog açık mı?
 * @param {Function} props.onOpenChange - Dialog açık/kapalı durumu değiştiğinde çağrılacak fonksiyon
 * @param {Function} props.onSuccess - Cihaz başarıyla eklendiğinde çağrılacak fonksiyon
 */
const DeviceAddDialog = ({ open, onOpenChange, onSuccess }) => {
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    host: '',
    group: '',
    platform: 'linux', // Varsayılan platform
    description: '',
    location: ''
  });

  // Form validation
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Platform selection is now optional for all categories
  const needsPlatformSelection = (category) => {
    return false; // Platform selection removed - categories are purely organizational
  };

  // Platform seçeneklerini getir
  const getPlatformOptions = () => {
    return [
      { value: 'windows', label: 'Windows Server', icon: Monitor },
      { value: 'linux', label: 'Linux Server', icon: Terminal }
    ];
  };

  // İkon string'ini React component'ine çevir
  const getIconComponent = (iconName) => {
    const iconMap = {
      Network, Router, Share2, Shield, Wifi, Radio,
      Server, ServerCrash, Cloud, Package, Database, HardDrive,
      Globe, Globe2, Webhook, Mail, Search,
      Cpu, Activity, Cog, Building2, Battery,
      Monitor, Terminal
    };
    return iconMap[iconName] || Network;
  };

  // Renk mapping
  const getColorClass = (color, isMain = false) => {
    const colorMap = {
      blue: isMain ? 'text-blue-500' : 'text-blue-400',
      green: isMain ? 'text-green-500' : 'text-green-400',
      purple: isMain ? 'text-purple-500' : 'text-purple-400',
      orange: isMain ? 'text-orange-500' : 'text-orange-400'
    };
    return colorMap[color] || 'text-gray-500';
  };
  const [error, setError] = useState(null);

  // Varsayılan ayarları saklamak için state
  const [defaultSettings, setDefaultSettings] = useState({
    defaultPingInterval: '5',
    defaultHttpInterval: '10',
    defaultDnsInterval: '10',
    defaultSslInterval: '60',
    defaultTcpInterval: '5',
    defaultDnsServer: '8.8.8.8'
  });

  // Kategori verileri için state
  const [categories, setCategories] = useState(null);
  const [platformRequiredCategories, setPlatformRequiredCategories] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(true);

  // Ayarları ve kategorileri yükle
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingCategories(true);

        // Ayarları ve kategorileri paralel olarak yükle
        const [settings, categoryData, platformData] = await Promise.all([
          settingsService.getAll(),
          categoryService.getAll(),
          categoryService.getPlatformRequired()
        ]);

        // Ayarları güncelle (sadece lean monitoring için gerekli olanlar)
        setDefaultSettings({
          defaultPingInterval: settings.defaultPingInterval || '5',
          defaultHttpInterval: settings.defaultHttpInterval || '10',
          defaultDnsInterval: settings.defaultDnsInterval || '10',
          defaultSslInterval: settings.defaultSslInterval || '60',
          defaultTcpInterval: settings.defaultTcpInterval || '5',
          defaultDnsServer: settings.defaultDnsServer || '8.8.8.8'
        });

        // Kategori verilerini güncelle
        setCategories(categoryData.categories);
        setPlatformRequiredCategories(platformData.categories);

        console.log('Kategoriler yüklendi:', categoryData.metadata);
      } catch (err) {
        console.error('Veriler yüklenirken hata oluştu:', err);
        setError('Kategori verileri yüklenirken bir hata oluştu');
      } finally {
        setLoadingCategories(false);
      }
    };

    loadData();
  }, []);

  // Form değişikliklerini işle
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Hata mesajını temizle
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    const newFormData = {
      ...formData,
      [name]: value
    };

    // Kategori değiştiğinde platform'u sıfırla
    if (name === 'group') {
      newFormData.platform = '';
    }

    setFormData(newFormData);

    // Hata mesajını temizle
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  // Formu doğrula
  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Cihaz adı gereklidir';
    }

    if (!formData.host.trim()) {
      errors.host = 'Host adresi gereklidir';
    } else if (!/^[a-zA-Z0-9]([a-zA-Z0-9\-\.]+)?[a-zA-Z0-9]$|^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(formData.host)) {
      errors.host = 'Geçerli bir host adı veya IP adresi girin';
    }

    if (!formData.group || formData.group.trim() === '') {
      errors.group = 'Kategori seçimi zorunludur';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Formu gönder
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Cihaz verisi oluştur
      const deviceData = {
        name: formData.name,
        host: formData.host,
        group: formData.group,
        platform: formData.platform, // Platform bilgisini kaydet
        description: formData.description,
        location: formData.location,
        monitors: {
          icmp: {
            enabled: true,
            method: 'icmp',
            interval: defaultSettings.defaultPingInterval
          }
        }
      };

      // Kategori bazlı varsayılan izleyicileri ekle (Lean Monitoring - sadece 5 tür)
      const category = formData.group.split('/')[0];
      const subCategory = formData.group.split('/')[1];

      // Web servisleri için özel izleyici atamaları
      if (category === 'Web') {
        // Tüm web servisleri için HTTP monitoring
        deviceData.monitors.http = {
          enabled: true,
          method: 'GET',
          expectedStatus: 'any',
          interval: defaultSettings.defaultHttpInterval,
          url: `http://${formData.host}` // Varsayılan URL
        };

        // Alt kategori bazlı özel atamalar
        switch (subCategory) {
          case 'WebServer':
            // Web sunucuları için SSL monitoring (HTTPS için)
            deviceData.monitors.ssl = {
              enabled: false, // Kullanıcı HTTPS varsa etkinleştirecek
              interval: defaultSettings.defaultSslInterval,
              port: 443,
              host: formData.host
            };
            break;

          case 'DNS':
            // DNS sunucuları için DNS monitoring
            deviceData.monitors.dns = {
              enabled: true,
              interval: defaultSettings.defaultDnsInterval,
              domain: 'google.com', // Test domain
              server: defaultSettings.defaultDnsServer,
              recordType: 'A'
            };
            break;
        }
      }

      // Platform-agnostic monitor setup - all monitors available for all devices
      // Only ICMP is enabled by default, users can enable others as needed
      // Categories are now purely organizational and don't affect monitor availability

      // Cihazı oluştur
      const response = await deviceService.create(deviceData);

      // Başarılı olduğunda
      setIsSubmitting(false);
      onOpenChange(false);

      // Başarı bildirimi göster
      notificationService.success(`${formData.name} cihazı eklendi`, {
        description: 'Cihaz başarıyla eklendi ve izlenmeye başlandı.',
        persistent: true, // Bildirim panelinde de göster
        category: 'device'
      });

      // Formu sıfırla
      setFormData({
        name: '',
        host: '',
        group: '',
        platform: 'linux',
        description: '',
        location: ''
      });

      // Başarı callback'ini çağır
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error creating device:', err);
      setError('Cihaz eklenirken bir hata oluştu.');

      // Hata bildirimi göster
      notificationService.error('Cihaz eklenemedi', {
        description: err.response?.data?.message || 'Cihaz eklenirken bir hata oluştu.'
      });

      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Yeni Cihaz Ekle</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-destructive/15 text-destructive p-3 rounded-md flex items-start space-x-2">
            <AlertCircle className="h-5 w-5 mt-0.5" />
            <span>{error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Cihaz Adı <span className="text-destructive">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Örn: Web Sunucusu"
                  className={formErrors.name ? "border-destructive" : ""}
                />
                {formErrors.name && (
                  <p className="text-xs text-destructive">{formErrors.name}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="host" className="text-right">
                Host Adresi <span className="text-destructive">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="host"
                  name="host"
                  value={formData.host}
                  onChange={handleChange}
                  placeholder="Örn: *********** veya server.example.com"
                  className={formErrors.host ? "border-destructive" : ""}
                />
                {formErrors.host && (
                  <p className="text-xs text-destructive">{formErrors.host}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="group" className="text-right">
                Kategori <span className="text-destructive">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Select
                  value={formData.group}
                  onValueChange={(value) => handleSelectChange('group', value)}
                >
                  <SelectTrigger id="group" className={formErrors.group ? "border-destructive" : ""}>
                    <SelectValue placeholder="Kategori seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingCategories ? (
                      <SelectItem value="" disabled>
                        <div className="flex items-center gap-2">
                          <RefreshCw className="h-4 w-4 animate-spin" />
                          Kategoriler yükleniyor...
                        </div>
                      </SelectItem>
                    ) : categories ? (
                      Object.entries(categories).map(([mainCategory, categoryData]) => {
                        const MainIcon = getIconComponent(categoryData.icon);
                        return (
                          <React.Fragment key={mainCategory}>
                            {/* Ana kategori başlığı */}
                            <SelectItem value={mainCategory} disabled className="font-semibold">
                              <div className="flex items-center gap-2">
                                <MainIcon className={`h-4 w-4 ${getColorClass(categoryData.color, true)}`} />
                                <span className="font-semibold">{mainCategory}</span>
                              </div>
                            </SelectItem>

                            {/* Alt kategoriler */}
                            {Object.entries(categoryData.subcategories).map(([subCategory, subData]) => {
                              const SubIcon = getIconComponent(subData.icon);
                              const categoryValue = `${mainCategory}/${subCategory}`;
                              return (
                                <SelectItem key={categoryValue} value={categoryValue}>
                                  <div className="flex items-center gap-2 ml-4">
                                    <SubIcon className={`h-4 w-4 ${getColorClass(categoryData.color, false)}`} />
                                    {subData.label}
                                  </div>
                                </SelectItem>
                              );
                            })}
                          </React.Fragment>
                        );
                      })
                    ) : (
                      <SelectItem value="" disabled>
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-red-500" />
                          Kategoriler yüklenemedi
                        </div>
                      </SelectItem>
                    )}

                  </SelectContent>
                </Select>
                {formErrors.group && (
                  <p className="text-xs text-destructive">{formErrors.group}</p>
                )}
              </div>
            </div>

            {/* Platform seçimi - sadece gerekli kategorilerde göster */}
            {needsPlatformSelection(formData.group) && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="platform" className="text-right">
                  Platform
                </Label>
                <div className="col-span-3 space-y-1">
                  <Select
                    value={formData.platform}
                    onValueChange={(value) => handleSelectChange('platform', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Platform seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      {getPlatformOptions().map((option) => {
                        const IconComponent = option.icon;
                        return (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <IconComponent className="h-4 w-4" />
                              {option.label}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Platform seçimi sistem monitoring türünü belirler (Windows WMI / Linux SSH)
                  </p>
                  {formErrors.platform && (
                    <p className="text-xs text-destructive">{formErrors.platform}</p>
                  )}
                </div>
              </div>
            )}

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Açıklama
              </Label>
              <div className="col-span-3">
                <Input
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Cihaz hakkında kısa açıklama (isteğe bağlı)"
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="location" className="text-right">
                Konum/Bölge
              </Label>
              <div className="col-span-3">
                <Input
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  placeholder="Cihazın fiziksel konumu (isteğe bağlı)"
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              İptal
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Ekleniyor...
                </>
              ) : (
                'Ekle'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DeviceAddDialog;
