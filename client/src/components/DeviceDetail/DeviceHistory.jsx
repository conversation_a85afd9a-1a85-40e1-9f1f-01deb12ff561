import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import MonitorTypeIcon from '../MonitorTypeIcon';
import PaginationComponent from '../PaginationComponent';
import { useDeviceHistory } from '../../hooks/useDeviceHistory';
import { Clock, RefreshCw } from 'lucide-react';

/**
 * DeviceHistory Component
 * Displays historical monitoring data with pagination
 */
const DeviceHistory = memo(({
  deviceId,
  device
}) => {
  const {
    loading,
    error,
    paginatedHistory,
    handlePageChange,
    handlePageSizeChange,
    refreshHistory
  } = useDeviceHistory(deviceId, device);

  if (loading) {
    return (
      <Card className="hover:shadow-md transition-all">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground mr-2" />
            <span className="text-muted-foreground">Geçmiş veriler yükleniyor...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="hover:shadow-md transition-all">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-muted-foreground" />
            İzleme Geçmişi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-destructive">
            <p className="font-medium">Hata</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { items, totalPages, totalItems, currentPage, itemsPerPage } = paginatedHistory;

  if (totalItems === 0) {
    return (
      <Card className="hover:shadow-md transition-all">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-muted-foreground" />
            İzleme Geçmişi
          </CardTitle>
          <CardDescription>
            Ayarlarda belirtilen saklama sürelerine göre tüm izleme türlerinin geçmişi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Clock className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Geçmiş Veri Yok</h3>
            <p className="text-muted-foreground">
              Bu cihaz için henüz geçmiş veri bulunmuyor.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderHistoryDetails = (record) => {
    switch (record.type) {
      case 'icmp':
        if (record.status === 'up' && record.responseTime) {
          return (
            <div className="text-xs text-muted-foreground">
              Yanıt süresi: <span className="font-mono">{record.responseTime} ms</span>
            </div>
          );
        }
        break;

      case 'dns':
        if (record.status === 'up') {
          return (
            <div className="text-xs text-muted-foreground">
              {record.responseTime && (
                <div>Yanıt süresi: <span className="font-mono">{record.responseTime} ms</span></div>
              )}
              {record.resolvedIp && (
                <div>Çözümlenen IP: <span className="font-mono">{record.resolvedIp}</span></div>
              )}
              {record.records && Array.isArray(record.records) && (
                <div>Çözümlenen IP: <span className="font-mono">{record.records.join(', ')}</span></div>
              )}
            </div>
          );
        }
        break;

      case 'ssl':
        if (record.status === 'up' && record.daysRemaining !== undefined) {
          return (
            <div className="text-xs text-muted-foreground">
              Kalan süre: <span className="font-mono">{record.daysRemaining} gün</span>
            </div>
          );
        }
        break;

      case 'tcp':
        if (record.status === 'up' && record.responseTime) {
          return (
            <div className="text-xs text-muted-foreground">
              Bağlantı süresi: <span className="font-mono">{record.responseTime} ms</span>
            </div>
          );
        }
        break;

      case 'http':
        if (record.status === 'up' && record.responseTime) {
          return (
            <div className="text-xs text-muted-foreground">
              Yanıt süresi: <span className="font-mono">{record.responseTime} ms</span>
              {record.statusCode && (
                <div>HTTP Kodu: <span className="font-mono">{record.statusCode}</span></div>
              )}
            </div>
          );
        }
        break;

      default:
        return null;
    }
    return null;
  };

  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-muted-foreground" />
          İzleme Geçmişi
        </CardTitle>
        <CardDescription>
          Ayarlarda belirtilen saklama sürelerine göre tüm izleme türlerinin geçmişi
          <span className="ml-2 text-sm">
            ({totalItems} kayıt)
          </span>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* History Records */}
          {items.map((record, index) => (
            <div key={`${record.type}-${record.timestamp}-${index}`} className="flex items-start border-b pb-3 last:border-0">
              <div className={`h-3 w-3 rounded-full mt-1.5 mr-3 flex-shrink-0 ${
                record.status === 'up' ? 'bg-success' : 
                record.status === 'warning' ? 'bg-warning' :
                record.status === 'critical' ? 'bg-destructive' :
                'bg-destructive'
              }`}></div>
              <div className="flex-grow">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="flex items-center gap-1">
                      <MonitorTypeIcon 
                        type={record.type} 
                        size="sm" 
                        useInheritedColor={true} 
                        showTooltip={false} 
                      />
                      <span>{record.typeLabel}</span>
                    </Badge>
                    <span className="text-sm font-medium">
                      {record.statusLabel}
                    </span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {new Date(parseInt(record.timestamp)).toLocaleString()}
                  </span>
                </div>

                {/* Monitor type specific details */}
                <div className="mt-1">
                  {renderHistoryDetails(record)}
                </div>

                {/* Error message if status is not up */}
                {record.status !== 'up' && record.message && (
                  <div className="mt-1 text-xs text-destructive">
                    {record.message}
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Pagination */}
          <PaginationComponent
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            pageSize={itemsPerPage}
            onPageSizeChange={handlePageSizeChange}
            pageSizeOptions={[5, 10, 25, 50, 100]}
            showPageSizeOptions={true}
          />
        </div>
      </CardContent>
    </Card>
  );
});

DeviceHistory.displayName = 'DeviceHistory';

export default DeviceHistory;
