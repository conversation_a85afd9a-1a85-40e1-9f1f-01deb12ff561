import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import StatusIndicator from '../StatusIndicator';
import MonitorTypeIcon from '../MonitorTypeIcon';
import MonitorTimesDisplay from '../MonitorTimesDisplay';
import { Settings, AlertCircle, Info, ExternalLink } from 'lucide-react';

/**
 * MonitorCard Component
 * Reusable card component for displaying monitor status and details
 */
const MonitorCard = memo(({
  monitorType,
  monitorData,
  deviceId,
  onSettingsClick,
  title,
  description
}) => {
  if (!monitorData) {
    return (
      <Card className="hover:shadow-md transition-all">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <MonitorTypeIcon 
                type={monitorType} 
                size="md" 
                useInheritedColor={true} 
                showTooltip={false} 
                className="text-muted-foreground" 
              />
              {title}
            </CardTitle>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onSettingsClick(monitorType)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-4 text-center">
            <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
            <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderMonitorContent = () => {
    switch (monitorType) {
      case 'icmp':
        return renderIcmpContent();
      case 'http':
        return renderHttpContent();
      case 'tcp':
        return renderTcpContent();
      case 'dns':
        return renderDnsContent();
      case 'ssl':
        return renderSslContent();
      default:
        return renderDefaultContent();
    }
  };

  const renderIcmpContent = () => {
    if (monitorData.status === 'up') {
      return (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Ping Süresi</span>
            <span className="font-mono text-lg font-bold">{monitorData.responseTime} ms</span>
          </div>
          <Progress
            value={Math.min(100, (monitorData.responseTime / 200) * 100)}
            className="h-2"
            indicatorClassName={
              monitorData.responseTime > 150 ? "bg-destructive" :
              monitorData.responseTime > 80 ? "bg-warning" : "bg-success"
            }
          />
          <div className="mt-4 pt-4 border-t space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Paket Kaybı</span>
              <span className="font-mono text-sm">{monitorData.packetLoss || 0}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Durum</span>
              <span className="font-mono text-sm">Çevrimiçi</span>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="py-4 text-center">
          <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
          <p className="text-sm font-medium">Cihaza ulaşılamıyor</p>
          <p className="text-xs text-muted-foreground mt-1">
            Son kontrol: {monitorData.lastCheck ? new Date(parseInt(monitorData.lastCheck)).toLocaleString() : '-'}
          </p>
        </div>
      );
    }
  };

  const renderHttpContent = () => {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Yanıt Süresi</span>
          <span className="font-mono text-lg font-bold">{monitorData.responseTime || 0} ms</span>
        </div>
        <Progress
          value={Math.min(100, ((monitorData.responseTime || 0) / 1000) * 100)}
          className="h-2"
          indicatorClassName={
            (monitorData.responseTime || 0) > 500 ? "bg-destructive" :
            (monitorData.responseTime || 0) > 200 ? "bg-warning" : "bg-success"
          }
        />
        <div className="mt-4 pt-4 border-t space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Durum</span>
            <span className="font-mono text-sm font-bold">
              {monitorData.details?.statusCode && monitorData.details.statusCode !== 0 ?
                `${monitorData.details.statusCode} (${monitorData.message || monitorData.details.statusText || 'Bilinmiyor'})`
                : `Bilinmiyor (${monitorData.message || 'Bağlantı hatası'})`}
            </span>
          </div>
          {/* HTTP status code specific suggestions */}
          {monitorData.details?.statusCode === 400 && (
            <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-xs text-blue-800">
                💡 <strong>Öneri:</strong> HTTP yerine HTTPS deneyin: <code className="bg-blue-100 px-1 rounded">https://</code>
              </p>
            </div>
          )}
          {monitorData.details?.statusCode === 404 && (
            <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-xs text-blue-800">
                💡 <strong>Öneri:</strong> URL'yi kontrol edin veya farklı bir path deneyin
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderTcpContent = () => {
    if (monitorData.status === 'up') {
      return (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Bağlantı Süresi</span>
            <span className="font-mono text-lg font-bold">{monitorData.responseTime} ms</span>
          </div>
          <Progress
            value={Math.min(100, (monitorData.responseTime / 200) * 100)}
            className="h-2"
            indicatorClassName={
              monitorData.responseTime > 150 ? "bg-destructive" :
              monitorData.responseTime > 80 ? "bg-warning" : "bg-success"
            }
          />
          <div className="mt-4 pt-4 border-t space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Port</span>
              <span className="font-mono text-sm">{monitorData.port}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Durum</span>
              <span className="font-mono text-sm">Açık</span>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="space-y-4">
          <div className="py-4 text-center">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
            <p className="text-sm font-medium">Port kapalı veya erişilemiyor</p>
            <p className="text-xs text-muted-foreground mt-1">
              Son kontrol: {monitorData.lastCheck ? new Date(parseInt(monitorData.lastCheck)).toLocaleString() : '-'}
            </p>
          </div>
          <div className="mt-4 pt-4 border-t space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Port</span>
              <span className="font-mono text-sm">{monitorData.port}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Durum</span>
              <span className="font-mono text-sm">Kapalı</span>
            </div>
            {monitorData.error && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Hata</span>
                <span className="font-mono text-sm text-destructive">
                  {typeof monitorData.error === 'string'
                    ? monitorData.error
                    : typeof monitorData.error === 'object'
                    ? monitorData.error.message || JSON.stringify(monitorData.error)
                    : 'Bilinmeyen hata'}
                </span>
              </div>
            )}
          </div>
        </div>
      );
    }
  };

  const renderDnsContent = () => {
    if (monitorData.status === 'up') {
      return (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Çözümleme Süresi</span>
            <span className="font-mono text-lg font-bold">{monitorData.responseTime} ms</span>
          </div>
          <Progress
            value={Math.min(100, (monitorData.responseTime / 100) * 100)}
            className="h-2"
            indicatorClassName={
              monitorData.responseTime > 80 ? "bg-destructive" :
              monitorData.responseTime > 40 ? "bg-warning" : "bg-success"
            }
          />
          <div className="mt-4 pt-4 border-t space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Çözümlenen IP</span>
              <span className="font-mono text-sm">{monitorData.resolvedIp || 'Bilinmiyor'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Durum</span>
              <span className="font-mono text-sm">Başarılı</span>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="py-4 text-center">
          <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
          <p className="text-sm font-medium">DNS çözümlemesi başarısız</p>
          <p className="text-xs text-muted-foreground mt-1">
            Son kontrol: {monitorData.lastCheck ? new Date(parseInt(monitorData.lastCheck)).toLocaleString() : '-'}
          </p>
        </div>
      );
    }
  };

  const renderSslContent = () => {
    if (monitorData.status === 'up') {
      const daysRemaining = monitorData.daysRemaining || 0;
      const isExpiringSoon = daysRemaining <= 30;
      
      return (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Kalan Süre</span>
            <span className={`font-mono text-lg font-bold ${isExpiringSoon ? 'text-destructive' : ''}`}>
              {daysRemaining} gün
            </span>
          </div>
          <Progress
            value={Math.min(100, (daysRemaining / 365) * 100)}
            className="h-2"
            indicatorClassName={
              daysRemaining <= 7 ? "bg-destructive" :
              daysRemaining <= 30 ? "bg-warning" : "bg-success"
            }
          />
          <div className="mt-4 pt-4 border-t space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Sertifika</span>
              <span className="font-mono text-sm">Geçerli</span>
            </div>
            {monitorData.issuer && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Veren</span>
                <span className="font-mono text-sm text-xs">{monitorData.issuer}</span>
              </div>
            )}
          </div>
        </div>
      );
    } else {
      return (
        <div className="py-4 text-center">
          <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
          <p className="text-sm font-medium">SSL sertifikası geçersiz</p>
          <p className="text-xs text-muted-foreground mt-1">
            Son kontrol: {monitorData.lastCheck ? new Date(parseInt(monitorData.lastCheck)).toLocaleString() : '-'}
          </p>
        </div>
      );
    }
  };

  const renderDefaultContent = () => {
    return (
      <div className="py-4 text-center">
        <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
        <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
      </div>
    );
  };

  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            <MonitorTypeIcon 
              type={monitorType} 
              size="md" 
              useInheritedColor={true} 
              showTooltip={false} 
              className="text-muted-foreground" 
            />
            {title}
          </CardTitle>
          <div className="flex items-center gap-2">
            <StatusIndicator status={monitorData.status} />
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onSettingsClick(monitorType)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <CardDescription>
          {description}
          {monitorData && (
            <MonitorTimesDisplay
              monitorData={{
                ...monitorData,
                deviceId,
                type: monitorType
              }}
            />
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {renderMonitorContent()}
      </CardContent>
    </Card>
  );
});

MonitorCard.displayName = 'MonitorCard';

export default MonitorCard;
