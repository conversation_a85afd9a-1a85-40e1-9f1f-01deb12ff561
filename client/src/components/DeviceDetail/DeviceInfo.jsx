import React, { memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import StatusIndicator from '../StatusIndicator';
import MonitorTypeIcon from '../MonitorTypeIcon';
import { getFormattedSubCategory, getSubCategoryIcon } from '../../utils/categoryUtils';
import {
  Settings, Edit, Trash2, Router, Building2, Tag, Cog, Eye, EyeOff,
  Activity, Globe2, Network, Server, Shield
} from 'lucide-react';

/**
 * DeviceInfo Component
 * Displays device basic information, category, location and monitor controls
 */
const DeviceInfo = memo(({
  device,
  overallStatus,
  onEditClick,
  onDeleteClick,
  onSettingsClick,
  onMonitorToggle,
  isMonitorEnabled
}) => {
  if (!device) {
    return (
      <Card className="hover:shadow-md transition-all">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Cihaz bilgileri yükleniyor...
          </div>
        </CardContent>
      </Card>
    );
  }

  // All monitor types - platform-agnostic approach
  const allMonitors = [
    { type: 'icmp', label: 'ICMP/Ping', icon: Activity },
    { type: 'http', label: 'HTTP/HTTPS', icon: Globe2 },
    { type: 'tcp', label: 'TCP Port', icon: Network },
    { type: 'dns', label: 'DNS', icon: Server },
    { type: 'ssl', label: 'SSL Certificate', icon: Shield }
  ];

  return (
    <div className="space-y-6">
      {/* Device Basic Info */}
      <Card className="hover:shadow-md transition-all">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl flex items-center gap-3">
              <StatusIndicator status={overallStatus} size="lg" />
              <div>
                <div className="font-bold">{device.name}</div>
                <div className="text-sm font-normal text-muted-foreground">
                  {device.host}
                </div>
              </div>
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onEditClick}
                className="flex items-center gap-2"
              >
                <Edit className="h-4 w-4" />
                Düzenle
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onDeleteClick}
                className="flex items-center gap-2 text-destructive hover:text-destructive"
              >
                <Trash2 className="h-4 w-4" />
                Sil
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Category and Platform */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground mb-1">Kategori</div>
              <div className="flex items-center gap-2">
                {getSubCategoryIcon(device.category)}
                <span className="font-medium">{getFormattedSubCategory(device.category)}</span>
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground mb-1">Platform</div>
              <Badge variant="outline" className="flex items-center gap-1 w-fit">
                <Router className="h-3 w-3" />
                {device.platform || 'Belirtilmemiş'}
              </Badge>
            </div>
          </div>

          {/* Description */}
          {device.description && (
            <div>
              <div className="text-sm font-medium text-muted-foreground mb-1">Açıklama</div>
              <p className="text-sm">{device.description}</p>
            </div>
          )}

          {/* Location */}
          {device.location && (
            <div>
              <div className="text-sm font-medium text-muted-foreground mb-1">Konum</div>
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{device.location}</span>
              </div>
            </div>
          )}

          {/* Tags */}
          {device.tags && device.tags.length > 0 && (
            <div>
              <div className="text-sm font-medium text-muted-foreground mb-2">Etiketler</div>
              <div className="flex flex-wrap gap-1">
                {device.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    <Tag className="h-3 w-3 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Monitor Controls */}
      <Card className="hover:shadow-md transition-all">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center gap-2">
            <Cog className="h-5 w-5 text-muted-foreground" />
            İzleme Ayarları
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {allMonitors.map((monitor) => {
            const enabled = isMonitorEnabled(monitor.type);
            const config = device.monitors?.[monitor.type] || {};
            
            return (
              <div key={monitor.type} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <MonitorTypeIcon 
                    type={monitor.type} 
                    size="md" 
                    useInheritedColor={true} 
                    showTooltip={false} 
                  />
                  <div>
                    <div className="font-medium">{monitor.label}</div>
                    <div className="text-xs text-muted-foreground">
                      {enabled ? 'Aktif' : 'Pasif'}
                      {enabled && config.interval && ` • ${config.interval}s aralık`}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => onSettingsClick(monitor.type)}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Ayarları düzenle</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={enabled}
                            onCheckedChange={(checked) => onMonitorToggle(monitor.type, checked)}
                          />
                          {enabled ? (
                            <Eye className="h-4 w-4 text-success" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-muted-foreground" />
                          )}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{enabled ? 'İzlemeyi durdur' : 'İzlemeyi başlat'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>
    </div>
  );
});

DeviceInfo.displayName = 'DeviceInfo';

export default DeviceInfo;
