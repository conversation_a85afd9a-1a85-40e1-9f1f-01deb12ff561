import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import StatusIndicator from '../StatusIndicator';
import MonitorCard from './MonitorCard';
import { useMonitorStatus } from '../../hooks/useMonitorStatus';
import { Activity, RefreshCw } from 'lucide-react';

/**
 * DeviceStatus Component
 * Displays real-time monitoring status for all enabled monitor types
 */
const DeviceStatus = memo(({
  deviceId,
  onSettingsClick
}) => {
  const {
    device,
    loading,
    error,
    getFormattedMonitorData,
    getEnabledMonitors,
    getStatusSummary,
    getOverallStatus
  } = useMonitorStatus(deviceId);

  if (loading) {
    return (
      <Card className="hover:shadow-md transition-all">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground mr-2" />
            <span className="text-muted-foreground">Durum bilgileri yükleniyor...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="hover:shadow-md transition-all border-destructive">
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            <p className="font-medium">Hata</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!device) {
    return (
      <Card className="hover:shadow-md transition-all">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Cihaz bulunamadı
          </div>
        </CardContent>
      </Card>
    );
  }

  const enabledMonitors = getEnabledMonitors();
  const statusSummary = getStatusSummary();
  const overallStatus = getOverallStatus();

  if (enabledMonitors.length === 0) {
    return (
      <Card className="hover:shadow-md transition-all">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-muted-foreground" />
            Cihaz Durumu
          </CardTitle>
          <CardDescription>
            Real-time izleme durumu
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">İzleme Aktif Değil</h3>
            <p className="text-muted-foreground mb-4">
              Bu cihaz için hiçbir izleme türü etkinleştirilmemiş.
            </p>
            <p className="text-sm text-muted-foreground">
              Sol panelden izleme türlerini etkinleştirin.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Summary */}
      <Card className="hover:shadow-md transition-all">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <StatusIndicator status={overallStatus} size="lg" />
                Genel Durum
              </CardTitle>
              <CardDescription>
                {statusSummary.total} izleme türü aktif
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-success">{statusSummary.up}</div>
              <div className="text-xs text-muted-foreground">Çalışıyor</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-warning">{statusSummary.warning}</div>
              <div className="text-xs text-muted-foreground">Uyarı</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-destructive">{statusSummary.critical}</div>
              <div className="text-xs text-muted-foreground">Kritik</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-destructive">{statusSummary.down}</div>
              <div className="text-xs text-muted-foreground">Çevrimdışı</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-muted-foreground">{statusSummary.unknown}</div>
              <div className="text-xs text-muted-foreground">Bilinmiyor</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Monitor Cards */}
      <div className="grid gap-6">
        {/* ICMP Monitor */}
        {device.monitors?.icmp?.enabled && (
          <MonitorCard
            monitorType="icmp"
            monitorData={getFormattedMonitorData('icmp')}
            deviceId={deviceId}
            onSettingsClick={onSettingsClick}
            title="ICMP/Ping"
            description="Ağ bağlantısı durumu"
          />
        )}

        {/* HTTP Monitor */}
        {device.monitors?.http?.enabled && (
          <MonitorCard
            monitorType="http"
            monitorData={getFormattedMonitorData('http')}
            deviceId={deviceId}
            onSettingsClick={onSettingsClick}
            title="HTTP/HTTPS"
            description="Web servis durumu"
          />
        )}

        {/* TCP Monitor */}
        {device.monitors?.tcp?.enabled && (
          <MonitorCard
            monitorType="tcp"
            monitorData={getFormattedMonitorData('tcp')}
            deviceId={deviceId}
            onSettingsClick={onSettingsClick}
            title="TCP Port"
            description="Port durumu"
          />
        )}

        {/* DNS Monitor */}
        {device.monitors?.dns?.enabled && (
          <MonitorCard
            monitorType="dns"
            monitorData={getFormattedMonitorData('dns')}
            deviceId={deviceId}
            onSettingsClick={onSettingsClick}
            title="DNS"
            description="DNS çözümleme durumu"
          />
        )}

        {/* SSL Monitor */}
        {device.monitors?.ssl?.enabled && (
          <MonitorCard
            monitorType="ssl"
            monitorData={getFormattedMonitorData('ssl')}
            deviceId={deviceId}
            onSettingsClick={onSettingsClick}
            title="SSL Certificate"
            description="SSL sertifika durumu"
          />
        )}
      </div>
    </div>
  );
});

DeviceStatus.displayName = 'DeviceStatus';

export default DeviceStatus;
