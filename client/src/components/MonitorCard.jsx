import React from 'react';
import { Settings } from 'lucide-react';
import { Button } from './ui/button';
import { Switch } from './ui/switch';

/**
 * <PERSON>zleme türü kartı bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.type - <PERSON>zleme türü (tcp, http, dns, ssl, snmp, database, api)
 * @param {string} props.title - <PERSON>zleme türü ba<PERSON>lığı
 * @param {React.ReactNode} props.icon - <PERSON>zleme türü ikonu
 * @param {boolean} props.enabled - <PERSON>zleme türü etkin mi?
 * @param {Function} props.onToggle - <PERSON>zleme türü etkinleştirildiğinde/devre dışı bırakıldığında çağrılacak fonksiyon
 * @param {Function} props.onSettingsClick - Ayarlar butonuna tıklandığında çağrılacak fonksiyon
 * @param {React.ReactNode} props.children - <PERSON><PERSON> i<PERSON>
 */
const MonitorCard = ({ type, title, icon, enabled, onToggle, onSettingsClick, children }) => {
  return (
    <div className="border rounded-lg p-4 mb-4">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center gap-2">
          {icon}
          <h3 className="font-medium">{title}</h3>
        </div>
        <div className="flex items-center gap-2">
          <Switch 
            checked={enabled} 
            onCheckedChange={onToggle} 
          />
          {enabled && (
            <Button variant="ghost" size="icon" onClick={() => onSettingsClick(type)}>
              <Settings className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      {enabled && children}
    </div>
  );
};

export default MonitorCard;
