import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from './ui/dropdown-menu';
import { Button } from './ui/button';
import { Bell, AlertTriangle, Info, CheckCircle, ExternalLink, Clock, Server, Settings, Eye } from 'lucide-react';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';

import { useNotificationMenu } from '../contexts/NotificationMenuContext';

// ✅ Basit redirect fonksiyonu - inline
const getNotificationRedirectUrl = (notification) => {
  // Cihaz bildirimi için cihaz detay sayfasına yönlendir
  if (notification.source?.type === 'device' && notification.source?.id) {
    return `/devices/${notification.source.id}`;
  }

  // Kullanıcı bildirimi için kullanıcı sayfasına yönlendir
  if (notification.source?.type === 'user' && notification.source?.id) {
    return `/users/${notification.source.id}`;
  }

  // Sistem bildirimi için ayarlar sayfasına yönlendir
  return '/settings';
};

const NotificationMenu = () => {
  const {
    unreadNotifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    refreshNotifications
  } = useNotificationMenu();

  const navigate = useNavigate();
  const [isOpen, setIsOpen] = React.useState(false);

  // Bildirime tıklandığında
  const handleNotificationClick = (notification) => {
    // Sistem kaynaklı bildirimlerde tıklama özelliğini devre dışı bırak
    if (notification.source?.type === 'system') {
      return;
    }

    // Bildirimi sessizce okundu olarak işaretle
    if (notification.status === 'new') {
      // Optimistic update: Hemen UI'dan kaldır, toast gösterme
      markAsRead(notification.id, false); // showToast = false
    }

    // Dropdown menüyü kapat
    setIsOpen(false);

    // Bildirim yönlendirme URL'sini al ve yönlendir
    const redirectUrl = getNotificationRedirectUrl(notification);
    navigate(redirectUrl);
  };

  // Bildirimi okundu işaretleme (buton ile)
  const handleMarkAsRead = (e, notificationId) => {
    e.stopPropagation(); // Parent click'i engelle
    markAsRead(notificationId, false); // showToast = false
  };

  // ✅ Basit İkon Sistemi - Source + Severity
  const getNotificationIcon = (notification) => {
    const severity = notification.severity || 'info';
    const sourceType = notification.source?.type || 'system';

    // 1. Önce source türüne göre temel ikon seç
    let baseIcon;
    let baseColor;

    if (sourceType === 'device') {
      baseIcon = Server; // ✅ DeviceDetail ile tutarlı
      baseColor = 'text-blue-500';
    } else {
      baseIcon = Settings; // ✅ theme.js ile tutarlı
      baseColor = 'text-green-500';
    }

    // 2. Severity'e göre renk override et
    let severityColor;
    switch (severity) {
      case 'critical':
        severityColor = 'text-red-500';
        break;
      case 'warning':
        severityColor = 'text-orange-500';
        break;
      case 'info':
        severityColor = 'text-blue-500';
        break;
      case 'success':
        severityColor = 'text-green-500';
        break;
      default:
        severityColor = baseColor;
    }

    // 3. Critical için özel ikon kullan
    if (severity === 'critical') {
      return <AlertTriangle className={`h-4 w-4 ${severityColor}`} />;
    }

    // 4. Success için özel ikon kullan
    if (severity === 'success') {
      return <CheckCircle className={`h-4 w-4 ${severityColor}`} />;
    }

    // 5. Diğer durumlar için source bazlı ikon
    const IconComponent = baseIcon;
    return <IconComponent className={`h-4 w-4 ${severityColor}`} />;
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0"
              variant="destructive"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[450px] p-0 overflow-hidden">
        {/* Başlık */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-base font-semibold">Bildirimler</h3>
          {unreadCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={markAllAsRead}
              className="flex items-center gap-1"
            >
              <Eye className="h-3.5 w-3.5 mr-1" /> Tümünü Okundu İşaretle
              <Badge variant="secondary" className="ml-1">{unreadCount}</Badge>
            </Button>
          )}
        </div>

        {/* Bildirim Listesi - Sadece Okunmamış Bildirimler */}
        <div className="p-2 border-b">
          <ScrollArea className="h-[400px]">
            {(() => {
              // Okunmamış bildirimleri zaman sırasına göre göster (en yeni üstte)
              // NotificationMenuContext'ten gelen veriler zaten sıralı

              // Bildirim öğesini render eden fonksiyon
              const renderNotification = (notification) => (
                <div
                  key={notification.id}
                  className="px-4 py-3 hover:bg-muted/50 transition-colors border-b last:border-b-0"
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      {getNotificationIcon(notification)}
                    </div>
                    <div
                      className="flex-1 min-w-0 cursor-pointer"
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start justify-between gap-3">
                        <p className="font-medium text-sm flex-1 min-w-0 pr-2">{notification.title}</p>
                        <div className="flex items-center text-xs text-muted-foreground whitespace-nowrap flex-shrink-0">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{formatTimestamp(notification.timestamp)}</span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                    </div>
                    <div className="ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={(e) => handleMarkAsRead(e, notification.id)}
                        title="Okundu işaretle"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              );

              // Boş durum mesajını gösteren fonksiyon
              const renderEmptyState = () => (
                <div className="flex flex-col items-center justify-center h-32 p-4">
                  <Bell className="h-10 w-10 text-muted-foreground/30 mb-2" />
                  <p className="text-sm text-muted-foreground">
                    {loading ? 'Bildirimler yükleniyor...' : 'Okunmamış bildirim bulunmuyor'}
                  </p>
                </div>
              );

              return (
                <>
                  {unreadNotifications.length === 0 ? (
                    renderEmptyState()
                  ) : (
                    unreadNotifications.map(notification => renderNotification(notification))
                  )}
                </>
              );
            })()}
          </ScrollArea>
        </div>

        {/* Alt Kısım */}
        <div className="p-2 border-t">
          <DropdownMenuItem
            className="w-full cursor-pointer flex items-center justify-center"
            onClick={() => {
              setIsOpen(false);
              navigate('/notifications');
            }}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Tüm bildirimleri görüntüle
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NotificationMenu;
