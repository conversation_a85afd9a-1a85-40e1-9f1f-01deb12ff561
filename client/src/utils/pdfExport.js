import { pdf } from '@react-pdf/renderer';
import { saveAs } from 'file-saver';
import html2canvas from 'html2canvas';

/**
 * <PERSON><PERSON> konte<PERSON> canvas'a dönüştürür
 * @param {HTMLElement} chartContainer - <PERSON><PERSON> konte<PERSON>
 * @returns {Promise<string>} - Canvas'ın data URL'i
 */
export const chartToImageDataUrl = async (chartContainer) => {
  if (!chartContainer) {
    throw new Error('Grafik konteyneri bulunamadı');
  }

  // Grafik canvas'ını oluştur
  const canvas = await html2canvas(chartContainer, {
    backgroundColor: '#ffffff',
    scale: 3, // Yüksek çözünürlük
    logging: false,
    useCORS: true,
    allowTaint: true,
  });

  // Canvas'ı data URL'e dönüştür
  return canvas.toDataURL('image/png', 1.0);
};

/**
 * PDF dokümanını oluşturur ve indirir
 * @param {React.ReactElement} pdfDocument - PDF doküman bileşeni
 * @param {string} fileName - İndirilecek dosya adı
 * @returns {Promise<void>}
 */
export const generateAndDownloadPDF = async (pdfDocument, fileName) => {
  try {
    console.log('PDF oluşturuluyor...');

    // PDF blob'ını oluştur
    const blob = await pdf(pdfDocument).toBlob();
    console.log('PDF blob oluşturuldu');

    // file-saver kullanarak indir (document.createElement kullanmadan)
    saveAs(blob, fileName);
    console.log('PDF indirme başlatıldı');

    return true;
  } catch (error) {
    console.error('PDF oluşturma hatası:', error);
    throw error;
  }
};
