/**
 * <PERSON><PERSON><PERSON> durumunu alır (sadece backend'den hesaplanmış durumu kullanır)
 * @param {Object} statuses - <PERSON><PERSON>az durumları
 * @param {string} deviceId - Cihaz ID'si
 * @returns {string} - Backend'den hesaplanmış durum
 */
export const getDeviceStatus = (statuses, deviceId) => {
  const deviceStatus = statuses[deviceId];

  if (!deviceStatus) return 'unknown';

  // Backend'den hesaplanmış durum varsa, onu kullan
  if (deviceStatus.calculatedStatus) {
    return deviceStatus.calculatedStatus;
  }

  // Backend'den henüz hesaplanmış durum gelmemişse 'unknown' döndür
  // Frontend'te hesaplama yapmıyoruz - sadece backend'e güveniyoruz
  return 'unknown';
};

/**
 * Geriye uyumluluk için eski fonksiyon adı
 * @deprecated getDeviceStatus kullanın
 */
export const calculateDeviceStatus = getDeviceStatus;

/**
 * Durum için varsayılan açıklama döndürür
 * Backend ile tutarlı açıklamalar sağlar
 * 6-durumlu sistem: 4 temel + 2 ek durum
 * @param {string} status - Durum değeri
 * @returns {string} - Açıklama
 */
export const getDefaultReasonForStatus = (status) => {
  switch (status) {
    case 'up':
      return 'Tüm servisler çalışıyor';
    case 'down':
      return 'Servisler yanıt vermiyor';
    case 'warning':
      return 'Servisler uyarı durumunda (orta seviye gecikme)';
    case 'critical':
      return 'Servisler kritik durumda (yavaş yanıt/yüksek gecikme)';
    case 'flapping':
      return 'Cihaz durumu sürekli değişiyor (kararsızlık tespit edildi)';
    case 'unknown':
      return 'Durum bilgisi bulunamadı - Cihaz yapılandırmasını kontrol edin';
    default:
      return 'Durum bilgisi bulunamadı';
  }
};

/**
 * Son kontrol zamanını formatlar
 * @param {number} timestamp - Zaman damgası
 * @returns {string} - Formatlanmış zaman
 */
export const formatLastCheckTime = (timestamp) => {
  if (!timestamp) return 'Henüz kontrol edilmedi';
  
  const now = Date.now();
  const diff = now - parseInt(timestamp);
  
  // 1 dakikadan az
  if (diff < 60000) {
    return 'Az önce';
  }
  
  // 1 saatten az
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes} dakika önce`;
  }
  
  // 1 günden az
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours} saat önce`;
  }
  
  // 1 günden fazla
  const days = Math.floor(diff / 86400000);
  return `${days} gün önce`;
};

/**
 * Durum kontrolü yapılması gerekip gerekmediğini kontrol eder
 * @param {number} lastCheck - Son kontrol zamanı
 * @param {number} maxAge - Maksimum yaş (ms)
 * @returns {boolean} - Kontrol gerekli mi?
 */
export const needsCheck = (lastCheck, maxAge = 5 * 60 * 1000) => {
  if (!lastCheck) return true;
  
  const now = Date.now();
  const diff = now - parseInt(lastCheck);
  
  return diff > maxAge;
};
