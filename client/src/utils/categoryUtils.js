/**
 * <PERSON><PERSON><PERSON> isimlerini daha okunabilir hale getiren yardımcı fonksiyonlar
 */

import {
  Activity, AlertCircle, Battery, Building2, Cloud, Cog, Cpu, Database,
  Globe, Globe2, HardDrive, Mail, Monitor, Network, Package, Radio,
  RefreshCw, Router, Search, Server, ServerCrash, Share2, Shield,
  Terminal, Webhook, Wifi
} from 'lucide-react';

/**
 * Alt kategori ismini daha okunabilir hale getirir
 * @param {string} subCategory - Alt kategori ismi
 * @returns {string} - Daha okunabilir alt kategori ismi
 */
export const formatSubCategory = (subCategory) => {
  if (!subCategory) return '';

  switch (subCategory) {
    case 'WebServer':
      return 'Web Sunucusu';
    case 'AccessPoint':
      return 'Access Point';
    case 'BinaSistemi':
      return 'Bina Sistemi';
    default:
      return subCategory;
  }
};

/**
 * <PERSON><PERSON>i grubundan alt kategoriyi çıkarır ve formatlar
 * @param {string} group - Kategori grubu (örn. "Web/WebServer")
 * @returns {string} - Formatlanmış alt kategori
 */
export const getFormattedSubCategory = (group) => {
  if (!group) return '';
  
  if (group.includes('/')) {
    const [, subCategory] = group.split('/');
    return formatSubCategory(subCategory);
  }
  
  return group;
};

/**
 * Kategori grubundan ana kategoriyi çıkarır
 * @param {string} group - Kategori grubu (örn. "Web/WebServer")
 * @returns {string} - Ana kategori
 */
export const getMainCategory = (group) => {
  if (!group) return '';
  
  if (group.includes('/')) {
    const [mainCategory] = group.split('/');
    return mainCategory;
  }
  
  return group;
};

/**
 * Alt kategori için uygun ikonu döndüren fonksiyon
 * @param {string} subCategory - Alt kategori ismi
 * @returns {string} - İkon ismi
 */
export const getSubCategoryIcon = (subCategory) => {
  if (!subCategory) return 'Tag';

  switch (subCategory) {
    // Ağ Cihazları
    case 'Router':
      return 'Router';
    case 'Switch':
      return 'Share2';
    case 'Firewall':
      return 'Shield';
    case 'AccessPoint':
      return 'Wifi';
    case 'Modem':
      return 'Radio';
    
    // Sunucular
    case 'Fiziksel':
      return 'Server';
    case 'Sanal':
      return 'Cloud';
    case 'Container':
      return 'Package';
    case 'Veritabanı':
      return 'Database';
    case 'Depolama':
      return 'HardDrive';
    
    // Web
    case 'WebServer':
      return 'Globe2';
    case 'API':
      return 'Webhook';
    case 'Mail':
      return 'Mail';
    case 'CDN':
      return 'Network';
    case 'DNS':
      return 'Search';
    
    // IoT
    case 'Sensör':
      return 'Activity';
    case 'PLC':
      return 'Cog';
    case 'BinaSistemi':
      return 'Building2';
    case 'UPS':
      return 'Battery';
    
    default:
      return 'Tag';
  }
};

/**
 * Ana kategori için uygun ikonu döndüren fonksiyon
 * @param {string} mainCategory - Ana kategori ismi
 * @returns {string} - İkon ismi
 */
export const getMainCategoryIcon = (mainCategory) => {
  if (!mainCategory) return 'Tag';

  switch (mainCategory) {
    case 'Ağ Cihazları':
      return 'Network';
    case 'Sunucular':
      return 'Server';
    case 'Web':
      return 'Globe';
    case 'IoT':
      return 'Cpu';
    default:
      return 'Tag';
  }
};

/**
 * İkon string'ini React component'ine çevir (DeviceAddDialog ile tutarlı)
 * @param {string} iconName - İkon ismi
 * @returns {React.Component} - İkon komponenti
 */
export const getIconComponent = (iconName) => {
  const iconMap = {
    Network, Router, Share2, Shield, Wifi, Radio,
    Server, ServerCrash, Cloud, Package, Database, HardDrive,
    Globe, Globe2, Webhook, Mail, Search,
    Cpu, Activity, Cog, Building2, Battery,
    Monitor, Terminal, RefreshCw, AlertCircle
  };
  return iconMap[iconName] || Network;
};

/**
 * Renk mapping (DeviceAddDialog ile tutarlı)
 * @param {string} color - Renk ismi
 * @param {boolean} isMain - Ana kategori mi?
 * @returns {string} - CSS sınıfı
 */
export const getColorClass = (color, isMain = false) => {
  const colorMap = {
    blue: isMain ? 'text-blue-500' : 'text-blue-400',
    green: isMain ? 'text-green-500' : 'text-green-400',
    purple: isMain ? 'text-purple-500' : 'text-purple-400',
    orange: isMain ? 'text-orange-500' : 'text-orange-400'
  };
  return colorMap[color] || 'text-gray-500';
};
