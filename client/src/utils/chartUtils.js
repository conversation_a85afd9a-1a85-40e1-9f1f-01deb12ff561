import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';

// <PERSON><PERSON> tü<PERSON>ü <PERSON>
export const getStatusChartOptions = () => [
  { value: 'pie', label: 'Pasta Grafik', icon: <PieChart className="h-4 w-4" /> },
  { value: 'doughnut', label: 'Halka Grafik', icon: <PieChart className="h-4 w-4" /> },
  { value: 'polarArea', label: 'Polar Alan', icon: <PieChart className="h-4 w-4" /> }
];

export const getResponseChartOptions = () => [
  { value: 'line', label: 'Çizgi Grafik', icon: <LineChart className="h-4 w-4" /> },
  { value: 'bar', label: 'Sütun Grafik', icon: <BarChart className="h-4 w-4" /> }
];

// Pasta grafik ayarları
export const getPieChartOptions = (showLegend, totalStats) => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const percentage = totalStats > 0 ? Math.round((value / totalStats) * 100) : 0;
            return `${label}: ${value} cihaz (${percentage}%)`;
          }
        }
      }
    }
  };
};

// Halka grafik ayarları
export const getDoughnutChartOptions = (showLegend, totalStats) => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '70%',
    plugins: {
      legend: {
        position: 'bottom',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const percentage = totalStats > 0 ? Math.round((value / totalStats) * 100) : 0;
            return `${label}: ${value} cihaz (${percentage}%)`;
          }
        }
      }
    }
  };
};

// Polar alan grafik ayarları
export const getPolarAreaChartOptions = (showLegend, totalStats) => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const percentage = totalStats > 0 ? Math.round((value / totalStats) * 100) : 0;
            return `${label}: ${value} cihaz (${percentage}%)`;
          }
        }
      }
    }
  };
};

// Yanıt süresi çizgi grafik ayarları
export const getResponseLineChartOptions = (showGrid, showLegend) => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Yanıt Süresi (ms)'
        },
        ticks: {
          callback: function(value) {
            return value + ' ms';
          }
        },
        grid: {
          display: showGrid
        }
      },
      x: {
        title: {
          display: true,
          text: 'Zaman'
        },
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y + ' ms';
          }
        }
      }
    },
    interaction: {
      mode: 'index',
      intersect: false,
    },
  };
};

// Yanıt süresi sütun grafik ayarları
export const getResponseBarChartOptions = (showGrid, showLegend) => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Yanıt Süresi (ms)'
        },
        ticks: {
          callback: function(value) {
            return value + ' ms';
          }
        },
        grid: {
          display: showGrid
        }
      },
      x: {
        title: {
          display: true,
          text: 'Zaman'
        },
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y + ' ms';
          }
        }
      }
    },
    interaction: {
      mode: 'index',
      intersect: false,
    },
  };
};

// Durum geçmişi grafik ayarları
export const getStatusHistoryChartOptions = (showGrid, showLegend) => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        stacked: true,
        title: {
          display: true,
          text: 'Cihaz Sayısı'
        },
        grid: {
          display: showGrid
        }
      },
      x: {
        stacked: true,
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.parsed.y} cihaz`;
          }
        }
      }
    }
  };
};

// Grup analizi grafik ayarları
export const getGroupChartOptions = (showGrid, showLegend) => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Cihaz Sayısı'
        },
        stacked: true,
        grid: {
          display: showGrid
        }
      },
      x: {
        title: {
          display: true,
          text: 'Cihaz Grupları'
        },
        stacked: true,
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        display: showLegend
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y + ' cihaz';
          }
        }
      }
    }
  };
};

// Durum dağılımı veri seti
export const getStatusDistributionData = (stats, STATUS_COLORS) => {
  return {
    labels: ['Çevrimiçi', 'Çevrimdışı', 'Bilinmiyor'],
    datasets: [
      {
        data: [stats.online, stats.offline, stats.warning],
        backgroundColor: [
          STATUS_COLORS.online.bg,
          STATUS_COLORS.offline.bg,
          STATUS_COLORS.unknown.bg
        ],
        borderColor: [
          STATUS_COLORS.online.border,
          STATUS_COLORS.offline.border,
          STATUS_COLORS.unknown.border
        ],
        borderWidth: 1,
      }
    ]
  };
};

// Grup analizi veri seti
export const getGroupChartData = (groupedByGroup, createStatusDataset) => {
  return {
    labels: Object.keys(groupedByGroup),
    datasets: [
      createStatusDataset('Çevrimiçi', Object.values(groupedByGroup).map(group => group.online), 'online'),
      createStatusDataset('Çevrimdışı', Object.values(groupedByGroup).map(group => group.offline), 'offline'),
      createStatusDataset('Bilinmiyor', Object.values(groupedByGroup).map(group => group.warning), 'unknown')
    ]
  };
};
