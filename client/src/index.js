// Webpack ve React Router uyarılarını bastır
const originalConsoleWarn = console.warn;
console.warn = function(message) {
  if (message && typeof message === 'string' &&
      (message.includes('DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE') ||
       message.includes('DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE') ||
       message.includes('React Router Future Flag Warning'))) {
    return;
  }
  originalConsoleWarn.apply(console, arguments);
};

import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import {
  BrowserRouter,
  createRoutesFromChildren,
  matchRoutes,
  UNSAFE_DataRouterContext,
  UNSAFE_DataRouterStateContext,
  UNSAFE_NavigationContext,
  UNSAFE_LocationContext,
  UNSAFE_RouteContext
} from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <ThemeProvider>
        <App />
      </ThemeProvider>
    </BrowserRouter>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
