/**
 * <PERSON><PERSON> doğrulama servisi
 */

import { api } from './api';

// Local storage anahtarları
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_KEY = 'user';

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> kaydı
 * @param {Object} userData - Kullanıcı verileri
 * @returns {Promise<Object>} - Kullanıcı ve token bilgileri
 */
const register = async (userData) => {
  const response = await api.post('/auth/register', userData);

  if (response.data.accessToken) {
    // Token ve kullanıcı bilgilerini sakla
    localStorage.setItem(TOKEN_KEY, response.data.accessToken);
    localStorage.setItem(REFRESH_TOKEN_KEY, response.data.refreshToken);
    localStorage.setItem(USER_KEY, JSON.stringify(response.data.user));
  }

  return response.data;
};

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> giri<PERSON>
 * @param {string} username - <PERSON><PERSON><PERSON><PERSON><PERSON> adı
 * @param {string} password - Şifre
 * @returns {Promise<Object>} - <PERSON><PERSON><PERSON><PERSON>ı ve token bilgileri
 */
const login = async (username, password) => {
  console.log('auth.js login attempt with:', { username, password });
  try {
    const response = await api.post('/auth/login', { username, password });
    console.log('auth.js login response:', response);

    if (response.data.accessToken) {
      // Token ve kullanıcı bilgilerini sakla
      localStorage.setItem(TOKEN_KEY, response.data.accessToken);
      localStorage.setItem(REFRESH_TOKEN_KEY, response.data.refreshToken);
      localStorage.setItem(USER_KEY, JSON.stringify(response.data.user));

      // Debug için localStorage'a kaydedilen değerleri kontrol et
      console.log('Saved to localStorage:', {
        [TOKEN_KEY]: localStorage.getItem(TOKEN_KEY),
        [REFRESH_TOKEN_KEY]: localStorage.getItem(REFRESH_TOKEN_KEY),
        [USER_KEY]: localStorage.getItem(USER_KEY)
      });
    }

    return response.data;
  } catch (error) {
    console.error('auth.js login error:', error);
    throw error;
  }
};

/**
 * Kullanıcı çıkışı
 */
const logout = async () => {
  try {
    // API'ye çıkış isteği gönder
    await api.post('/auth/logout');
  } catch (error) {
    console.error('Çıkış hatası:', error);
  } finally {
    // Local storage'dan token ve kullanıcı bilgilerini temizle
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
  }
};

/**
 * Token yenileme
 * @returns {Promise<Object>} - Yeni token bilgileri
 */
const refreshToken = async () => {
  const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
  const user = getCurrentUser();

  if (!refreshToken || !user) {
    return Promise.reject('Refresh token veya kullanıcı bulunamadı');
  }

  try {
    const response = await api.post('/auth/refresh-token', {
      refreshToken,
      userId: user.id
    });

    // Yeni token'ları sakla
    localStorage.setItem(TOKEN_KEY, response.data.accessToken);
    localStorage.setItem(REFRESH_TOKEN_KEY, response.data.refreshToken);

    return response.data;
  } catch (error) {
    // Hata durumunda çıkış yap
    logout();
    return Promise.reject(error);
  }
};

/**
 * Mevcut kullanıcıyı getir
 * @returns {Object|null} - Kullanıcı nesnesi veya null
 */
const getCurrentUser = () => {
  const userStr = localStorage.getItem(USER_KEY);
  return userStr ? JSON.parse(userStr) : null;
};

/**
 * Access token'ı getir
 * @returns {string|null} - Access token veya null
 */
const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * Kullanıcının giriş yapmış olup olmadığını kontrol et
 * @returns {boolean} - Giriş yapmış ise true
 */
const isAuthenticated = () => {
  return !!getToken() && !!getCurrentUser();
};

/**
 * Kullanıcının admin olup olmadığını kontrol et
 * @returns {boolean} - Admin ise true
 */
const isAdmin = () => {
  const user = getCurrentUser();
  return user && user.role === 'admin';
};

/**
 * Şifre değiştir
 * @param {string} currentPassword - Mevcut şifre
 * @param {string} newPassword - Yeni şifre
 * @returns {Promise<Object>} - Yanıt
 */
const changePassword = async (currentPassword, newPassword) => {
  const response = await api.post('/users/change-password', {
    currentPassword,
    newPassword
  });

  return response.data;
};

/**
 * Şifre sıfırlama talebi
 * @param {string} email - E-posta adresi
 * @returns {Promise<Object>} - Yanıt
 */
const forgotPassword = async (email) => {
  const response = await api.post('/auth/forgot-password', {
    email
  });

  return response.data;
};

/**
 * Şifre sıfırlama token'ını doğrula
 * @param {string} token - Reset token
 * @returns {Promise<Object>} - Token bilgileri
 */
const verifyResetToken = async (token) => {
  const response = await api.get(`/auth/verify-reset-token/${token}`);
  return response.data;
};

/**
 * Şifre sıfırla
 * @param {string} token - Reset token
 * @param {string} password - Yeni şifre
 * @returns {Promise<Object>} - Yanıt
 */
const resetPassword = async (token, password) => {
  const response = await api.post('/auth/reset-password', {
    token,
    password
  });

  return response.data;
};

// Servis nesnesini oluştur
const authService = {
  register,
  login,
  logout,
  refreshToken,
  getCurrentUser,
  getToken,
  isAuthenticated,
  isAdmin,
  changePassword,
  forgotPassword,
  verifyResetToken,
  resetPassword
};

// Servis nesnesini export et
export default authService;
export { authService };
