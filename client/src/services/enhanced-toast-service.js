import { toast } from 'sonner';

/**
 * Gelişmiş Toast Servisi
 * - Deduplication (tekrar önleme)
 * - Smart grouping (akıllı gruplama)
 * - <PERSON>llanıcı ayarları desteği
 * - Action button'lar
 * - <PERSON><PERSON> bildirimleri
 */

class EnhancedToastService {
  constructor() {
    this.recentToasts = new Map();
    this.pendingGroups = new Map();
    this.groupingTimeouts = new Map();
    this.toastSettings = null;
  }

  // Toast ayarlarını güncelle
  updateSettings(settings) {
    this.toastSettings = settings;
  }

  // Bildirim toast'ı göster
  showNotificationToast(notification, settings = null) {
    const toastSettings = settings || this.toastSettings || {
      toastsEnabled: true,
      toastDuration: 5000,
      toastSounds: true,
      toastDeduplication: true,
      toastGrouping: true,
      // Varsayılan filtreleme ayarları
      notifyOnCritical: true,
      notifyOnWarning: true,
      notifyOnInfo: true,
      notifyOnSuccess: true,
      notifyOnDevice: true,
      notifyOnSystem: true
    };

    if (!toastSettings.toastsEnabled) {
      return;
    }

    // ✅ Kullanıcı ayarlarına göre filtreleme
    if (!this.shouldShowNotificationBySettings(notification, toastSettings)) {
      return;
    }

    // Deduplication kontrolü
    if (toastSettings.toastDeduplication && !this.shouldShowToast(notification)) {
      return;
    }

    // Gruplama kontrolü
    if (toastSettings.toastGrouping) {
      this.handleGroupedToast(notification, toastSettings);
      return;
    }

    // Normal toast göster
    this.displayToast(notification, toastSettings);
  }

  // ✅ Kullanıcı ayarlarına göre bildirim gösterilmeli mi?
  shouldShowNotificationBySettings(notification, settings) {
    // ✅ Severity bazlı filtreleme
    const severity = notification.severity || 'info';
    let severityAllowed = true;

    switch (severity) {
      case 'critical':
        severityAllowed = settings.notifyOnCritical !== false;
        break;
      case 'warning':
        severityAllowed = settings.notifyOnWarning !== false;
        break;
      case 'success':
        severityAllowed = settings.notifyOnSuccess !== false;
        break;
      case 'info':
      default:
        severityAllowed = settings.notifyOnInfo !== false;
        break;
    }

    // ✅ Source bazlı filtreleme
    const sourceType = notification.source?.type || notification.category || 'system';
    let sourceAllowed = true;

    switch (sourceType) {
      case 'device':
        sourceAllowed = settings.notifyOnDevice !== false;
        break;
      case 'system':
      default:
        sourceAllowed = settings.notifyOnSystem !== false;
        break;
    }

    // Her iki filtreyi de geçmeli
    return severityAllowed && sourceAllowed;
  }

  // Toast'ın gösterilip gösterilmeyeceğini kontrol et
  shouldShowToast(notification) {
    const key = this.getToastKey(notification);
    const now = Date.now();
    const lastShown = this.recentToasts.get(key);

    // Son 30 saniye içinde aynı toast gösterilmişse, gösterme
    if (lastShown && (now - lastShown) < 30000) {
      return false;
    }

    // Toast'ı kaydet
    this.recentToasts.set(key, now);

    // Eski kayıtları temizle (5 dakika)
    this.cleanupOldToasts();

    return true;
  }

  // Toast anahtarı oluştur
  getToastKey(notification) {
    return `${notification.category}-${notification.title}-${notification.source?.id || 'system'}`;
  }

  // Eski toast kayıtlarını temizle
  cleanupOldToasts() {
    const now = Date.now();
    for (const [key, timestamp] of this.recentToasts.entries()) {
      if (now - timestamp > 300000) { // 5 dakika
        this.recentToasts.delete(key);
      }
    }
  }

  // Gruplu toast işleme
  handleGroupedToast(notification, settings) {
    const groupKey = this.getGroupKey(notification);

    // Mevcut grubu al veya yeni oluştur
    if (!this.pendingGroups.has(groupKey)) {
      this.pendingGroups.set(groupKey, []);
    }

    // Bildirimi gruba ekle
    this.pendingGroups.get(groupKey).push(notification);

    // Mevcut timeout'u temizle
    if (this.groupingTimeouts.has(groupKey)) {
      clearTimeout(this.groupingTimeouts.get(groupKey));
    }

    // Yeni timeout ayarla (2 saniye bekle)
    const timeout = setTimeout(() => {
      this.flushGroup(groupKey, settings);
    }, 2000);

    this.groupingTimeouts.set(groupKey, timeout);
  }

  // Grup anahtarı oluştur
  getGroupKey(notification) {
    return `${notification.category}-${notification.source?.type || 'system'}`;
  }

  // Grubu flush et ve toast göster
  flushGroup(groupKey, settings) {
    const notifications = this.pendingGroups.get(groupKey);
    if (!notifications || notifications.length === 0) {
      return;
    }

    // Grup temizle
    this.pendingGroups.delete(groupKey);
    this.groupingTimeouts.delete(groupKey);

    if (notifications.length === 1) {
      // Tek bildirim varsa normal göster
      this.displayToast(notifications[0], settings);
    } else {
      // Çoklu bildirim varsa grupla
      const groupedNotification = this.createGroupedNotification(notifications);
      this.displayToast(groupedNotification, settings);
    }
  }

  // ✅ Gruplu bildirim oluştur - Basit sistem
  createGroupedNotification(notifications) {
    const count = notifications.length;
    const sourceTypes = [...new Set(notifications.map(n => n.source?.type || 'system'))];
    const sourceNames = [...new Set(notifications.map(n => n.source?.name || 'Sistem').filter(Boolean))];

    let title, message;

    if (sourceTypes.length === 1) {
      // Aynı kaynak türü
      const sourceTypeName = this.getSourceDisplayName(sourceTypes[0]);
      title = `${count} yeni ${sourceTypeName} bildirimi`;

      if (sourceNames.length === 1) {
        message = `${sourceNames[0]} kaynağından ${count} yeni bildirim alındı.`;
      } else {
        message = `${sourceNames.length} farklı kaynaktan ${count} yeni bildirim alındı.`;
      }
    } else {
      // Karışık kaynak türleri
      title = `${count} yeni bildirim`;
      message = `${sourceTypes.length} farklı kaynak türünden ${count} yeni bildirim alındı.`;
    }

    return {
      id: `group-${Date.now()}`,
      title,
      message,
      category: sourceTypes[0], // ✅ İlk source türünü kullan (device/system)
      severity: this.getHighestSeverity(notifications),
      timestamp: new Date().toISOString(),
      isGrouped: true,
      groupedNotifications: notifications,
      source: sourceNames.length === 1 ? { name: sourceNames[0], type: sourceTypes[0] } : { name: 'Çoklu Kaynak', type: 'mixed' }
    };
  }

  // ✅ En yüksek önem derecesini al - Success dahil
  getHighestSeverity(notifications) {
    const severityOrder = ['critical', 'warning', 'success', 'info'];
    for (const severity of severityOrder) {
      if (notifications.some(n => n.severity === severity)) {
        return severity;
      }
    }
    return 'info';
  }

  // ✅ Basit kaynak türü görünen adını al
  getSourceDisplayName(sourceType) {
    const names = {
      device: 'cihaz',
      system: 'sistem'
    };
    return names[sourceType] || sourceType;
  }

  // ✅ Severity görünen adını al
  getSeverityDisplayName(severity) {
    const names = {
      critical: 'kritik',
      warning: 'uyarı',
      info: 'bilgi',
      success: 'başarı'
    };
    return names[severity] || severity;
  }

  // Toast'ı ekranda göster
  displayToast(notification, settings) {
    const toastType = this.getToastType(notification);
    const duration = settings.toastDuration === 0 ? Infinity : settings.toastDuration;

    // Ses çal
    if (settings.toastSounds) {
      this.playNotificationSound(notification.severity);
    }

    // Toast seçenekleri - Ultra temiz tasarım
    const options = {
      duration,
      description: notification.message,
      // Hiç buton yok - sadece bilgi
      action: undefined,
      cancel: undefined
    };

    // Toast göster
    toast[toastType](notification.title, options);
  }

  // Toast tipini belirle
  getToastType(notification) {
    switch (notification.severity) {
      case 'critical':
        return 'error';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      default:
        return 'info';
    }
  }



  // Bildirim sesi çal
  playNotificationSound(severity) {
    try {
      // Modern tarayıcılarda AudioContext kullan
      if (typeof window !== 'undefined' && window.AudioContext) {
        const audioContext = new window.AudioContext();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // Önem derecesine göre frekans
        const frequency = severity === 'critical' ? 800 : severity === 'warning' ? 600 : 400;

        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
        oscillator.type = 'sine';

        // Ses seviyesi
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        // Sesi çal
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
      }
    } catch (error) {
      console.warn('Bildirim sesi çalınamadı:', error);
    }
  }

  // Manuel toast göster (genel kullanım)
  showToast(type, title, message, options = {}) {
    const settings = this.toastSettings || {
      toastsEnabled: true,
      toastDuration: 5000,
      toastSounds: true
    };

    if (!settings.toastsEnabled) {
      return;
    }

    const duration = options.duration || settings.toastDuration;

    // Ses çal (eğer ayarlarda açıksa)
    if (settings.toastSounds && options.playSound !== false) {
      const severity = type === 'error' ? 'critical' : type === 'warning' ? 'warning' : 'info';
      this.playNotificationSound(severity);
    }

    // Ultra temiz tasarım: hiç buton yok
    const toastOptions = {
      description: message,
      duration: duration === 0 ? Infinity : duration,
      // Hiç buton yok - sadece bilgi
      action: undefined,
      cancel: undefined
    };

    toast[type](title, toastOptions);
  }

  // Başarı toast'ı
  success(title, message, options = {}) {
    this.showToast('success', title, message, options);
  }

  // Hata toast'ı
  error(title, message, options = {}) {
    this.showToast('error', title, message, options);
  }

  // Uyarı toast'ı
  warning(title, message, options = {}) {
    this.showToast('warning', title, message, options);
  }

  // Bilgi toast'ı
  info(title, message, options = {}) {
    this.showToast('info', title, message, options);
  }
}

// Singleton instance
const enhancedToastService = new EnhancedToastService();

// Global erişim için window'a ekle (development için)
if (typeof window !== 'undefined') {
  window.enhancedToastService = enhancedToastService;
  window.testToast = {
    success: (message) => enhancedToastService.success("Test Başarı", message),
    error: (message) => enhancedToastService.error("Test Hata", message),
    warning: (message) => enhancedToastService.warning("Test Uyarı", message),
    info: (message) => enhancedToastService.info("Test Bilgi", message)
  };
}

export default enhancedToastService;
