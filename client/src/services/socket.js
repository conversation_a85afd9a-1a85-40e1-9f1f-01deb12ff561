/**
 * Socket.io bağlantısını sağlayan servis
 * Bu dosya, Context API üzerinden socket.io bağlantısına eri<PERSON><PERSON>
 */

import { useSocket } from '../contexts/SocketContext';

// Socket hook'unu dışa aktar
export const useSocketService = () => {
  const socket = useSocket();

  // Socket üzerinden işlemler yapan yardımcı fonksiyonlar
  const emitEvent = (eventName, data) => {
    if (!socket) return;
    socket.emit(eventName, data);
  };

  const onEvent = (eventName, callback) => {
    if (!socket) return () => {};
    socket.on(eventName, callback);
    return () => socket.off(eventName, callback);
  };

  return {
    socket,
    emitEvent,
    onEvent
  };
};
