// API yardımcı fonksiyonları

/**
 * JSON string'i parse eder, hata durumunda orijinal değeri döndürür
 * @param {string|object} jsonString - Parse edilecek JSON string
 * @param {string} errorContext - Hata mesajı için bağlam
 * @returns {object} - Parse edilmiş JSON objesi veya orijinal değer
 */
export const safeJsonParse = (jsonString, errorContext = 'JSON') => {
  if (typeof jsonString !== 'string') return jsonString;
  
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    console.error(`Error parsing ${errorContext}:`, e);
    return jsonString;
  }
};

/**
 * Bir obje içindeki JSON string'leri parse eder
 * @param {object} data - İşlenecek veri objesi
 * @param {string} detailsKey - Parse edilecek alanın adı
 * @param {string} errorContext - Hata mesajı için bağlam
 * @returns {object} - JSON alanları parse edilmiş obje
 */
export const parseJsonDetails = (data, detailsKey = 'details', errorContext = 'details') => {
  if (!data) return data;
  
  if (data[detailsKey] && typeof data[detailsKey] === 'string') {
    data[detailsKey] = safeJsonParse(data[detailsKey], errorContext);
  }
  
  return data;
};

/**
 * Bir dizi içindeki her öğenin belirli bir alanını JSON olarak parse eder
 * @param {Array} dataArray - İşlenecek veri dizisi
 * @param {string} detailsKey - Parse edilecek alanın adı
 * @param {string} errorContext - Hata mesajı için bağlam
 * @returns {Array} - JSON alanları parse edilmiş dizi
 */
export const parseJsonDetailsInArray = (dataArray, detailsKey = 'details', errorContext = 'history details') => {
  if (!Array.isArray(dataArray)) return dataArray;
  
  return dataArray.map(item => parseJsonDetails(item, detailsKey, errorContext));
};

/**
 * Bir obje içindeki tüm alt objelerin belirli bir alanını JSON olarak parse eder
 * @param {object} dataObject - İşlenecek veri objesi
 * @param {Array} keys - Parse edilecek alt objelerin anahtarları
 * @param {string} detailsKey - Parse edilecek alanın adı
 * @returns {object} - JSON alanları parse edilmiş obje
 */
export const parseJsonDetailsInNestedObjects = (dataObject, keys, detailsKey = 'details') => {
  if (!dataObject) return dataObject;
  
  keys.forEach(key => {
    if (dataObject[key]) {
      dataObject[key] = parseJsonDetails(dataObject[key], detailsKey, `${key} ${detailsKey}`);
    }
  });
  
  return dataObject;
};

/**
 * Bir obje içindeki tüm alt objelerin belirli bir alanını JSON olarak parse eder
 * @param {object} dataObject - İşlenecek veri objesi (cihaz ID'lerine göre gruplandırılmış)
 * @param {Array} keys - Parse edilecek alt objelerin anahtarları
 * @param {string} detailsKey - Parse edilecek alanın adı
 * @returns {object} - JSON alanları parse edilmiş obje
 */
export const parseJsonDetailsInAllDevices = (dataObject, keys, detailsKey = 'details') => {
  if (!dataObject) return dataObject;
  
  Object.keys(dataObject).forEach(deviceId => {
    dataObject[deviceId] = parseJsonDetailsInNestedObjects(dataObject[deviceId], keys, detailsKey);
  });
  
  return dataObject;
};
