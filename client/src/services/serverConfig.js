/**
 * <PERSON><PERSON><PERSON> yapılandırma servisi
 * Proxy kullanıldığında doğrudan göreceli URL'ler kullanılır
 */

// Sunucu bilgilerini saklamak için
let serverConfig = {
  ip: 'localhost',
  port: '5000',
  frontendPort: '3000',
  isLoaded: true
};

/**
 * Sunucu bilgilerini döndürür
 * @returns {Promise<Object>} - Sunucu bilgileri
 */
const loadServerConfig = async () => {
  // Proxy kullanıldığında sunucu bilgilerini almaya gerek yok
  console.log('Sunucu bilgileri kullanılıyor:', serverConfig);
  return serverConfig;
};

/**
 * Sunucu bilgilerini döndürür
 * @returns {Object} - Sunucu bilgileri
 */
const getServerConfig = () => {
  return serverConfig;
};

/**
 * Tam API URL'sini oluşturur
 * @returns {string} - API URL
 */
const getApiUrl = () => {
  // Proxy kullanıldığında doğrudan /api yolunu kullan
  return '/api';
};

/**
 * Tam Socket.io URL'sini oluşturur
 * @returns {string} - Socket.io URL
 */
const getSocketUrl = () => {
  // Proxy kullanıldığında doğrudan kök URL'yi kullan
  return '/';
};

// Servis nesnesini oluştur
const serverConfigService = {
  loadServerConfig,
  getServerConfig,
  getApiUrl,
  getSocketUrl
};

// Servis nesnesini export et
export default serverConfigService;
