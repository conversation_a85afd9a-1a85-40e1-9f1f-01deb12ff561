/**
 * Socket.io olaylarını merkezi olarak yöneten servis
 */

/**
 * Socket.io bağlantı olaylarını dinler
 * @param {Object} socket - Socket.io bağlantısı
 * @param {Function} onConnect - Bağlantı kurulduğunda çalışacak fonksiyon
 * @param {Function} onDisconnect - Bağlantı kesildiğinde çalışacak fonksiyon
 * @returns {Function} - Temizlik fonksiyonu
 */
export const setupConnectionListeners = (socket, { onConnect, onDisconnect }) => {
  if (!socket) return () => {};
  
  socket.on('connect', onConnect);
  socket.on('disconnect', onDisconnect);
  
  return () => {
    socket.off('connect', onConnect);
    socket.off('disconnect', onDisconnect);
  };
};

/**
 * <PERSON>ihaz olaylarını dinler
 * @param {Object} socket - Socket.io bağlantısı
 * @param {Object} handlers - Olay işleyicileri
 * @returns {Function} - Temizlik fonksiyonu
 */
export const setupDeviceListeners = (socket, handlers) => {
  if (!socket) return () => {};
  
  const {
    onDeviceNew,
    onDeviceUpdate,
    onDeviceDelete,
    onDeviceChecked,
    onDeviceStatusUpdate
  } = handlers;
  
  socket.on('device:new', onDeviceNew);
  socket.on('device:update', onDeviceUpdate);
  socket.on('device:delete', onDeviceDelete);
  socket.on('device:checked', onDeviceChecked);
  socket.on('device:status:update', onDeviceStatusUpdate);
  
  return () => {
    socket.off('device:new', onDeviceNew);
    socket.off('device:update', onDeviceUpdate);
    socket.off('device:delete', onDeviceDelete);
    socket.off('device:checked', onDeviceChecked);
    socket.off('device:status:update', onDeviceStatusUpdate);
  };
};

/**
 * İzleme olaylarını dinler
 * @param {Object} socket - Socket.io bağlantısı
 * @param {Function} onMonitorUpdate - İzleme güncellendiğinde çalışacak fonksiyon
 * @returns {Function} - Temizlik fonksiyonu
 */
export const setupMonitorListeners = (socket, { onMonitorUpdate }) => {
  if (!socket) return () => {};
  
  socket.on('monitor:update', onMonitorUpdate);
  
  return () => {
    socket.off('monitor:update', onMonitorUpdate);
  };
};

/**
 * Bildirim olaylarını dinler
 * @param {Object} socket - Socket.io bağlantısı
 * @param {Function} onNotificationNew - Yeni bildirim geldiğinde çalışacak fonksiyon
 * @param {Function} onNotificationUpdate - Bildirim güncellendiğinde çalışacak fonksiyon
 * @returns {Function} - Temizlik fonksiyonu
 */
export const setupNotificationListeners = (socket, { onNotificationNew, onNotificationUpdate }) => {
  if (!socket) return () => {};
  
  socket.on('notification:new', onNotificationNew);
  socket.on('notification:update', onNotificationUpdate);
  
  return () => {
    socket.off('notification:new', onNotificationNew);
    socket.off('notification:update', onNotificationUpdate);
  };
};

/**
 * Sistem olaylarını dinler
 * @param {Object} socket - Socket.io bağlantısı
 * @param {Function} onSystemUpdate - Sistem güncellendiğinde çalışacak fonksiyon
 * @returns {Function} - Temizlik fonksiyonu
 */
export const setupSystemListeners = (socket, { onSystemUpdate }) => {
  if (!socket) return () => {};
  
  socket.on('system:update', onSystemUpdate);
  
  return () => {
    socket.off('system:update', onSystemUpdate);
  };
};

/**
 * Cihaz kontrol isteklerini gönderir
 * @param {Object} socket - Socket.io bağlantısı
 * @param {Array} deviceIds - Kontrol edilecek cihaz ID'leri
 * @param {Array} monitorTypes - Kontrol edilecek izleme türleri
 * @returns {Promise} - İşlem sonucu
 */
export const sendDeviceCheckRequest = (socket, deviceIds = [], monitorTypes = []) => {
  if (!socket) return Promise.reject(new Error('Socket bağlantısı yok'));
  
  return new Promise((resolve, reject) => {
    // Yanıt için bir kerelik olay dinleyicisi ekle
    const responseHandler = (response) => {
      console.log('Cihaz kontrol yanıtı alındı:', response);
      socket.off('device:check:response', responseHandler);
      
      if (response.success) {
        resolve(true);
      } else {
        reject(new Error(response.error || 'Cihaz kontrolü başarısız oldu'));
      }
    };
    
    // Yanıtı dinle
    socket.on('device:check:response', responseHandler);
    
    // İsteği gönder
    socket.emit('device:check', { deviceIds, monitorTypes });
    
    // 30 saniye timeout
    setTimeout(() => {
      socket.off('device:check:response', responseHandler);
      reject(new Error('Cihaz kontrol isteği zaman aşımına uğradı'));
    }, 30000);
  });
};

/**
 * Cihaz durumlarını ister
 * @param {Object} socket - Socket.io bağlantısı
 */
export const requestDeviceStatuses = (socket) => {
  if (!socket) return;
  
  console.log('Mevcut durumlar isteniyor...');
  socket.emit('device:status:get');
};
