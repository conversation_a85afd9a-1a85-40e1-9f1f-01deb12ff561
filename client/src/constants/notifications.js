/**
 * ✅ Basit Bildirim Sistemi Sabitleri
 */

// Bildirim türleri
export const NOTIFICATION_TYPES = {
  DEVICE: 'device',
  SYSTEM: 'system',
  USER: 'user'
};

// ✅ Basit Kaynak Türleri
export const NOTIFICATION_SOURCES = {
  DEVICE: 'device',
  SYSTEM: 'system'
};

// ✅ Legacy Support - Eski kod uyumluluğu için
export const NOTIFICATION_CATEGORIES = {
  // Basit mapping - sadece source türleri
  DEVICE: 'device',
  SYSTEM: 'system',
  USER: 'user',

  // Legacy uyumluluk
  DEVICE_CONNECTION: 'device',
  DEVICE_PERFORMANCE: 'device',
  DEVICE_SERVICE: 'device',
  DEVICE_SECURITY: 'device',
  DEVICE_HARDWARE: 'device',
  SYSTEM_MONITORING: 'system',
  SYSTEM_USER: 'system',
  SYSTEM_SECURITY: 'system',
  SYSTEM_MAINTENANCE: 'system',
  SYSTEM_ERROR: 'system',

  // Di<PERSON>er legacy
  STATUS: 'device',
  PERFORMANCE: 'device',
  SECURITY: 'system',
  MAINTENANCE: 'system'
};

// Bildirim önem dereceleri
export const NOTIFICATION_SEVERITY = {
  CRITICAL: 'critical',
  WARNING: 'warning',
  INFO: 'info',
  SUCCESS: 'success'
};

// ✅ Basit Bildirim Durumları
export const NOTIFICATION_STATUS = {
  NEW: 'new',
  READ: 'read',
  RESOLVED: 'resolved'
};

// ✅ Basit Kaynak Renkleri
export const SOURCE_COLORS = {
  [NOTIFICATION_SOURCES.DEVICE]: 'blue',
  [NOTIFICATION_SOURCES.SYSTEM]: 'green'
};

// ✅ Basit Kaynak İkonları (Lucide ikon isimleri)
export const SOURCE_ICONS = {
  [NOTIFICATION_SOURCES.DEVICE]: 'Server',
  [NOTIFICATION_SOURCES.SYSTEM]: 'Settings'
};

// ✅ Basit Kaynak Route'ları
export const SOURCE_ROUTES = {
  [NOTIFICATION_SOURCES.DEVICE]: '/devices',
  [NOTIFICATION_SOURCES.SYSTEM]: '/settings'
};

// ✅ Önem Derecesi Renkleri
export const SEVERITY_COLORS = {
  [NOTIFICATION_SEVERITY.CRITICAL]: 'red',
  [NOTIFICATION_SEVERITY.WARNING]: 'orange',
  [NOTIFICATION_SEVERITY.INFO]: 'blue',
  [NOTIFICATION_SEVERITY.SUCCESS]: 'green'
};

// ✅ Önem Derecesi İkonları
export const SEVERITY_ICONS = {
  [NOTIFICATION_SEVERITY.CRITICAL]: 'AlertTriangle',
  [NOTIFICATION_SEVERITY.WARNING]: 'AlertTriangle',
  [NOTIFICATION_SEVERITY.INFO]: 'Info',
  [NOTIFICATION_SEVERITY.SUCCESS]: 'CheckCircle'
};

// ✅ Durum Renkleri
export const STATUS_COLORS = {
  [NOTIFICATION_STATUS.NEW]: 'red',
  [NOTIFICATION_STATUS.READ]: 'gray',
  [NOTIFICATION_STATUS.RESOLVED]: 'green'
};

// ✅ Durum İkonları
export const STATUS_ICONS = {
  [NOTIFICATION_STATUS.NEW]: 'Circle',
  [NOTIFICATION_STATUS.READ]: 'Eye',
  [NOTIFICATION_STATUS.RESOLVED]: 'CheckCircle'
};

// ✅ Toast Bildirim Türleri
export const TOAST_TYPES = {
  [NOTIFICATION_SEVERITY.CRITICAL]: 'error',
  [NOTIFICATION_SEVERITY.WARNING]: 'warning',
  [NOTIFICATION_SEVERITY.INFO]: 'info',
  [NOTIFICATION_SEVERITY.SUCCESS]: 'success'
};
