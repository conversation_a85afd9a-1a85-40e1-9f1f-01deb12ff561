# NetWatch Tema Sistemi Rehberi

B<PERSON> rehber, NetWatch uygulamasında tutarlı tasarım için merkezi tema sisteminin nasıl kullanılacağını açıklar.

## 📁 <PERSON><PERSON><PERSON>

```
client/src/lib/theme.js - <PERSON> tema dosyası
```

## 🎨 Tema Bileşenleri

### 1. Du<PERSON> Türleri (STATUS_TYPES)

**6-Durumlu Sistem: 4 Temel + 2 Ek Durum**

```javascript
import { STATUS_TYPES } from '../lib/theme';

// Cihaz durumları (6 durum)
STATUS_TYPES.UP          // 'up' - Tüm servisler çalışıyor
STATUS_TYPES.DOWN        // 'down' - Servisler yanıt vermiyor
STATUS_TYPES.WARNING     // 'warning' - Orta seviye sorunlar
STATUS_TYPES.CRITICAL    // 'critical' - <PERSON><PERSON><PERSON> sorunlar
STATUS_TYPES.FLAPPING    // 'flapping' - Karars<PERSON><PERSON>l<PERSON>k tespit edildi
STATUS_TYPES.UNKNOWN     // 'unknown' - Durum bilgisi yok

// Bildirim durumları
STATUS_TYPES.NEW         // 'new'
STATUS_TYPES.READ        // 'read'
STATUS_TYPES.RESOLVED    // 'resolved'
```

### 2. Önem Dereceleri (SEVERITY_TYPES)

```javascript
import { SEVERITY_TYPES } from '../lib/theme';

SEVERITY_TYPES.CRITICAL  // 'critical'
SEVERITY_TYPES.WARNING   // 'warning'
SEVERITY_TYPES.SUCCESS   // 'success'
SEVERITY_TYPES.INFO      // 'info'
```

### 3. Renk Sistemleri

#### Durum Renkleri
```javascript
import { STATUS_COLORS } from '../lib/theme';

// Otomatik karanlık mod desteği
const statusColor = STATUS_COLORS[STATUS_TYPES.UP]; 
// → 'text-green-500 dark:text-green-400'
```

#### Önem Derecesi Badge'leri
```javascript
import { getSeverityBadgeClass } from '../lib/theme';

<span className={getSeverityBadgeClass('critical')}>
  Kritik
</span>
// → Otomatik karanlık mod desteği ile kırmızı badge
```

### 4. Bildirim Stilleri

#### Satır Stilleri
```javascript
import { getNotificationRowClass } from '../lib/theme';

<TableRow className={getNotificationRowClass(notification.status)}>
  // Otomatik okunmamış bildirim vurgusu + karanlık mod
</TableRow>
```

#### Metin Stilleri
```javascript
import { getNotificationTextClass } from '../lib/theme';

<span className={getNotificationTextClass(notification.status)}>
  {notification.title}
</span>
// → Okunmamış: kalın, Okunmuş: normal + karanlık mod
```

### 5. Genel Metin Stilleri

```javascript
import { TEXT_STYLES } from '../lib/theme';

<p className={`text-sm ${TEXT_STYLES.PRIMARY}`}>Ana metin</p>
<p className={`text-sm ${TEXT_STYLES.SECONDARY}`}>İkincil metin</p>
<p className={`text-sm ${TEXT_STYLES.MUTED}`}>Soluk metin</p>
```

## 🔧 Yardımcı Fonksiyonlar

### Etiket Alma
```javascript
import { getSeverityLabel } from '../lib/theme';

getSeverityLabel('critical'); // → 'Kritik'
getSeverityLabel('warning');  // → 'Uyarı'
```

### İkon Alma
```javascript
import { getStatusIcon } from '../lib/theme';

{getStatusIcon(STATUS_TYPES.UP, 'md')}
// → Yeşil CheckCircle ikonu
```

## 🌙 Karanlık Mod Desteği

Tüm renkler otomatik karanlık mod desteği ile gelir:

```javascript
// Light mode: text-gray-900
// Dark mode:  text-gray-100
TEXT_STYLES.PRIMARY

// Light mode: bg-blue-100 text-blue-800
// Dark mode:  bg-blue-900/30 text-blue-300
getSeverityBadgeClass('info')
```

## ✅ En İyi Uygulamalar

### ✅ Yapılması Gerekenler
- Tema sistemindeki tanımlı renkleri kullanın
- Yardımcı fonksiyonları tercih edin
- Yeni renkler eklerken karanlık mod desteği ekleyin

### ❌ Yapılmaması Gerekenler
- Hard-coded Tailwind sınıfları kullanmayın
- Karanlık mod için ayrı stil yazmayın
- Tema dışında renk tanımlamayın

## 🔄 Tema Genişletme

Yeni stil kategorileri eklemek için:

1. `theme.js` dosyasına yeni sabitler ekleyin
2. Karanlık mod varyantlarını dahil edin
3. Yardımcı fonksiyon oluşturun
4. Bu rehberi güncelleyin

## 📋 Örnek Kullanım

```javascript
import { 
  STATUS_TYPES,
  SEVERITY_TYPES,
  getSeverityBadgeClass,
  getNotificationRowClass,
  getNotificationTextClass,
  getSeverityLabel,
  TEXT_STYLES
} from '../lib/theme';

const NotificationRow = ({ notification }) => (
  <TableRow className={getNotificationRowClass(notification.status)}>
    <TableCell>
      <span className={getSeverityBadgeClass(notification.severity)}>
        {getSeverityLabel(notification.severity)}
      </span>
    </TableCell>
    <TableCell>
      <span className={getNotificationTextClass(notification.status)}>
        {notification.title}
      </span>
      <p className={`text-xs ${TEXT_STYLES.MUTED}`}>
        {notification.message}
      </p>
    </TableCell>
  </TableRow>
);
```

Bu sistem sayesinde:
- 🎨 Tutarlı tasarım
- 🌙 Otomatik karanlık mod
- 🔧 Kolay bakım
- 📱 Responsive tasarım
- ♿ Erişilebilirlik desteği
