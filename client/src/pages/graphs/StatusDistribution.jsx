import React, { useRef, useState } from 'react';
import { Download, <PERSON>ader2, <PERSON><PERSON><PERSON>, SlidersHorizontal } from 'lucide-react';
import { <PERSON>, <PERSON>hn<PERSON>, PolarArea } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  RadialLinearScale
} from 'chart.js';

// Chart.js bileşenlerini kaydet
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  RadialLinearScale
);
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '../../components/ui/dropdown-menu';
import { Checkbox } from '../../components/ui/checkbox';
import { Label } from '../../components/ui/label';
import { useDeviceStats, useDevices, useStatusDistribution, useChartOptions, chartTypeOptions } from '../../hooks/useGraphData';
import { chartToImageDataUrl, generateAndDownloadPDF } from '../../utils/pdfExport';
import { settingsService } from '../../services/api';
import ChartPDFDocument from '../../components/ChartPDFDocument';

// Durum Dağılımı Grafiği
const StatusDistributionChart = ({
  data,
  chartType,
  pieOptions,
  doughnutOptions,
  polarAreaOptions,
  isLoading
}) => {
  // Yükleme durumu
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground">Veriler yükleniyor...</p>
      </div>
    );
  }

  // Veri kontrolü - daha kapsamlı kontrol
  const hasValidData = data &&
                      data.datasets &&
                      Array.isArray(data.datasets) &&
                      data.datasets.length > 0 &&
                      data.datasets[0] &&
                      data.datasets[0].data &&
                      Array.isArray(data.datasets[0].data) &&
                      data.datasets[0].data.length > 0;

  // Veri yoksa veya geçersizse
  if (!hasValidData) {
    return (
      <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
        <PieChart className="h-10 w-10 mb-3 opacity-50" />
        <span>Veri bulunamadı</span>
        <p className="text-xs mt-2 text-center">Cihaz ekleyerek grafikleri görüntüleyebilirsiniz</p>
      </div>
    );
  }

  // Grafik türüne göre uygun grafiği göster
  return (
    <div className="w-full h-full max-w-full overflow-hidden flex items-center justify-center">
      <div className="w-4/5 h-4/5">
        {chartType === 'pie' && (
          <Pie data={data} options={pieOptions} />
        )}
        {chartType === 'doughnut' && (
          <Doughnut data={data} options={doughnutOptions} />
        )}
        {chartType === 'polarArea' && (
          <PolarArea data={data} options={polarAreaOptions} />
        )}
      </div>
    </div>
  );
};

const StatusDistribution = () => {
  // Grafik referansı
  const chartRef = useRef(null);

  // Durum değişkenleri
  const [statusChartType, setStatusChartType] = useState('pie');
  const [showLegend, setShowLegend] = useState(true);
  const [showGrid, setShowGrid] = useState(true);

  // Veri sorgularını çalıştır
  const { data: deviceStatuses, isLoading: isStatusesLoading } = useDeviceStats();
  const { data: devices, isLoading: isDevicesLoading } = useDevices();

  // Grafik ayarlarını oluştur
  const {
    pieChartOptions,
    doughnutChartOptions,
    polarAreaChartOptions,
  } = useChartOptions(showLegend, showGrid);

  // Durum dağılımı verilerini hesapla
  const { data: statusDistributionData } = useStatusDistribution(devices, deviceStatuses);

  // PDF olarak dışa aktar
  const exportAsPDF = async () => {
    try {
      console.log("PDF dışa aktarma başlatılıyor...");

      const title = "Durum Dağılımı";
      console.log("Grafik başlığı:", title);

      const today = new Date();
      const dateStr = today.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
      const timeStr = today.toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit'
      });

      // Grafik konteynerini bul
      const chartContainer = chartRef.current;
      if (!chartContainer) {
        throw new Error('Grafik bulunamadı');
      }
      console.log("Grafik konteyneri bulundu");

      // Grafik görüntüsünü oluştur
      console.log("Grafik görüntüsü oluşturuluyor...");
      const chartImageDataUrl = await chartToImageDataUrl(chartContainer);
      console.log("Grafik görüntüsü başarıyla oluşturuldu");

      // İstatistik verilerini hazırla
      let statsData = null;

      // Şirket adını ayarlardan al
      let companyName = 'NetWatch';
      try {
        const settings = await settingsService.getAll();
        companyName = settings.companyName || 'NetWatch';
      } catch (error) {
        console.error('Ayarlar alınırken hata:', error);
      }

      // PDF dokümanını oluştur
      console.log("PDF dokümanı oluşturuluyor...");
      const pdfDocument = (
        <ChartPDFDocument
          title={title}
          chartImageDataUrl={chartImageDataUrl}
          dateStr={dateStr}
          timeStr={timeStr}
          stats={statsData}
          companyName={companyName}
        />
      );

      // Dosya adı
      const fileName = `${title.replace(/\s+/g, '_')}_${dateStr.replace(/\//g, '-')}.pdf`;

      // PDF'i oluştur ve indir
      console.log("PDF indiriliyor:", fileName);
      await generateAndDownloadPDF(pdfDocument, fileName);
      console.log("PDF başarıyla oluşturuldu ve indirildi");
    } catch (error) {
      console.error('PDF dışa aktarma hatası:', error);
      console.error('Hata detayları:', error.message);
      console.error('Hata stack:', error.stack);
      alert(`Grafik dışa aktarılırken bir hata oluştu: ${error.message}`);
    }
  };

  return (
    <div className="p-6 space-y-6 max-w-full overflow-x-hidden">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Durum Dağılımı</h1>
        </div>
      </div>

      <div className="grid gap-4 w-full">
        {/* Başlık ve Seçim Kartı */}
        <Card className="w-full">
          <CardHeader className="px-6 py-4">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Durum Dağılımı</CardTitle>
                <CardDescription>
                  Cihazların mevcut durum dağılımı
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Select value={statusChartType} onValueChange={setStatusChartType}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Grafik Türü" />
                  </SelectTrigger>
                  <SelectContent>
                    {chartTypeOptions.status.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <SlidersHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Grafik Ayarları</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <div className="p-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="showLegend" checked={showLegend} onCheckedChange={setShowLegend} />
                        <Label htmlFor="showLegend">Göstergeyi Göster</Label>
                      </div>
                      <div className="flex items-center space-x-2 mt-2">
                        <Checkbox id="showGrid" checked={showGrid} onCheckedChange={setShowGrid} />
                        <Label htmlFor="showGrid">Izgarayı Göster</Label>
                      </div>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button variant="outline" size="icon" onClick={exportAsPDF} title="PDF olarak indir">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Grafik Kartı */}
        <Card className="w-full">
          <CardContent className="px-6 py-6">
            <div className="h-[600px] flex flex-col items-center justify-center chart-container w-full" ref={chartRef}>
              <StatusDistributionChart
                data={statusDistributionData}
                chartType={statusChartType}
                pieOptions={pieChartOptions}
                doughnutOptions={doughnutChartOptions}
                polarAreaOptions={polarAreaChartOptions}
                isLoading={isStatusesLoading || isDevicesLoading}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StatusDistribution;
