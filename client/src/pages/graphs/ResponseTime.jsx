import React, { useRef, useState, useEffect } from 'react';
import { AlertTriangle, Calendar, Download, LineChart, Loader2, SlidersHorizontal } from 'lucide-react';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Chart.js bileşenlerini kaydet
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '../../components/ui/dropdown-menu';
import { Checkbox } from '../../components/ui/checkbox';
import { Label } from '../../components/ui/label';
import { ScrollArea } from '../../components/ui/scroll-area';
import { useDeviceStats, useDevices, useResponseTimeData, useChartOptions, chartTypeOptions, timeRangeOptions } from '../../hooks/useGraphData';
import { chartToImageDataUrl, generateAndDownloadPDF } from '../../utils/pdfExport';
import ChartPDFDocument from '../../components/ChartPDFDocument';
import { settingsService } from '../../services/api';
import { usePageLoading } from '../../hooks/useSmartLoading';
import { SmartPageLoading } from '../../components/ui/smart-loading';
import { Skeleton } from '../../components/ui/skeleton';
import { STATUS_BG_COLORS, STATUS_TYPES } from '../../lib/theme';

// Yanıt Süresi Grafiği
const ResponseTimeChart = ({
  data,
  chartType,
  lineOptions,
  barOptions,
  isLoading,
  selectedDevices
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {/* Grafik Başlığı Skeleton */}
        <div className="flex items-center justify-between">
          <Skeleton width="w-32" height="h-6" />
          <Skeleton width="w-24" height="h-8" rounded="rounded-md" />
        </div>

        {/* Grafik Alanı Skeleton */}
        <div className="h-[300px] bg-muted rounded-lg flex items-center justify-center">
          <div className="text-center space-y-2">
            <Skeleton width="w-16" height="h-16" rounded="rounded-full" className="mx-auto" />
            <Skeleton width="w-32" height="h-4" className="mx-auto" />
          </div>
        </div>

        {/* Alt Bilgiler Skeleton */}
        <div className="flex justify-between items-center">
          <Skeleton width="w-20" height="h-4" />
          <Skeleton width="w-24" height="h-4" />
        </div>
      </div>
    );
  }

  if (!data || !data.datasets || data.datasets.length === 0) {
    if (selectedDevices.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
          <LineChart className="h-10 w-10 mb-3 opacity-50" />
          <span>Cihaz seçilmedi</span>
          <p className="text-xs mt-2 text-center">Lütfen en az bir cihaz seçin</p>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
        <AlertTriangle className="h-10 w-10 mb-3 opacity-50" />
        <span>Yanıt süresi verisi bulunamadı</span>
        <p className="text-xs mt-2 text-center">Seçili cihazlar için yanıt süresi verisi henüz mevcut değil</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full max-w-full overflow-hidden flex items-center justify-center">
      <div className="w-full h-full">
        <div style={{ display: chartType === 'line' ? 'block' : 'none', height: '100%', width: '100%' }}>
          <Line data={data} options={lineOptions} />
        </div>
        <div style={{ display: chartType === 'bar' ? 'block' : 'none', height: '100%', width: '100%' }}>
          <Bar data={data} options={barOptions} />
        </div>
      </div>
    </div>
  );
};

const ResponseTime = () => {
  // Grafik referansı
  const chartRef = useRef(null);

  // Durum değişkenleri
  const [responseChartType, setResponseChartType] = useState('line');
  const [timeRange, setTimeRange] = useState('24h');
  const [selectedDevices, setSelectedDevices] = useState([]);
  const [showLegend, setShowLegend] = useState(true);
  const [showGrid, setShowGrid] = useState(true);

  // Veri sorgularını çalıştır
  const { data: deviceStatuses, isLoading: isStatusesLoading } = useDeviceStats();
  const { data: devices, isLoading: isDevicesLoading } = useDevices();
  const { data: responseTimeData, isLoading: isResponseLoading } = useResponseTimeData(selectedDevices, timeRange, devices);

  // Smart loading hook
  const pageLoading = usePageLoading(devices, isDevicesLoading || isStatusesLoading);

  // Grafik ayarlarını oluştur
  const {
    responseLineChartOptions,
    responseBarChartOptions,
  } = useChartOptions(showLegend, showGrid);

  // İlk yükleme sırasında varsayılan cihazları seç
  useEffect(() => {
    console.log('Devices effect triggered:', { devices, selectedDevices });
    if (devices && devices.length > 0 && selectedDevices.length === 0) {
      // İlk 5 cihazı seç (veya daha az varsa tümünü)
      const deviceIds = devices.slice(0, Math.min(5, devices.length)).map(device => device.id);
      console.log('Selecting default devices:', deviceIds);
      setSelectedDevices(deviceIds);
    }
  }, [devices, selectedDevices.length]);

  // Cihaz seçimi için yardımcı fonksiyonlar
  const handleDeviceSelect = (deviceId) => {
    setSelectedDevices(prev => {
      if (prev.includes(deviceId)) {
        return prev.filter(id => id !== deviceId);
      } else {
        return [...prev, deviceId];
      }
    });
  };

  const handleSelectAll = () => {
    if (devices && devices.length > 0) {
      if (selectedDevices.length === devices.length) {
        setSelectedDevices([]);
      } else {
        setSelectedDevices(devices.map(device => device.id));
      }
    }
  };

  // Cihaz durumu rengini belirle
  const getStatusColor = (status) => {
    return STATUS_BG_COLORS[status] || STATUS_BG_COLORS[STATUS_TYPES.UNKNOWN];
  };

  // PDF olarak dışa aktar
  const exportAsPDF = async () => {
    try {
      console.log("PDF dışa aktarma başlatılıyor...");

      const title = "Yanıt Süresi Grafiği";
      console.log("Grafik başlığı:", title);

      const today = new Date();
      const dateStr = today.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
      const timeStr = today.toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit'
      });

      // Grafik konteynerini bul
      const chartContainer = chartRef.current;
      if (!chartContainer) {
        throw new Error('Grafik bulunamadı');
      }
      console.log("Grafik konteyneri bulundu");

      // Grafik görüntüsünü oluştur
      console.log("Grafik görüntüsü oluşturuluyor...");
      const chartImageDataUrl = await chartToImageDataUrl(chartContainer);
      console.log("Grafik görüntüsü başarıyla oluşturuldu");

      // İstatistik verilerini hazırla
      let statsData = null;

      // Şirket adını ayarlardan al
      let companyName = 'NetWatch';
      try {
        const settings = await settingsService.getAll();
        companyName = settings.companyName || 'NetWatch';
      } catch (error) {
        console.error('Ayarlar alınırken hata:', error);
      }

      // PDF dokümanını oluştur
      console.log("PDF dokümanı oluşturuluyor...");
      const pdfDocument = (
        <ChartPDFDocument
          title={title}
          chartImageDataUrl={chartImageDataUrl}
          dateStr={dateStr}
          timeStr={timeStr}
          stats={statsData}
          companyName={companyName}
        />
      );

      // Dosya adı
      const fileName = `${title.replace(/\s+/g, '_')}_${dateStr.replace(/\//g, '-')}.pdf`;

      // PDF'i oluştur ve indir
      console.log("PDF indiriliyor:", fileName);
      await generateAndDownloadPDF(pdfDocument, fileName);
      console.log("PDF başarıyla oluşturuldu ve indirildi");
    } catch (error) {
      console.error('PDF dışa aktarma hatası:', error);
      console.error('Hata detayları:', error.message);
      console.error('Hata stack:', error.stack);
      alert(`Grafik dışa aktarılırken bir hata oluştu: ${error.message}`);
    }
  };

  // Smart loading - sayfa seviyesi
  if (pageLoading.shouldShowFullPageLoading()) {
    return (
      <SmartPageLoading
        loadingType="initial"
        pageTitle="Yanıt Süresi Grafiği"
        skeletonLayout="default"
      />
    );
  }

  return (
    <div className="p-6 space-y-6 max-w-full overflow-x-hidden">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Yanıt Süresi Grafiği</h1>
        </div>
      </div>

      <div className="grid gap-4 w-full">
        {/* Başlık ve Seçim Kartı */}
        <Card className="w-full">
          <CardHeader className="px-6 py-4">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Yanıt Süresi Grafiği</CardTitle>
                <CardDescription>
                  Seçili cihazların yanıt süresi değişimleri
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Select value={responseChartType} onValueChange={setResponseChartType}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Grafik Türü" />
                  </SelectTrigger>
                  <SelectContent>
                    {chartTypeOptions.response.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-[180px]">
                    <Calendar className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Zaman Aralığı" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeRangeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <SlidersHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Grafik Ayarları</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <div className="p-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="showLegend" checked={showLegend} onCheckedChange={setShowLegend} />
                        <Label htmlFor="showLegend">Göstergeyi Göster</Label>
                      </div>
                      <div className="flex items-center space-x-2 mt-2">
                        <Checkbox id="showGrid" checked={showGrid} onCheckedChange={setShowGrid} />
                        <Label htmlFor="showGrid">Izgarayı Göster</Label>
                      </div>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button variant="outline" size="icon" onClick={exportAsPDF} title="PDF olarak indir">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Grafik ve Cihazlar Kartları */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 w-full">
          {/* Grafik Kartı */}
          <Card className="md:col-span-3">
            <CardContent className="px-6 py-6">
              <div className="h-[600px] chart-container w-full" ref={chartRef}>
                <ResponseTimeChart
                  data={responseTimeData}
                  chartType={responseChartType}
                  lineOptions={responseLineChartOptions}
                  barOptions={responseBarChartOptions}
                  isLoading={isResponseLoading}
                  selectedDevices={selectedDevices}
                />
              </div>
            </CardContent>
          </Card>

          {/* Cihazlar Kartı */}
          <Card>
            <CardHeader className="px-6 py-4">
              <div className="flex justify-between items-center">
                <CardTitle>Cihazlar</CardTitle>
                <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                  {selectedDevices.length === (devices?.length || 0) ? 'Tümünü Kaldır' : 'Tümünü Seç'}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[490px]">
                <div className="p-4 space-y-3">
                  {isDevicesLoading ? (
                    <div className="text-center py-4">Yükleniyor...</div>
                  ) : devices && devices.length > 0 ? (
                    devices.map(device => {
                      const status = deviceStatuses?.[device.id]?.icmp?.status || 'unknown';
                      return (
                        <div key={device.id} className="flex items-center space-x-3 py-1">
                          <Checkbox
                            id={`device-${device.id}`}
                            checked={selectedDevices.includes(device.id)}
                            onCheckedChange={() => handleDeviceSelect(device.id)}
                            className="h-5 w-5"
                          />
                          <Label
                            htmlFor={`device-${device.id}`}
                            className="flex-1 flex items-center justify-between cursor-pointer text-base"
                          >
                            <span className="truncate">{device.name}</span>
                            <div className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}></div>
                          </Label>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center py-4">Cihaz bulunamadı</div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ResponseTime;
