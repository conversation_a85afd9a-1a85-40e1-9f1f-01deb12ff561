import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { AlertTriangle, Home } from 'lucide-react';

const NotFound = () => {
  const navigate = useNavigate();
  
  return (
    <div className="flex justify-center items-center h-[80vh]">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6 text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-20 w-20 text-destructive" />
          </div>
          <h1 className="text-2xl font-bold mb-2">Sayfa Bulunamadı</h1>
          <p className="text-muted-foreground mb-6">
            Aradığınız sayfa mevcut değil veya taşınmış olabilir.
          </p>
          <Button 
            onClick={() => navigate('/')}
            className="gap-2"
          >
            <Home className="h-4 w-4" />
            Ana Sayfaya Dön
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotFound;
