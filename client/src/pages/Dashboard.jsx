import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { Switch } from '../components/ui/switch';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import StatusIndicator from '../components/StatusIndicator';
import { STATUS_TYPES, STATUS_COLORS } from '../lib/theme';

import {
  Server, AlertCircle, CheckCircle, XCircle, RefreshCw, Activity, Loader2, LineChart as LineChartIcon, Bell
} from 'lucide-react';

// Bileşenler
import StatusCard from '../components/dashboard/StatusCard';
// LineChart bileşeni kaldırıldı

// AlertsList kaldırıldı
import SystemHealthCard from '../components/dashboard/SystemHealthCard';
import QuickActionCard from '../components/dashboard/QuickActionCard';

// Chart.js bileşenleri
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Chart.js bileşenlerini kaydet
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// API servisleri
import { deviceService, monitorService, systemService } from '../services/api';

// Context
import { useSocket } from '../contexts/SocketContext';
import { useDevices } from '../contexts/DeviceContext';
import { usePageLoading } from '../hooks/useSmartLoading';
import { SmartPageLoading } from '../components/ui/smart-loading';
import { useSystem } from '../contexts/SystemContext';




const Dashboard = () => {
  const navigate = useNavigate();
  const socket = useSocket();

  const { devices, statuses, loading, error, loadData: loadDevicesData } = useDevices();

  // Yanıt süresi geçmişi için yerel state kullan
  const [dashboardError, setDashboardError] = useState(null);
  const [dashboardLoading, setDashboardLoading] = useState(true);

  // Smart loading hook
  const pageLoading = usePageLoading(devices, loading || dashboardLoading);
  const [responseTimeHistory, setResponseTimeHistory] = useState([]);
  // DeviceContext'ten gerekli değişkenleri al
  const [isRefreshing, setIsRefreshing] = useState(false); // Yenileme durumu




  // Socket güncellemelerini işle
  const handleSocketUpdate = useCallback((data) => {
    // Verileri yeniden yüklemek yerine mevcut state'i güncelle
    // Bu, gereksiz API çağrılarını önler
    if (data.type === 'device') {
      setDevices(prev => prev.map(d => d.id === data.id ? {...d, ...data} : d));
    } else if (data.type === 'status') {
      setStatuses(prev => ({...prev, [data.deviceId]: {...prev[data.deviceId], ...data.data}}));
    }
  }, []);



  // Backend'den gelen hesaplanmış durumu kullan
  const getDeviceStatus = useCallback((deviceId) => {
    const deviceStatus = statuses[deviceId]?.calculatedStatus;
    return deviceStatus || 'unknown';
  }, [statuses]);

  // Verileri yükle (ilk yükleme için)
  const loadData = useCallback(async () => {
    try {
      // Yanıt süresi geçmişini yükle (son 24 saat)
      // Gerçek verileri kullan
      let responseTimeData = [];

      // Çevrimiçi cihazları filtrele
      const onlineDevices = devices.filter(device => {
        const status = getDeviceStatus(device.id);
        return status === 'up';
      });

      if (onlineDevices.length > 0) {
        // Her çevrimiçi cihaz için ICMP geçmişini al
        const deviceHistoryPromises = onlineDevices.map(device =>
          monitorService.getIcmpHistory(device.id, '24h')
        );

        const allDeviceHistories = await Promise.all(deviceHistoryPromises);

        // Zaman bazlı veri noktalarını oluştur (son 24 saat, saatlik)
        const now = new Date();
        const timePoints = [];

        for (let i = 24; i >= 0; i--) {
          const time = new Date(now);
          time.setHours(now.getHours() - i);
          timePoints.push({
            timestamp: time.toISOString(),
            hour: time.getHours(),
            responseTimeSum: 0,
            count: 0
          });
        }

        // Tüm cihazların verilerini birleştir ve saatlik ortalama hesapla
        allDeviceHistories.forEach(deviceHistory => {
          if (deviceHistory && deviceHistory.length > 0) {
            deviceHistory.forEach(entry => {
              if (entry.responseTime && entry.timestamp) {
                const entryTime = new Date(entry.timestamp);
                const hourIndex = timePoints.findIndex(tp =>
                  new Date(tp.timestamp).getHours() === entryTime.getHours() &&
                  new Date(tp.timestamp).getDate() === entryTime.getDate()
                );

                if (hourIndex !== -1) {
                  timePoints[hourIndex].responseTimeSum += entry.responseTime;
                  timePoints[hourIndex].count += 1;
                }
              }
            });
          }
        });

        // Ortalama yanıt sürelerini hesapla
        responseTimeData = timePoints.map(point => ({
          timestamp: point.timestamp,
          value: point.count > 0 ? Math.round(point.responseTimeSum / point.count) : 0
        }));
      } else {
        // Çevrimiçi cihaz yoksa veya veri yoksa, boş veri noktaları oluştur
        const now = new Date();
        for (let i = 24; i >= 0; i--) {
          const time = new Date(now);
          time.setHours(now.getHours() - i);

          responseTimeData.push({
            timestamp: time.toISOString(),
            value: 0
          });
        }
      }

      setResponseTimeHistory(responseTimeData);
      setDashboardLoading(false);
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setDashboardError('Yanıt süresi verileri yüklenirken bir hata oluştu.');
      setDashboardLoading(false);
    }
  }, [devices, getDeviceStatus, monitorService]);

  // Verileri güncelleme fonksiyonu (sadece manuel kontrol için kullanılıyor)
  const refreshData = useCallback(async () => {
    try {
      // loadData fonksiyonunu çağırarak yanıt süresi verilerini güncelle
      await loadData();
    } catch (err) {
      console.error('Error updating dashboard data:', err);
    }
  }, [loadData]);

  // Sayfa yüklendiğinde sistem sağlığı verilerini yükle
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Otomatik yenileme kaldırıldı, artık Socket.io ile gerçek zamanlı güncellemeler kullanılıyor

  // Socket.io olaylarını dinle
  useEffect(() => {
    if (!socket) return;

    // Bildirim güncellemelerini dinle
    socket.on('notification:update', (data) => {
      console.log('Dashboard - notification:update olayı alındı:', data);
      // Bildirim güncellendiğinde verileri yenile
      refreshData();
    });

    // Monitor güncellemelerini dinle (yanıt süresi grafiği için)
    socket.on('monitor:update', (data) => {
      if (data.type === 'icmp') {
        // ICMP güncellemesi geldiğinde yanıt süresi verilerini güncelle
        refreshData();
      }
    });

    // Sistem sağlığı güncellemeleri artık SystemContext tarafından yönetiliyor

    return () => {
      socket.off('notification:update');
      socket.off('monitor:update');
      socket.off('system:health:update');
    };
  }, [socket, refreshData]);





  // İstatistikleri hesapla
  const stats = useMemo(() => {
    const total = devices.length;
    let online = 0;
    let offline = 0;
    let warning = 0;
    let critical = 0;
    let degraded = 0;
    let flapping = 0;
    let partial = 0;
    let unknown = 0;

    devices.forEach(device => {
      // Sadece backend'den gelen hesaplanmış durumu kullan
      const deviceStatus = getDeviceStatus(device.id);

      switch (deviceStatus) {
        case 'up':
          online++;
          break;
        case 'down':
          offline++;
          break;
        case 'warning':
          warning++;
          break;
        case 'critical':
          critical++;
          break;
        case 'degraded':
          degraded++;
          break;
        case 'flapping':
          flapping++;
          break;
        case 'partial':
          partial++;
          break;
        default:
          unknown++;
      }
    });

    return {
      total,
      online,
      offline,
      warning,
      critical,
      degraded,
      flapping,
      partial,
      unknown
    };
  }, [devices, getDeviceStatus]);

  // Smart loading - sayfa seviyesi
  if (pageLoading.shouldShowFullPageLoading()) {
    return (
      <SmartPageLoading
        loadingType="initial"
        pageTitle="Kontrol Paneli"
        skeletonLayout="dashboard"
      />
    );
  }

  if (error || dashboardError) {
    return (
      <div className="p-6 max-w-2xl mx-auto mt-10">
        <div className="bg-destructive/15 text-destructive p-6 rounded-lg mb-6 border border-destructive/30 shadow-sm">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-lg mb-2">Veri Yükleme Hatası</h3>
              <p>{error || dashboardError}</p>
            </div>
          </div>
        </div>
        <Button onClick={loadData} size="lg" className="w-full sm:w-auto">
          <RefreshCw className="mr-2 h-5 w-5" /> Yeniden Dene
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Başlık ve Kontroller */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Kontrol Paneli</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Sistem durumunu ve cihaz istatistiklerini tek bir bakışta görüntüleyin
          </p>
        </div>

        {/* Socket bağlantı durumu göstergesi kaldırıldı ve sistem servisleri durumuna taşındı */}
      </div>

      {/* Toplam Cihaz ve Hızlı Erişim Kartları - En üst sıra */}
      <div className="grid grid-cols-1 md:grid-cols-12 gap-4 mb-2">
        {/* Toplam Cihaz Kartı - Standart tasarım */}
        <div className="md:col-span-4">
          <StatusCard
            title="Toplam Cihaz"
            value={stats.total}
            icon={<Server className="h-5 w-5" />}
            description="İzlenen cihaz sayısı"
            className="h-full"
          />
        </div>

        {/* Hızlı Erişim Kartları - Daha kompakt */}
        <div className="md:col-span-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <QuickActionCard
            title="Cihazları Kontrol Et"
            description="Tüm cihazların durumunu kontrol et"
            icon={<RefreshCw className="h-6 w-6" />}
            onClick={() => {
              if (socket && socket.connected) {
                socket.emit('device:check', { deviceIds: [], monitorTypes: [] });
              }
            }}
          />

          <QuickActionCard
            title="Bildirimleri Görüntüle"
            description="Tüm bildirimleri listele"
            icon={<Bell className="h-6 w-6" />}
            onClick={() => navigate('/notifications')}
          />

          <QuickActionCard
            title="Sistem Durumu"
            description="Servis durumlarını kontrol et"
            icon={<Activity className="h-6 w-6" />}
            onClick={() => navigate('/settings/system')}
          />
        </div>
      </div>

      {/* Tüm Durum Kartları - İkinci sıra, önem sırasına göre boyutlandırılmış */}
      <div className="grid grid-cols-12 gap-3">
        {/* Ana durum kartları - Daha büyük */}
        <div className="col-span-6 md:col-span-3">
          <StatusCard
            title="Çevrimiçi"
            value={stats.online}
            icon={<CheckCircle className={`h-6 w-6 ${STATUS_COLORS[STATUS_TYPES.UP]}`} />}
            description="Çalışan cihazlar"
            status="success"
            className="h-full"
          />
        </div>

        <div className="col-span-6 md:col-span-3">
          <StatusCard
            title="Çevrimdışı"
            value={stats.offline}
            icon={<XCircle className={`h-6 w-6 ${STATUS_COLORS[STATUS_TYPES.DOWN]}`} />}
            description="Erişilemeyen cihazlar"
            status="error"
            className="h-full"
          />
        </div>

        {/* Orta öncelikli durum kartları */}
        <div className="col-span-6 md:col-span-3">
          <StatusCard
            title="Uyarı Veren"
            value={stats.warning}
            icon={<AlertCircle className={`h-6 w-6 ${STATUS_COLORS[STATUS_TYPES.WARNING]}`} />}
            description="Sorunlu cihazlar"
            status="warning"
            className="h-full"
          />
        </div>

        <div className="col-span-6 md:col-span-3">
          <StatusCard
            title="Kritik"
            value={stats.critical}
            icon={<AlertCircle className={`h-6 w-6 ${STATUS_COLORS[STATUS_TYPES.CRITICAL]}`} />}
            description="Kritik sorunlu cihazlar"
            status="critical"
            className="h-full"
          />
        </div>

        {/* Ek durum kartları - 6-durumlu cihaz sistemi */}
        <div className="col-span-6 md:col-span-3">
          <StatusCard
            title="Kararsız"
            value={stats.flapping}
            icon={<RefreshCw className={`h-5 w-5 ${STATUS_COLORS[STATUS_TYPES.FLAPPING]}`} />}
            description="Durumu sürekli değişen cihazlar"
            status="flapping"
            className="h-full"
          />
        </div>

        <div className="col-span-6 md:col-span-3">
          <StatusCard
            title="Bilinmiyor"
            value={stats.unknown}
            icon={<AlertCircle className={`h-5 w-5 ${STATUS_COLORS[STATUS_TYPES.UNKNOWN]}`} />}
            description="Durumu bilinmeyen cihazlar"
            status="unknown"
            className="h-full"
          />
        </div>
      </div>

      {/* Sistem Sağlığı ve Grafik */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Yanıt Süresi Grafiği */}
        <Card>
          <CardHeader>
            <CardTitle>Ortalama Yanıt Süresi</CardTitle>
            <CardDescription>Son 24 saat</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              {responseTimeHistory.length > 0 ? (
                <Line
                  data={{
                    labels: responseTimeHistory.map(item => {
                      // Timestamp'i formatla
                      const date = new Date(item.timestamp);
                      return date.getHours() + ':00';
                    }),
                    datasets: [
                      {
                        label: 'Yanıt Süresi (ms)',
                        data: responseTimeHistory.map(item => item.value),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgb(59, 130, 246)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 1,
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        tension: 0.3,
                        fill: true,
                      }
                    ]
                  }}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false,
                      },
                      tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(17, 24, 39, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(59, 130, 246, 0.3)',
                        borderWidth: 1,
                        padding: 10,
                        displayColors: false,
                      },
                    },
                    scales: {
                      x: {
                        grid: {
                          display: false,
                        },
                        ticks: {
                          maxRotation: 0,
                          autoSkip: true,
                          maxTicksLimit: 8,
                        },
                      },
                      y: {
                        beginAtZero: true,
                        grid: {
                          color: 'rgba(0, 0, 0, 0.05)',
                        },
                      },
                    },
                    interaction: {
                      mode: 'nearest',
                      axis: 'x',
                      intersect: false,
                    },
                  }}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                  <LineChartIcon className="h-10 w-10 mb-3 opacity-50" />
                  <span>Veri bulunamadı</span>
                  <p className="text-xs mt-2 text-center">Yanıt süresi verisi henüz mevcut değil</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Sistem Sağlığı Kartı */}
        <SystemHealthCard />
      </div>
    </div>
  );
};

export default Dashboard;
