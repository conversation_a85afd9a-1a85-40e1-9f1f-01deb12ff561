import React, { useState, useEffect } from 'react';
import { userService, settingsService } from '../services/api';
import { useSocket } from '../contexts/SocketContext';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { notificationService } from '../services/notification-service';
import { usePageLoading } from '../hooks/useSmartLoading';
import { SmartPageLoading } from '../components/ui/smart-loading';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../components/ui/alert-dialog';
// Form içi Alert bileşenleri kaldırıldı - sadece toast bildirimleri kullanılıyor
import { Users, Plus, Edit, Trash2, Info as InfoIcon, UserCog, Lock, Unlock, UserCheck, UserX, X, Key, Clock, AlertTriangle, CheckCircle } from 'lucide-react';
import { Checkbox } from '../components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { useAuth } from '../contexts/AuthContext';
import PaginationComponent from '../components/PaginationComponent';
import FloatingActionBar from '../components/ui/floating-action-bar';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  // Form içi error state kaldırıldı - sadece toast bildirimleri kullanılıyor

  // Smart loading hook
  const pageLoading = usePageLoading(users, loading);
  const [success, setSuccess] = useState('');

  const [editUser, setEditUser] = useState(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  // Rol değiştirme dialog'u için state
  const [bulkRoleDialog, setBulkRoleDialog] = useState({
    open: false,
    role: 'user'
  });

  // Yeni kullanıcı ekleme state'leri
  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user'
  });
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  // addUserError state kaldırıldı - sadece toast bildirimleri kullanılıyor

  // Seçim state'leri
  const [selectedUsers, setSelectedUsers] = useState([]);

  // Sayfalandırma state'leri
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const { user: currentUser } = useAuth();
  const socket = useSocket();

  // Kullanıcıları yükle
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const data = await userService.getAll();
        setUsers(data);
      } catch (err) {
        console.error('Kullanıcıları getirme hatası:', err);

        // Hata bildirimi göster
        notificationService.error('Kullanıcılar yüklenemedi', {
          description: 'Kullanıcılar yüklenirken bir hata oluştu'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Socket event'lerini dinle
  useEffect(() => {
    if (!socket) return;

    // Kullanıcı durum değişikliklerini dinle
    const handleUserStatusUpdate = (data) => {
      console.log('Kullanıcı durum güncellemesi:', data);

      setUsers(prev => prev.map(user => {
        if (user.id === data.userId) {
          return {
            ...user,
            status: data.status,
            locked: data.locked,
            lockedUntil: data.lockedUntil
          };
        }
        return user;
      }));

      // Socket event'i ile kullanıcı durumu güncellendi
      // Backend'de zaten bildirim oluşturuluyor, burada ek toast göstermeye gerek yok

      // Sadece otomatik kilitleme için toast göster (bu önemli bir güvenlik olayı)
      if (data.action === 'auto_lock') {
        notificationService.warning('Kullanıcı Otomatik Kilitlendi', {
          description: `${data.username} kullanıcısı çok fazla başarısız giriş denemesi nedeniyle kilitlendi.`,
          category: 'user'
        });
      }
    };

    // Kullanıcı oluşturma
    const handleUserCreated = (data) => {
      console.log('Yeni kullanıcı oluşturuldu:', data);
      setUsers(prev => [...prev, data.user]);
      // Backend'de zaten bildirim oluşturuluyor, burada ek toast göstermeye gerek yok
    };

    // Kullanıcı güncelleme
    const handleUserUpdated = (data) => {
      console.log('Kullanıcı güncellendi:', data);
      setUsers(prev => prev.map(user =>
        user.id === data.user.id ? { ...user, ...data.user } : user
      ));
      // Backend'de zaten bildirim oluşturuluyor, burada ek toast göstermeye gerek yok
    };

    // Kullanıcı silme
    const handleUserDeleted = (data) => {
      console.log('Kullanıcı silindi:', data);
      setUsers(prev => prev.filter(user => user.id !== data.userId));
      // Backend'de zaten bildirim oluşturuluyor, burada ek toast göstermeye gerek yok
    };

    // Event listener'ları ekle
    socket.on('user:status:update', handleUserStatusUpdate);
    socket.on('user:created', handleUserCreated);
    socket.on('user:updated', handleUserUpdated);
    socket.on('user:deleted', handleUserDeleted);

    // Cleanup
    return () => {
      socket.off('user:status:update', handleUserStatusUpdate);
      socket.off('user:created', handleUserCreated);
      socket.off('user:updated', handleUserUpdated);
      socket.off('user:deleted', handleUserDeleted);
    };
  }, [socket]);

  // Kullanıcı düzenleme
  const handleEditUser = (user) => {
    setEditUser({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    });
    setIsEditDialogOpen(true);
  };

  // Kullanıcı silme
  const handleDeleteUser = (user) => {
    setUserToDelete(user);
    setIsDeleteDialogOpen(true);
  };

  // Yeni kullanıcı ekleme
  const handleAddUser = () => {
    setNewUser({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'user'
    });
    // Dialog açılıyor
    setIsAddDialogOpen(true);
  };

  // Kullanıcı düzenleme formunu gönder
  const handleEditSubmit = async (e) => {
    e.preventDefault();

    try {
      const updatedUser = await userService.update(editUser.id, editUser);

      // Kullanıcı listesini güncelle
      setUsers(users.map(user => user.id === updatedUser.id ? updatedUser : user));

      setSuccess('Kullanıcı başarıyla güncellendi');
      setIsEditDialogOpen(false);

      // Başarı bildirimi göster
      notificationService.success('Kullanıcı güncellendi', {
        description: `${editUser.username} kullanıcısı başarıyla güncellendi.`,
        persistent: true,
        category: 'user'
      });

      // Sayfadaki başarı mesajını göstermeyi kaldırdık
      // 3 saniye sonra başarı mesajını temizle
      // setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      // Hata bildirimi göster
      notificationService.error('Kullanıcı güncellenemedi', {
        description: err.response?.data?.error || 'Kullanıcı güncellenirken bir hata oluştu'
      });
    }
  };

  // Yeni kullanıcı ekleme formunu gönder
  const handleAddSubmit = async (e) => {
    e.preventDefault();

    // Form validasyonu
    if (!newUser.username.trim() || !newUser.email.trim() || !newUser.password || !newUser.confirmPassword) {
      notificationService.error('Eksik bilgi', {
        description: 'Tüm alanları doldurun'
      });
      return;
    }

    if (newUser.password !== newUser.confirmPassword) {
      notificationService.error('Şifre hatası', {
        description: 'Şifreler eşleşmiyor'
      });
      return;
    }

    // Şifre politikası kontrolü backend'de yapılıyor

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newUser.email)) {
      notificationService.error('E-posta hatası', {
        description: 'Geçerli bir e-posta adresi girin'
      });
      return;
    }

    try {
      // Şifre doğrulama alanını kaldır
      const { confirmPassword, ...userData } = newUser;

      const response = await userService.create(userData);

      // Kullanıcı listesine ekle
      setUsers([...users, response.user]);

      setSuccess('Kullanıcı başarıyla oluşturuldu');
      setIsAddDialogOpen(false);

      // Başarı bildirimi göster
      notificationService.success('Kullanıcı oluşturuldu', {
        description: `${newUser.username} kullanıcısı başarıyla oluşturuldu.`,
        persistent: true,
        category: 'user'
      });

      // Form'u temizle
      setNewUser({
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        role: 'user'
      });

    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Kullanıcı oluşturulurken bir hata oluştu';

      // Hata bildirimi göster
      notificationService.error('Kullanıcı oluşturulamadı', {
        description: errorMessage
      });
    }
  };

  // Kullanıcı silme işlemini onayla
  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      if (selectedUsers.length > 0 && !userToDelete.id) {
        // Toplu silme
        for (const userId of selectedUsers) {
          await userService.delete(userId);
        }

        // Kullanıcı listesinden sil
        setUsers(users.filter(user => !selectedUsers.includes(user.id)));

        setSuccess(`${selectedUsers.length} kullanıcı başarıyla silindi`);
        setIsDeleteDialogOpen(false);

        // Başarı bildirimi göster
        notificationService.success('Kullanıcılar silindi', {
          description: `${selectedUsers.length} kullanıcı başarıyla silindi.`,
          persistent: true,
          category: 'user'
        });

        setSelectedUsers([]);
      } else {
        // Tek kullanıcı silme
        await userService.delete(userToDelete.id);

        // Kullanıcı listesinden sil
        setUsers(users.filter(user => user.id !== userToDelete.id));

        setSuccess('Kullanıcı başarıyla silindi');
        setIsDeleteDialogOpen(false);

        // Başarı bildirimi göster
        notificationService.success('Kullanıcı silindi', {
          description: `${userToDelete.username} kullanıcısı başarıyla silindi.`,
          persistent: true,
          category: 'user'
        });
      }

      setUserToDelete(null);

    } catch (err) {
      // Hata bildirimi göster
      notificationService.error('Kullanıcı silinemedi', {
        description: err.response?.data?.error || 'Kullanıcı silinirken bir hata oluştu'
      });
    }
  };

  // Tek bir kullanıcıyı seçme/seçimi kaldırma
  const handleSelectUser = (userId, checked) => {
    if (checked === true) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  // Tüm kullanıcıları seçme/seçimi kaldırma
  const handleSelectAll = (checked) => {
    if (checked === true) {
      // Mevcut kullanıcının kendisini seçime dahil etme
      const selectableUsers = users.filter(user => user.id !== currentUser.id);
      setSelectedUsers(selectableUsers.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  // Toplu silme işlemi
  const handleBulkDelete = () => {
    setUserToDelete({
      id: null,
      username: `${selectedUsers.length} kullanıcı`,
      email: ''
    });
    setIsDeleteDialogOpen(true);
  };

  // Toplu düzenleme işlemi (tek kullanıcı seçiliyse)
  const handleBulkEdit = () => {
    if (selectedUsers.length === 1) {
      const user = users.find(u => u.id === selectedUsers[0]);
      if (user) {
        handleEditUser(user);
      }
    }
  };

  // Toplu rol değiştirme işlemi
  const handleBulkRoleChange = async () => {
    try {
      // Seçili kullanıcıları güncelle
      for (const userId of selectedUsers) {
        await userService.update(userId, { role: bulkRoleDialog.role });
      }

      // Kullanıcı listesini yerel olarak güncelle
      setUsers(prev => prev.map(user => {
        if (selectedUsers.includes(user.id)) {
          return { ...user, role: bulkRoleDialog.role };
        }
        return user;
      }));

      setBulkRoleDialog({ open: false, role: 'user' });
      setSelectedUsers([]);

      // Başarı bildirimi göster
      notificationService.success('Roller güncellendi', {
        description: `${selectedUsers.length} kullanıcının rolü ${bulkRoleDialog.role === 'admin' ? 'Yönetici' : 'Kullanıcı'} olarak güncellendi.`,
        persistent: true,
        category: 'user'
      });

    } catch (err) {
      // Hata bildirimi göster
      notificationService.error('Roller güncellenemedi', {
        description: err.response?.data?.error || 'Kullanıcı rolleri güncellenirken bir hata oluştu'
      });
    }
  };

  // Toplu kullanıcı kilidini kaldırma işlemi (sadece geçici kilitler)
  const handleBulkUnlock = async () => {
    try {
      // Seçili kullanıcıların kilidini kaldır
      for (const userId of selectedUsers) {
        await userService.unlock(userId);
      }

      // Kullanıcı listesini yerel olarak güncelle
      setUsers(prev => prev.map(user => {
        if (selectedUsers.includes(user.id)) {
          return {
            ...user,
            locked: false,
            lockedUntil: null
          };
        }
        return user;
      }));

      setSelectedUsers([]);

      // Backend'de zaten bildirim oluşturuluyor, burada ek toast göstermeye gerek yok

    } catch (err) {
      // Hata bildirimi göster
      notificationService.error('Kilitler kaldırılamadı', {
        description: err.response?.data?.error || 'Kullanıcı kilitleri kaldırılırken bir hata oluştu'
      });
    }
  };

  // Toplu kullanıcı devre dışı bırakma işlemi
  const handleBulkDisable = async () => {
    try {
      // Seçili kullanıcıları devre dışı bırak
      for (const userId of selectedUsers) {
        await userService.disable(userId);
      }

      // Kullanıcı listesini yerel olarak güncelle
      setUsers(prev => prev.map(user => {
        if (selectedUsers.includes(user.id)) {
          return {
            ...user,
            status: 'disabled'
          };
        }
        return user;
      }));

      setSelectedUsers([]);

      // Backend'de zaten bildirim oluşturuluyor, burada ek toast göstermeye gerek yok

    } catch (err) {
      // Hata bildirimi göster
      notificationService.error('Kullanıcılar devre dışı bırakılamadı', {
        description: err.response?.data?.error || 'Kullanıcılar devre dışı bırakılırken bir hata oluştu'
      });
    }
  };

  // Toplu kullanıcı aktif etme işlemi
  const handleBulkEnable = async () => {
    try {
      // Seçili kullanıcıları aktif et
      for (const userId of selectedUsers) {
        await userService.enable(userId);
      }

      // Kullanıcı listesini yerel olarak güncelle
      setUsers(prev => prev.map(user => {
        if (selectedUsers.includes(user.id)) {
          return {
            ...user,
            status: 'active'
          };
        }
        return user;
      }));

      setSelectedUsers([]);

      // Backend'de zaten bildirim oluşturuluyor, burada ek toast göstermeye gerek yok

    } catch (err) {
      // Hata bildirimi göster
      notificationService.error('Kullanıcılar aktif edilemedi', {
        description: err.response?.data?.error || 'Kullanıcılar aktif edilirken bir hata oluştu'
      });
    }
  };

  // Tarih formatla
  const formatDate = (timestamp) => {
    if (!timestamp) return 'Bilinmiyor';
    return new Date(parseInt(timestamp)).toLocaleString('tr-TR');
  };

  // Şifre geçerlilik durumunu formatla
  const formatPasswordExpiry = (passwordExpiry) => {
    if (!passwordExpiry) {
      return {
        text: 'Bilinmiyor',
        icon: <Clock className="h-4 w-4 text-gray-400" />,
        className: 'text-gray-500',
        tooltip: 'Şifre geçerlilik bilgisi bulunamadı'
      };
    }

    const { daysUntilExpiry, isExpired, isExpiringSoon, expiryDate } = passwordExpiry;

    // Null veya undefined kontrolü
    if (daysUntilExpiry === null || daysUntilExpiry === undefined) {
      return {
        text: 'Bilinmiyor',
        icon: <Clock className="h-4 w-4 text-gray-400" />,
        className: 'text-gray-500',
        tooltip: 'Şifre geçerlilik süresi hesaplanamadı'
      };
    }

    // Süresiz şifre
    if (daysUntilExpiry === Infinity) {
      return {
        text: 'Süresiz',
        icon: <CheckCircle className="h-4 w-4 text-gray-400" />,
        className: 'text-gray-500',
        tooltip: 'Şifre geçerlilik süresi belirlenmemiş'
      };
    }

    // Süresi dolmuş
    if (isExpired) {
      return {
        text: 'Süresi Dolmuş',
        icon: <AlertTriangle className="h-4 w-4 text-red-500" />,
        className: 'text-red-600 font-medium',
        tooltip: `Şifre süresi dolmuş. Son geçerlilik: ${expiryDate ? new Date(expiryDate).toLocaleDateString('tr-TR') : 'Bilinmiyor'}`
      };
    }

    // Yakında dolacak (7 gün içinde)
    if (isExpiringSoon) {
      return {
        text: `${daysUntilExpiry} gün kaldı`,
        icon: <Clock className="h-4 w-4 text-orange-500" />,
        className: 'text-orange-600 font-medium',
        tooltip: `Şifre ${daysUntilExpiry} gün sonra dolacak. Son geçerlilik: ${expiryDate ? new Date(expiryDate).toLocaleDateString('tr-TR') : 'Bilinmiyor'}`
      };
    }

    // Normal durum
    return {
      text: `${daysUntilExpiry} gün kaldı`,
      icon: <Key className="h-4 w-4 text-green-500" />,
      className: 'text-green-600',
      tooltip: `Şifre ${daysUntilExpiry} gün sonra dolacak. Son geçerlilik: ${expiryDate ? new Date(expiryDate).toLocaleDateString('tr-TR') : 'Bilinmiyor'}`
    };
  };

  // Sayfalandırma için hesaplamalar
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = users.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(users.length / itemsPerPage);

  // Sayfa değiştirme fonksiyonu
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Sayfa başına öğe sayısını değiştirme fonksiyonu
  const handlePageSizeChange = (newPageSize) => {
    // Yeni sayfa boyutuna göre mevcut sayfayı ayarla
    const newTotalPages = Math.ceil(users.length / newPageSize);
    const newCurrentPage = Math.min(currentPage, newTotalPages);

    setItemsPerPage(newPageSize);
    setCurrentPage(newCurrentPage);
  };

  // Smart loading - sayfa seviyesi
  if (pageLoading.shouldShowFullPageLoading()) {
    return (
      <SmartPageLoading
        loadingType="initial"
        pageTitle="Kullanıcı Yönetimi"
        skeletonLayout="table"
      />
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Kullanıcı Yönetimi</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Kullanıcıları yönetin ve yetkilendirin
          </p>

        </div>
        <div className="flex items-center gap-2 self-end sm:self-auto">
          <Button size="sm" onClick={handleAddUser}>
            <Plus className="mr-2 h-4 w-4" /> Kullanıcı Ekle
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="flex flex-col">
              <CardTitle>Kullanıcı Listesi</CardTitle>
              <p className="text-xs text-muted-foreground mt-2">
                <InfoIcon className="inline-block h-3 w-3 mr-1" /> Kullanıcıları seçerek toplu işlemler yapabilirsiniz
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto w-full">
              <Table className="table-auto w-full">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={users.filter(user => user.id !== currentUser.id).length > 0 &&
                           selectedUsers.length === users.filter(user => user.id !== currentUser.id).length}
                  onCheckedChange={handleSelectAll}
                  disabled={users.filter(user => user.id !== currentUser.id).length === 0}
                />
              </TableHead>
              <TableHead>Kullanıcı Adı</TableHead>
              <TableHead>E-posta</TableHead>
              <TableHead>Rol</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Kilitleme</TableHead>
              <TableHead>Şifre Geçerlilik</TableHead>
              <TableHead>Kayıt Tarihi</TableHead>
              <TableHead>Son Güncelleme</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-4">
                  Henüz kullanıcı bulunmuyor
                </TableCell>
              </TableRow>
            ) : (
              currentItems.map((user) => (
                <TableRow
                  key={user.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={(e) => {
                    // Checkbox'a veya butonlara tıklandığında satır tıklamasını engelle
                    if (e.target.type === 'checkbox' || e.target.closest('button') || e.target.closest('[role="checkbox"]')) {
                      return;
                    }
                    // Kullanıcı detay sayfası yoksa şimdilik edit dialog'unu aç
                    handleEditUser(user);
                  }}
                >
                  <TableCell onClick={(e) => e.stopPropagation()}>
                    {user.id !== currentUser.id ? (
                      <Checkbox
                        checked={selectedUsers.includes(user.id)}
                        onCheckedChange={(checked) => handleSelectUser(user.id, checked)}
                      />
                    ) : (
                      <div className="w-4 h-4" /> // Boş alan (kendini seçemesin)
                    )}
                  </TableCell>
                  <TableCell className="font-medium">{user.username}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      user.role === 'admin' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.role === 'admin' ? 'Yönetici' : 'Kullanıcı'}
                    </span>
                  </TableCell>
                  {/* Durum Sütunu */}
                  <TableCell>
                    {user.status === 'disabled' ? (
                      <div className="flex items-center gap-1">
                        <UserX className="h-4 w-4 text-red-500" />
                        <span className="text-xs text-red-600 font-medium">Devre Dışı</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1">
                        <UserCheck className="h-4 w-4 text-green-500" />
                        <span className="text-xs text-green-600">Aktif</span>
                      </div>
                    )}
                  </TableCell>

                  {/* Kilitleme Sütunu */}
                  <TableCell>
                    {user.locked && user.lockedUntil ? (
                      <div className="flex items-center gap-1">
                        <Lock className="h-4 w-4 text-orange-500" />
                        <span className="text-xs text-orange-600">
                          Kilitli
                          <div className="text-xs text-muted-foreground">
                            {new Date(user.lockedUntil).toLocaleString('tr-TR')} kadar
                          </div>
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1">
                        <Unlock className="h-4 w-4 text-gray-400" />
                        <span className="text-xs text-gray-500">Açık</span>
                      </div>
                    )}
                  </TableCell>

                  {/* Şifre Geçerlilik Sütunu */}
                  <TableCell>
                    {(() => {
                      const expiryInfo = formatPasswordExpiry(user.passwordExpiry);
                      return (
                        <div
                          className="flex items-center gap-1 cursor-help"
                          title={expiryInfo.tooltip}
                        >
                          {expiryInfo.icon}
                          <span className={`text-xs ${expiryInfo.className}`}>
                            {expiryInfo.text}
                          </span>
                        </div>
                      );
                    })()}
                  </TableCell>

                  <TableCell>{formatDate(user.createdAt)}</TableCell>
                  <TableCell>{formatDate(user.updatedAt)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
              </Table>
            </div>
          </div>

          {/* Sayfalandırma */}
          <div className="mt-4">
            <PaginationComponent
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={users.length}
              onPageChange={paginate}
              pageSize={itemsPerPage}
              onPageSizeChange={handlePageSizeChange}
              pageSizeOptions={[5, 10, 25, 50, 100]}
              showPageSizeOptions={true}
            />
          </div>
        </CardContent>
      </Card>

      {/* Kullanıcı Düzenleme Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Kullanıcı Düzenle</DialogTitle>
            <DialogDescription>
              Kullanıcı bilgilerini güncelleyin
            </DialogDescription>
          </DialogHeader>

          {editUser && (
            <form onSubmit={handleEditSubmit}>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Kullanıcı Adı</Label>
                  <Input
                    id="username"
                    value={editUser.username}
                    onChange={(e) => setEditUser({ ...editUser, username: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-posta</Label>
                  <Input
                    id="email"
                    type="email"
                    value={editUser.email}
                    onChange={(e) => setEditUser({ ...editUser, email: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Rol</Label>
                  <Select
                    value={editUser.role}
                    onValueChange={(value) => setEditUser({ ...editUser, role: value })}
                  >
                    <SelectTrigger id="role">
                      <SelectValue placeholder="Rol seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">Kullanıcı</SelectItem>
                      <SelectItem value="admin">Yönetici</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  İptal
                </Button>
                <Button type="submit">Kaydet</Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* Yeni Kullanıcı Ekleme Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Kullanıcı Ekle</DialogTitle>
            <DialogDescription>
              Yeni bir kullanıcı hesabı oluşturun
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleAddSubmit}>
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="new-username">Kullanıcı Adı</Label>
                <Input
                  id="new-username"
                  value={newUser.username}
                  onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
                  placeholder="Kullanıcı adını girin"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-email">E-posta</Label>
                <Input
                  id="new-email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  placeholder="E-posta adresini girin"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-password">Şifre</Label>
                <Input
                  id="new-password"
                  type="password"
                  value={newUser.password}
                  onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                  placeholder="Şifreyi girin"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-confirm-password">Şifre Tekrar</Label>
                <Input
                  id="new-confirm-password"
                  type="password"
                  value={newUser.confirmPassword}
                  onChange={(e) => setNewUser({ ...newUser, confirmPassword: e.target.value })}
                  placeholder="Şifreyi tekrar girin"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-role">Rol</Label>
                <Select
                  value={newUser.role}
                  onValueChange={(value) => setNewUser({ ...newUser, role: value })}
                >
                  <SelectTrigger id="new-role">
                    <SelectValue placeholder="Rol seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">Kullanıcı</SelectItem>
                    <SelectItem value="admin">Yönetici</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                İptal
              </Button>
              <Button type="submit">Kullanıcı Oluştur</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Rol Değiştirme Dialog */}
      <Dialog open={bulkRoleDialog.open} onOpenChange={(open) => !open && setBulkRoleDialog({ ...bulkRoleDialog, open: false })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rol Değiştir</DialogTitle>
            <DialogDescription>
              Seçili {selectedUsers.length} kullanıcı için rol değiştir
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="bulkRole">Rol</Label>
                <Select
                  value={bulkRoleDialog.role}
                  onValueChange={(value) => setBulkRoleDialog({ ...bulkRoleDialog, role: value })}
                >
                  <SelectTrigger id="bulkRole">
                    <SelectValue placeholder="Rol seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-2" /> Kullanıcı
                      </div>
                    </SelectItem>
                    <SelectItem value="admin">
                      <div className="flex items-center">
                        <UserCog className="h-4 w-4 mr-2" /> Yönetici
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setBulkRoleDialog({ ...bulkRoleDialog, open: false })}>
              İptal
            </Button>
            <Button onClick={handleBulkRoleChange}>
              Uygula
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Kullanıcı Silme Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {selectedUsers.length > 0 && !userToDelete?.id ? 'Kullanıcıları Sil' : 'Kullanıcıyı Sil'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz.
              {selectedUsers.length > 0 && !userToDelete?.id ? (
                <>
                  <br />
                  <strong>{selectedUsers.length} kullanıcıyı</strong> silmek istediğinizden emin misiniz?
                </>
              ) : (
                <>
                  Bu kullanıcıyı silmek istediğinizden emin misiniz?
                  <br />
                  <strong>{userToDelete?.username}</strong> ({userToDelete?.email})
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteUser} className="bg-destructive text-destructive-foreground">
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Floating Action Bar */}
      <FloatingActionBar
        selectedCount={selectedUsers.length}
        selectedLabel="kullanıcı"
        onClearSelection={() => setSelectedUsers([])}
      >
        {/* Düzenle butonu - sadece tek seçim */}
        {selectedUsers.length === 1 && (
          <Button
            variant="outline"
            size="default"
            onClick={handleBulkEdit}
          >
            <Edit className="mr-2 h-4 w-4" /> Düzenle
          </Button>
        )}

        {/* Rol değiştir */}
        <Button
          variant="outline"
          size="default"
          onClick={() => setBulkRoleDialog({ open: true, role: 'user' })}
        >
          <UserCog className="mr-2 h-4 w-4" /> Rol
        </Button>

        {/* Kilit Aç butonu - sadece kilitli kullanıcılar seçiliyse göster */}
        {selectedUsers.some(userId => {
          const user = users.find(u => u.id === userId);
          return user && user.locked;
        }) && (
          <Button
            variant="outline"
            size="default"
            onClick={handleBulkUnlock}
            className="text-blue-600 border-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-400 dark:hover:bg-blue-950"
          >
            <Unlock className="mr-2 h-4 w-4" /> Kilit Aç
          </Button>
        )}

        {/* Aktif Et butonu - sadece devre dışı kullanıcılar seçiliyse göster */}
        {selectedUsers.some(userId => {
          const user = users.find(u => u.id === userId);
          return user && user.status === 'disabled';
        }) && (
          <Button
            variant="success"
            size="default"
            onClick={handleBulkEnable}
          >
            <UserCheck className="mr-2 h-4 w-4" /> Aktif Et
          </Button>
        )}

        {/* Devre Dışı Bırak butonu - sadece aktif kullanıcılar seçiliyse göster */}
        {selectedUsers.some(userId => {
          const user = users.find(u => u.id === userId);
          return user && user.status === 'active';
        }) && (
          <Button
            variant="warning"
            size="default"
            onClick={handleBulkDisable}
          >
            <UserX className="mr-2 h-4 w-4" /> Devre Dışı
          </Button>
        )}

        {/* Sil butonu */}
        <Button
          variant="destructive"
          size="default"
          onClick={handleBulkDelete}
        >
          <Trash2 className="mr-2 h-4 w-4" /> Sil
        </Button>
      </FloatingActionBar>
    </div>
  );
};

export default UserManagement;
