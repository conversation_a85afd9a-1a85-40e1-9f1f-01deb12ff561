import React, { useState, useCallback, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../components/ui/alert-dialog';
import DeviceEditDialog from '../components/DeviceEditDialog';
import MonitorSettingsSheet from '../components/MonitorSettingsSheet';
import { ArrowLeft, RefreshCw } from 'lucide-react';

// API servisleri
import { deviceService } from '../services/api';
import { notificationService } from '../services/notification-service';

// Context
import { useDevices } from '../contexts/DeviceContext';

// Custom hooks
import { useMonitorStatus } from '../hooks/useMonitorStatus';

// Sub-components
import DeviceInfo from '../components/DeviceDetail/DeviceInfo';
import DeviceStatus from '../components/DeviceDetail/DeviceStatus';
import DeviceHistory from '../components/DeviceDetail/DeviceHistory';

/**
 * DeviceDetail Page Component
 * Refactored for better maintainability and performance
 */
const DeviceDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { loadData } = useDevices();

  // Custom hooks
  const {
    device,
    loading: statusLoading,
    error: statusError,
    getOverallStatus,
    isMonitorEnabled
  } = useMonitorStatus(id);

  // Local state - minimal UI control only
  const [activeTab, setActiveTab] = useState('status');
  const [settingsSheet, setSettingsSheet] = useState({
    open: false,
    monitorType: null
  });
  const [editDialog, setEditDialog] = useState({ open: false });
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    deviceName: '',
    deviceId: null
  });

  // Memoized values for performance
  const overallStatus = useMemo(() => getOverallStatus(), [getOverallStatus]);

  // Event handlers
  const handleBackClick = useCallback(() => {
    navigate('/');
  }, [navigate]);

  const handleRefresh = useCallback(async () => {
    try {
      await loadData();
      notificationService.showSuccess('Cihaz durumu güncellendi');
    } catch (error) {
      console.error('Error refreshing device:', error);
      notificationService.showError('Cihaz durumu güncellenirken hata oluştu');
    }
  }, [loadData]);

  const handleEditClick = useCallback(() => {
    setEditDialog({ open: true });
  }, []);

  const handleDeleteClick = useCallback(() => {
    if (!device) return;
    setDeleteDialog({
      open: true,
      deviceName: device.name,
      deviceId: device.id
    });
  }, [device]);

  const handleDelete = useCallback(async () => {
    if (!deleteDialog.deviceId) return;

    try {
      await deviceService.deleteDevice(deleteDialog.deviceId);
      notificationService.showSuccess(`${deleteDialog.deviceName} cihazı silindi`);
      setDeleteDialog({ open: false, deviceName: '', deviceId: null });
      navigate('/');
    } catch (error) {
      console.error('Error deleting device:', error);
      notificationService.showError('Cihaz silinirken hata oluştu');
    }
  }, [deleteDialog, navigate]);

  const handleSettingsClick = useCallback((monitorType) => {
    setSettingsSheet({
      open: true,
      monitorType
    });
  }, []);

  const handleMonitorToggle = useCallback(async (monitorType, enabled) => {
    if (!device) return;

    console.log('🔧 handleMonitorToggle called:', { monitorType, enabled, device: device?.name });

    try {
      const updatedMonitors = {
        ...device.monitors,
        [monitorType]: {
          ...device.monitors?.[monitorType],
          enabled
        }
      };

      console.log('📤 Sending API request with monitors:', updatedMonitors);

      await deviceService.update(device.id, {
        name: device.name,
        host: device.host,
        description: device.description || '',
        group: device.group,
        location: device.location || '',
        platform: device.platform,
        monitors: updatedMonitors,
        alerts: device.alerts || []
      });

      console.log('✅ API request successful, calling loadData...');
      await loadData();
      console.log('✅ loadData completed');

      notificationService.showSuccess(
        `${monitorType.toUpperCase()} izleme ${enabled ? 'etkinleştirildi' : 'devre dışı bırakıldı'}`
      );
    } catch (error) {
      console.error('❌ Error toggling monitor:', error);
      notificationService.showError('İzleme ayarı güncellenirken hata oluştu');
    }
  }, [device, loadData]);

  // Loading state
  if (statusLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={handleBackClick}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Geri
            </Button>
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Cihaz Detayları</h1>
              <p className="text-muted-foreground mt-1 text-base">
                Cihaz bilgileri yükleniyor...
              </p>
            </div>
          </div>
        </div>
        <div className="text-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <span className="text-lg text-muted-foreground">Cihaz bilgileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (statusError || !device) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={handleBackClick}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Geri
            </Button>
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Cihaz Detayları</h1>
              <p className="text-muted-foreground mt-1 text-base">
                Hata oluştu
              </p>
            </div>
          </div>
        </div>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-destructive mb-2">Hata</h2>
          <p className="text-muted-foreground">
            {statusError || 'Cihaz bulunamadı'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header - DeviceList ile tutarlı */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={handleBackClick}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Geri
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{device?.name || 'Cihaz Detayları'}</h1>
            <p className="text-muted-foreground mt-1 text-base">
              Cihaz durumunu izleyin ve ayarlarını yönetin
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 self-end sm:self-auto">
          <Button
            variant="outline"
            onClick={handleRefresh}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Kontrol Et
          </Button>
        </div>
      </div>

      {/* Main Content - DeviceList ile tutarlı grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Device Info */}
        <div className="lg:col-span-1">
          <DeviceInfo
            device={device}
            overallStatus={overallStatus}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteClick}
            onSettingsClick={handleSettingsClick}
            onMonitorToggle={handleMonitorToggle}
            isMonitorEnabled={isMonitorEnabled}
          />
        </div>

        {/* Right Column - Status and History */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="status">Durum</TabsTrigger>
              <TabsTrigger value="history">Geçmiş</TabsTrigger>
            </TabsList>

            <TabsContent value="status" className="mt-6">
              <DeviceStatus
                deviceId={id}
                onSettingsClick={handleSettingsClick}
              />
            </TabsContent>

            <TabsContent value="history" className="mt-6">
              <DeviceHistory
                deviceId={id}
                device={device}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Dialogs */}
      <AlertDialog 
        open={deleteDialog.open} 
        onOpenChange={(open) => !open && setDeleteDialog({ ...deleteDialog, open: false })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cihazı Sil</AlertDialogTitle>
            <AlertDialogDescription>
              <strong>{deleteDialog.deviceName}</strong> cihazını silmek istediğinize emin misiniz? 
              Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialog({ ...deleteDialog, open: false })}>
              İptal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground"
            >
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <MonitorSettingsSheet
        open={settingsSheet.open}
        onOpenChange={(open) => setSettingsSheet({ ...settingsSheet, open })}
        monitorType={settingsSheet.monitorType}
        deviceId={id}
        currentSettings={device?.monitors?.[settingsSheet.monitorType] || {}}
        onSuccess={loadData}
      />

      <DeviceEditDialog
        open={editDialog.open}
        onOpenChange={(open) => setEditDialog({ open })}
        device={device}
        onSuccess={loadData}
      />
    </div>
  );
};

export default DeviceDetail;
