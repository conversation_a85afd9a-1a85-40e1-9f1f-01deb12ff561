import React, { useState } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
// Form içi hata mesajları kaldırıldığı için Alert bileşenleri artık gerekli değil
import { LogIn } from 'lucide-react';
import { notificationService } from '../services/notification-service';


const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  // Form içi hata mesajları kaldırıldı - sadece toast bildirimleri kullanılıyor
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Kullanıcı başka bir sayfadan yönlendirildiyse, o sayfaya geri dön
  const from = location.state?.from?.pathname || '/';

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!username.trim() || !password) {
      notificationService.error('Eksik bilgi', {
        description: 'Kullanıcı adı ve şifre gereklidir'
      });
      return;
    }
    setIsSubmitting(true);

    try {
      console.log('Login attempt with:', { username, password });
      const result = await login(username, password);
      console.log('Login result:', result);

      // Başarılı giriş sonrası son aktivite zamanını güncelle
      // Bu, giriş yapar yapmaz oturum zaman aşımı uyarısı alınmasını önler
      const resetLastActivity = () => {
        // Debug log kaldırıldı
        // Kullanıcı aktivitesi olaylarını tetikle
        document.dispatchEvent(new MouseEvent('mousemove'));
      };

      // Giriş sonrası son aktivite zamanını güncelle
      resetLastActivity();

      // Başarılı giriş bildirimi göster
      notificationService.success('Giriş başarılı', {
        description: `Hoş geldiniz, ${username}!`
      });

      navigate(from, { replace: true });
    } catch (err) {
      console.error('Login error:', err);
      const errorMessage = err.response?.data?.error || 'Giriş yapılamadı. Lütfen bilgilerinizi kontrol edin.';
      const errorCode = err.response?.data?.code;

      // Hesap durumu uyarıları için özel toast bildirimleri
      if (errorCode === 'ACCOUNT_LOCKED') {
        // Hata bildirimini göster
        notificationService.error('Hesap kilitlendi', {
          description: errorMessage,
          persistent: true,
          category: 'security'
        });
      } else if (errorCode === 'ACCOUNT_DISABLED') {
        // Hesap devre dışı bildirimi
        notificationService.error('Hesap devre dışı', {
          description: errorMessage,
          persistent: true,
          category: 'security'
        });
      } else {
        // Genel hata bildirimi - kalıcı olarak kaydet ve güvenlik kategorisine ekle
        notificationService.error('Giriş başarısız', {
          description: errorMessage,
          persistent: true,
          category: 'security'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">

      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold flex items-center gap-2">
            <LogIn className="h-6 w-6" />
            Giriş Yap
          </CardTitle>
          <CardDescription>
            NetWatch'a erişmek için giriş yapın
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Kullanıcı Adı</Label>
              <Input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Kullanıcı adınızı girin"
                disabled={isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Şifre</Label>
                <Link to="/forgot-password" className="text-sm text-primary hover:underline">
                  Şifremi Unuttum
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Şifrenizi girin"
                disabled={isSubmitting}
              />
            </div>

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? 'Giriş Yapılıyor...' : 'Giriş Yap'}
            </Button>
          </form>
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center text-muted-foreground">
            Hesabınız yok mu?{' '}
            <Link to="/register" className="text-primary hover:underline">
              Kayıt Ol
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Login;
