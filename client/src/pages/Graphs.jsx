import React, { useState, useEffect, useRef, useCallback } from 'react';
import html2canvas from 'html2canvas';
import { PDFViewer, PDFDownloadLink } from '@react-pdf/renderer';
import ChartPDFDocument from '../components/ChartPDFDocument';
import { chartToImageDataUrl, generateAndDownloadPDF } from '../utils/pdfExport';
import { settingsService } from '../services/api';
import { usePageLoading } from '../hooks/useSmartLoading';
import { SmartPageLoading } from '../components/ui/smart-loading';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Checkbox } from '../components/ui/checkbox';
import { Label } from '../components/ui/label';
import { Badge } from '../components/ui/badge';
import { Separator } from '../components/ui/separator';
import { ScrollArea } from '../components/ui/scroll-area';
import {
  PieChart, LineChart, BarChart, Download, Calendar, Clock, Filter, SlidersHorizontal,
  Maximize2, ChevronDown, CheckCircle, XCircle, AlertCircle, ArrowDownUp, ArrowUpDown, AlertTriangle, Loader2,
  RefreshCw, Activity
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../components/ui/dropdown-menu";
import { useSocket } from '../contexts/SocketContext';
import { STATUS_TYPES, STATUS_BG_COLORS, getChartStatusColors, getChartColors } from '../lib/theme';

// Chart.js ve bileşenleri
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Filler,
  BarElement,
  RadialLinearScale,
  DoughnutController,
  PolarAreaController
} from 'chart.js';
import { Pie, Doughnut, PolarArea, Line, Bar } from 'react-chartjs-2';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

// Özel hook'lar
import {
  useDeviceStats,
  useDevices,
  useResponseTimeData,
  useStatusHistory,
  useStatusDistribution,
  useGroupAnalysis,
  useChartOptions,
  chartTypeOptions,
  timeRangeOptions
} from '../hooks/useGraphData';

// Grafik renkleri için tema dosyasından getChartColors() fonksiyonunu kullanıyoruz

// Durum renkleri artık theme.js'den içe aktarılıyor

// Chart.js bileşenlerini kaydet
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Filler,
  BarElement,
  RadialLinearScale,
  DoughnutController,
  PolarAreaController
);

// Durum Dağılımı Grafiği
const StatusDistributionChart = ({
  data,
  chartType,
  pieOptions,
  doughnutOptions,
  polarAreaOptions,
  isLoading
}) => {
  // Yükleme durumu
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground">Veriler yükleniyor...</p>
      </div>
    );
  }

  // Veri kontrolü - daha kapsamlı kontrol
  const hasValidData = data &&
                      data.datasets &&
                      Array.isArray(data.datasets) &&
                      data.datasets.length > 0 &&
                      data.datasets[0] &&
                      data.datasets[0].data &&
                      Array.isArray(data.datasets[0].data) &&
                      data.datasets[0].data.length > 0;

  // Veri yoksa veya geçersizse
  if (!hasValidData) {
    return (
      <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
        <PieChart className="h-10 w-10 mb-3 opacity-50" />
        <span>Veri bulunamadı</span>
        <p className="text-xs mt-2 text-center">Cihaz ekleyerek grafikleri görüntüleyebilirsiniz</p>
      </div>
    );
  }

  // Grafik türüne göre uygun grafiği göster
  return (
    <div className="w-full h-full max-w-full overflow-hidden">
      {chartType === 'pie' && (
        <Pie data={data} options={pieOptions} />
      )}
      {chartType === 'doughnut' && (
        <Doughnut data={data} options={doughnutOptions} />
      )}
      {chartType === 'polarArea' && (
        <PolarArea data={data} options={polarAreaOptions} />
      )}
    </div>
  );
};

// Yanıt Süresi Grafiği
const ResponseTimeChart = ({
  data,
  chartType,
  lineOptions,
  barOptions,
  isLoading,
  selectedDevices
}) => {
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground">Veriler yükleniyor...</p>
      </div>
    );
  }

  if (!data || !data.datasets || data.datasets.length === 0) {
    if (selectedDevices.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
          <LineChart className="h-10 w-10 mb-3 opacity-50" />
          <span>Cihaz seçilmedi</span>
          <p className="text-xs mt-2 text-center">Lütfen en az bir cihaz seçin</p>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
        <AlertTriangle className="h-10 w-10 mb-3 opacity-50" />
        <span>Yanıt süresi verisi bulunamadı</span>
        <p className="text-xs mt-2 text-center">Seçili cihazlar için yanıt süresi verisi henüz mevcut değil</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full max-w-full overflow-hidden">
      <div style={{ display: chartType === 'line' ? 'block' : 'none', height: '100%', width: '100%' }}>
        <Line data={data} options={lineOptions} />
      </div>
      <div style={{ display: chartType === 'bar' ? 'block' : 'none', height: '100%', width: '100%' }}>
        <Bar data={data} options={barOptions} />
      </div>
    </div>
  );
};

// Durum Değişim Geçmişi Grafiği
const StatusHistoryChart = ({
  data,
  options,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground">Veriler yükleniyor...</p>
      </div>
    );
  }

  // Veri kontrolü - daha esnek kontrol
  const hasValidData = data &&
                      data.labels &&
                      Array.isArray(data.labels) &&
                      data.labels.length > 0 &&
                      data.datasets &&
                      Array.isArray(data.datasets) &&
                      data.datasets.length > 0;

  if (!hasValidData) {
    return (
      <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
        <AlertTriangle className="h-10 w-10 mb-3 opacity-50" />
        <span>Durum geçmişi verisi bulunamadı</span>
        <p className="text-xs mt-2 text-center">Cihazların izlenmesi devam ettikçe veriler burada görüntülenecektir</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full max-w-full overflow-hidden">
      <Bar data={data} options={options} />
    </div>
  );
};

// Kategori Analizi Grafiği
const GroupChart = ({
  data,
  options,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground">Veriler yükleniyor...</p>
      </div>
    );
  }

  // Veri kontrolü - daha esnek kontrol
  const hasValidData = data &&
                      data.labels &&
                      Array.isArray(data.labels) &&
                      data.labels.length > 0 &&
                      data.datasets &&
                      Array.isArray(data.datasets) &&
                      data.datasets.length > 0;

  if (!hasValidData) {
    return (
      <div className="flex flex-col items-center justify-center text-muted-foreground h-full">
        <BarChart className="h-10 w-10 mb-3 opacity-50" />
        <span>Veri bulunamadı</span>
        <p className="text-xs mt-2 text-center">Henüz kategori verisi bulunmuyor</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full max-w-full overflow-hidden">
      <Bar data={data} options={options} />
    </div>
  );
};

const Graphs = () => {
  const navigate = useNavigate();
  const socket = useSocket(); // Socket.io bağlantısını al

  // Grafik referansları
  const statusChartRef = useRef(null);
  const responseChartRef = useRef(null);
  const historyChartRef = useRef(null);
  const groupChartRef = useRef(null);
  const activeChartContainerRef = useRef(null);

  // Durum değişkenleri
  const [activeTab, setActiveTab] = useState('status');
  const [statusChartType, setStatusChartType] = useState('pie');
  const [responseChartType, setResponseChartType] = useState('line');
  const [timeRange, setTimeRange] = useState('24h');
  const [selectedDevices, setSelectedDevices] = useState([]);
  const [showLegend, setShowLegend] = useState(true);
  const [showGrid, setShowGrid] = useState(true);

  // Veri sorgularını çalıştır
  const { data: deviceStatuses, isLoading: isStatusesLoading, refetch: refetchDeviceStatuses } = useDeviceStats();
  const { data: devices, isLoading: isDevicesLoading, refetch: refetchDevices } = useDevices();
  const { data: responseTimeData, isLoading: isResponseLoading, refetch: refetchResponseTimeData } = useResponseTimeData(selectedDevices, timeRange, devices);
  const { data: statusHistoryData, isLoading: isHistoryLoading, refetch: refetchStatusHistory } = useStatusHistory(timeRange);

  // Grafik ayarlarını oluştur
  const {
    pieChartOptions,
    doughnutChartOptions,
    polarAreaChartOptions,
    responseLineChartOptions,
    responseBarChartOptions,
    statusHistoryChartOptions,
    groupChartOptions
  } = useChartOptions(showLegend, showGrid);

  // Durum dağılımı ve grup analizi verilerini hesapla
  const { stats, data: statusDistributionData } = useStatusDistribution(devices, deviceStatuses);
  const { data: groupChartData } = useGroupAnalysis(devices, deviceStatuses);

  // Socket.io olaylarını dinle
  useEffect(() => {
    if (!socket) return;

    // Cihaz durumu güncellendiğinde
    const handleMonitorUpdate = (data) => {
      console.log('Socket: monitor:update olayı alındı', data);
      refetchDeviceStatuses();
    };

    // Cihaz durumu hesaplandığında
    const handleStatusUpdate = (data) => {
      console.log('Socket: device:status:update olayı alındı', data);
      refetchDeviceStatuses();
    };

    // Yeni cihaz eklendiğinde
    const handleDeviceNew = (data) => {
      console.log('Socket: device:new olayı alındı', data);
      refetchDevices();
    };

    // Cihaz güncellendiğinde
    const handleDeviceUpdate = (data) => {
      console.log('Socket: device:update olayı alındı', data);
      refetchDevices();
    };

    // Cihaz silindiğinde
    const handleDeviceDelete = (data) => {
      console.log('Socket: device:delete olayı alındı', data);
      refetchDevices();
    };

    // Cihaz kontrolü tamamlandığında
    const handleDeviceChecked = (data) => {
      console.log('Socket: device:checked olayı alındı', data);
      refetchDeviceStatuses();
      if (selectedDevices.includes(data.deviceId)) {
        refetchResponseTimeData();
      }
      refetchStatusHistory();
    };

    // Olayları dinle
    socket.on('monitor:update', handleMonitorUpdate);
    socket.on('device:status:update', handleStatusUpdate);
    socket.on('device:new', handleDeviceNew);
    socket.on('device:update', handleDeviceUpdate);
    socket.on('device:delete', handleDeviceDelete);
    socket.on('device:checked', handleDeviceChecked);

    // Temizleme fonksiyonu
    return () => {
      socket.off('monitor:update', handleMonitorUpdate);
      socket.off('device:status:update', handleStatusUpdate);
      socket.off('device:new', handleDeviceNew);
      socket.off('device:update', handleDeviceUpdate);
      socket.off('device:delete', handleDeviceDelete);
      socket.off('device:checked', handleDeviceChecked);
    };
  }, [socket, selectedDevices, refetchDeviceStatuses, refetchDevices, refetchResponseTimeData, refetchStatusHistory]);

  // Cihaz seçimi için yardımcı fonksiyonlar
  const handleDeviceSelect = (deviceId) => {
    setSelectedDevices(prev => {
      if (prev.includes(deviceId)) {
        return prev.filter(id => id !== deviceId);
      } else {
        return [...prev, deviceId];
      }
    });
  };

  const handleSelectAll = () => {
    if (devices && devices.length > 0) {
      if (selectedDevices.length === devices.length) {
        setSelectedDevices([]);
      } else {
        setSelectedDevices(devices.map(device => device.id));
      }
    }
  };

  // İlk yükleme sırasında varsayılan cihazları seç
  useEffect(() => {
    console.log('Devices effect triggered:', { devices, selectedDevices });
    if (devices && devices.length > 0 && selectedDevices.length === 0) {
      // İlk 5 cihazı seç (veya daha az varsa tümünü)
      const deviceIds = devices.slice(0, Math.min(5, devices.length)).map(device => device.id);
      console.log('Selecting default devices:', deviceIds);
      setSelectedDevices(deviceIds);
    }
  }, [devices, selectedDevices.length]);

  // Cihaz durumlarını ve cihaz listesini kontrol et
  useEffect(() => {
    console.log('Current device statuses:', deviceStatuses);
    console.log('Current devices:', devices);

    // Cihaz durumları ve cihaz listesi varsa, durum dağılımını hesapla
    if (deviceStatuses && devices && devices.length > 0) {
      console.log('Calculating status distribution...');
      const stats = { total: 0, online: 0, offline: 0, warning: 0 };

      devices.forEach(device => {
        if (!device || !device.id) return;

        stats.total++;
        // Backend'den hesaplanmış durumu kullan
        const status = deviceStatuses[device.id]?.calculatedStatus || 'unknown';

        if (status === 'up') {
          stats.online++;
        } else if (status === 'down') {
          stats.offline++;
        } else {
          stats.warning++;
        }
      });

      console.log('Calculated status distribution:', stats);
    }
  }, [deviceStatuses, devices]);

  // Yükleme durumu
  const isLoading = false; // isStatusesLoading || isDevicesLoading;

  // Smart loading hook
  const pageLoading = usePageLoading(null, isLoading);

  // Smart loading - sayfa seviyesi
  if (pageLoading.shouldShowFullPageLoading()) {
    return (
      <SmartPageLoading
        loadingType="initial"
        pageTitle="Grafikler"
        skeletonLayout="default"
      />
    );
  }

  // Cihaz durumu rengini belirle
  const getStatusColor = (status) => {
    return STATUS_BG_COLORS[status] || STATUS_BG_COLORS[STATUS_TYPES.UNKNOWN];
  };

  // Aktif grafik referansını al
  const getActiveChartRef = () => {
    switch (activeTab) {
      case 'status':
        return statusChartRef;
      case 'response':
        return responseChartRef;
      case 'history':
        return historyChartRef;
      case 'groups':
        return groupChartRef;
      default:
        return null;
    }
  };

  // Aktif grafik başlığını al
  const getActiveChartTitle = () => {
    switch (activeTab) {
      case 'status':
        return 'Durum Dağılımı';
      case 'response':
        return 'Yanıt Süresi Grafiği';
      case 'history':
        return 'Durum Değişim Geçmişi';
      case 'groups':
        return 'Kategori Analizi';
      default:
        return 'Grafik';
    }
  };



  // PDF olarak dışa aktar
  const exportAsPDF = async () => {
    try {
      console.log("PDF dışa aktarma başlatılıyor...");

      // Aktif sekmeyi ve başlığı al
      const title = getActiveChartTitle();
      console.log("Aktif grafik başlığı:", title);

      const today = new Date();
      const dateStr = today.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
      const timeStr = today.toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit'
      });

      // Grafik konteynerini bul
      const chartContainer = document.querySelector(`[data-tab="${activeTab}"] .chart-container`);
      if (!chartContainer) {
        throw new Error('Grafik bulunamadı');
      }
      console.log("Grafik konteyneri bulundu");

      // Grafik görüntüsünü oluştur
      console.log("Grafik görüntüsü oluşturuluyor...");
      const chartImageDataUrl = await chartToImageDataUrl(chartContainer);
      console.log("Grafik görüntüsü başarıyla oluşturuldu");

      // İstatistik verilerini hazırla (artık kullanılmıyor)
      let statsData = null;

      // Şirket adını ayarlardan al
      let companyName = 'NetWatch';
      try {
        const settings = await settingsService.getAll();
        companyName = settings.companyName || 'NetWatch';
      } catch (error) {
        console.error('Ayarlar alınırken hata:', error);
      }

      // PDF dokümanını oluştur
      console.log("PDF dokümanı oluşturuluyor...");
      const pdfDocument = (
        <ChartPDFDocument
          title={title}
          chartImageDataUrl={chartImageDataUrl}
          dateStr={dateStr}
          timeStr={timeStr}
          stats={statsData}
          companyName={companyName}
        />
      );

      // Dosya adı
      const fileName = `${title.replace(/\s+/g, '_')}_${dateStr.replace(/\//g, '-')}.pdf`;

      // PDF'i oluştur ve indir
      console.log("PDF indiriliyor:", fileName);
      await generateAndDownloadPDF(pdfDocument, fileName);
      console.log("PDF başarıyla oluşturuldu ve indirildi");
    } catch (error) {
      console.error('PDF dışa aktarma hatası:', error);
      console.error('Hata detayları:', error.message);
      console.error('Hata stack:', error.stack);
      alert(`Grafik dışa aktarılırken bir hata oluştu: ${error.message}`);
    }
  };



  return (
    <div className="p-6 space-y-6 max-w-full overflow-x-hidden">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight flex items-center gap-3">
            <BarChart className="h-6 w-6" />
            Grafikler
          </h1>
        </div>
        {/* PDF indirme butonu her sekmenin başlık kartına taşındı */}
      </div>

      <div className="mb-4 flex flex-col gap-4">
        <div className="w-full">
          <Tabs defaultValue="status" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="w-full grid grid-cols-4">
              <TabsTrigger value="status" className="text-sm">
                <span className="flex items-center justify-center gap-1">
                  <PieChart className="h-4 w-4" />
                  Durum Dağılımı
                </span>
              </TabsTrigger>
              <TabsTrigger value="history" className="text-sm">
                <span className="flex items-center justify-center gap-1">
                  <Clock className="h-4 w-4" />
                  Durum Geçmişi
                </span>
              </TabsTrigger>
              <TabsTrigger value="response" className="text-sm">
                <span className="flex items-center justify-center gap-1">
                  <LineChart className="h-4 w-4" />
                  Yanıt Süresi
                </span>
              </TabsTrigger>
              <TabsTrigger value="groups" className="text-sm">
                <span className="flex items-center justify-center gap-1">
                  <BarChart className="h-4 w-4" />
                  Kategori Analizi
                </span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs defaultValue="status" value={activeTab} onValueChange={setActiveTab} className="space-y-4 w-full max-w-full overflow-x-hidden">

        <TabsContent value="status" className="space-y-4 w-full max-w-full overflow-x-hidden" data-tab="status">
          <div className="grid gap-4 w-full">
            {/* Başlık ve Seçim Kartı */}
            <Card className="w-full">
              <CardHeader className="px-6 py-4">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <PieChart className="h-5 w-5" />
                      Durum Dağılımı
                    </CardTitle>
                    <CardDescription>
                      Cihazların mevcut durum dağılımı
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select value={statusChartType} onValueChange={setStatusChartType}>
                      <SelectTrigger className="w-[140px]">
                        <SelectValue placeholder="Grafik Türü" />
                      </SelectTrigger>
                      <SelectContent>
                        {chartTypeOptions.status.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon">
                          <SlidersHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Grafik Ayarları</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="showLegend" checked={showLegend} onCheckedChange={setShowLegend} />
                            <Label htmlFor="showLegend">Göstergeyi Göster</Label>
                          </div>
                          <div className="flex items-center space-x-2 mt-2">
                            <Checkbox id="showGrid" checked={showGrid} onCheckedChange={setShowGrid} />
                            <Label htmlFor="showGrid">Izgarayı Göster</Label>
                          </div>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button variant="outline" size="icon" onClick={exportAsPDF} title="PDF olarak indir">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Grafik Kartı */}
            <Card className="w-full">
              <CardContent className="px-6 py-6">
                <div className="h-[550px] flex flex-col items-center justify-center chart-container w-full" ref={statusChartRef}>
                  <StatusDistributionChart
                    data={statusDistributionData}
                    chartType={statusChartType}
                    pieOptions={pieChartOptions}
                    doughnutOptions={doughnutChartOptions}
                    polarAreaOptions={polarAreaChartOptions}
                    isLoading={isStatusesLoading || isDevicesLoading}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4 w-full max-w-full overflow-x-hidden" data-tab="history">
          <div className="grid gap-4 w-full">
            {/* Başlık Kartı */}
            <Card className="w-full">
              <CardHeader className="px-6 py-4">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Durum Değişim Geçmişi
                    </CardTitle>
                    <CardDescription>
                      Zaman içindeki cihaz durumu değişimleri
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select value={timeRange} onValueChange={setTimeRange}>
                      <SelectTrigger className="w-[180px]">
                        <Calendar className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="Zaman Aralığı" />
                      </SelectTrigger>
                      <SelectContent>
                        {timeRangeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon">
                          <SlidersHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Grafik Ayarları</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="showLegend" checked={showLegend} onCheckedChange={setShowLegend} />
                            <Label htmlFor="showLegend">Göstergeyi Göster</Label>
                          </div>
                          <div className="flex items-center space-x-2 mt-2">
                            <Checkbox id="showGrid" checked={showGrid} onCheckedChange={setShowGrid} />
                            <Label htmlFor="showGrid">Izgarayı Göster</Label>
                          </div>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button variant="outline" size="icon" onClick={exportAsPDF} title="PDF olarak indir">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Grafik Kartı */}
            <Card className="w-full">
              <CardContent className="px-6 py-6">
                <div className="h-[550px] chart-container w-full" ref={historyChartRef}>
                  <StatusHistoryChart
                    data={statusHistoryData}
                    options={statusHistoryChartOptions}
                    isLoading={isHistoryLoading}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="response" className="space-y-4 w-full max-w-full overflow-x-hidden" data-tab="response">
          <div className="grid gap-4 w-full">
            {/* Başlık ve Seçim Kartı */}
            <Card className="w-full">
              <CardHeader className="px-6 py-4">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <LineChart className="h-5 w-5" />
                      Yanıt Süresi Grafiği
                    </CardTitle>
                    <CardDescription>
                      Seçili cihazların yanıt süresi değişimleri
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select value={responseChartType} onValueChange={setResponseChartType}>
                      <SelectTrigger className="w-[140px]">
                        <SelectValue placeholder="Grafik Türü" />
                      </SelectTrigger>
                      <SelectContent>
                        {chartTypeOptions.response.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select value={timeRange} onValueChange={setTimeRange}>
                      <SelectTrigger className="w-[180px]">
                        <Calendar className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="Zaman Aralığı" />
                      </SelectTrigger>
                      <SelectContent>
                        {timeRangeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon">
                          <SlidersHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Grafik Ayarları</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="showLegend" checked={showLegend} onCheckedChange={setShowLegend} />
                            <Label htmlFor="showLegend">Göstergeyi Göster</Label>
                          </div>
                          <div className="flex items-center space-x-2 mt-2">
                            <Checkbox id="showGrid" checked={showGrid} onCheckedChange={setShowGrid} />
                            <Label htmlFor="showGrid">Izgarayı Göster</Label>
                          </div>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button variant="outline" size="icon" onClick={exportAsPDF} title="PDF olarak indir">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Grafik ve Cihazlar Kartları */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 w-full">
              {/* Grafik Kartı */}
              <Card className="md:col-span-3">
                <CardContent className="px-6 py-6">
                  <div className="h-[550px] chart-container w-full" ref={responseChartRef}>
                    <ResponseTimeChart
                      data={responseTimeData}
                      chartType={responseChartType}
                      lineOptions={responseLineChartOptions}
                      barOptions={responseBarChartOptions}
                      isLoading={isResponseLoading}
                      selectedDevices={selectedDevices}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Cihazlar Kartı */}
              <Card>
                <CardHeader className="px-6 py-4">
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Cihazlar
                    </CardTitle>
                    <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                      {selectedDevices.length === (devices?.length || 0) ? 'Tümünü Kaldır' : 'Tümünü Seç'}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <ScrollArea className="h-[490px]">
                    <div className="p-4 space-y-3">
                      {isDevicesLoading ? (
                        <div className="text-center py-4">Yükleniyor...</div>
                      ) : devices && devices.length > 0 ? (
                        devices.map(device => {
                          const status = deviceStatuses?.[device.id]?.icmp?.status || 'unknown';
                          return (
                            <div key={device.id} className="flex items-center space-x-3 py-1">
                              <Checkbox
                                id={`device-${device.id}`}
                                checked={selectedDevices.includes(device.id)}
                                onCheckedChange={() => handleDeviceSelect(device.id)}
                                className="h-5 w-5"
                              />
                              <Label
                                htmlFor={`device-${device.id}`}
                                className="flex-1 flex items-center justify-between cursor-pointer text-base"
                              >
                                <span className="truncate">{device.name}</span>
                                <div className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}></div>
                              </Label>
                            </div>
                          );
                        })
                      ) : (
                        <div className="text-center py-4">Cihaz bulunamadı</div>
                      )}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="groups" className="space-y-4 w-full max-w-full overflow-x-hidden" data-tab="groups">
          <div className="grid gap-4 w-full">
            {/* Başlık Kartı */}
            <Card className="w-full">
              <CardHeader className="px-6 py-4">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart className="h-5 w-5" />
                      Kategori Analizi
                    </CardTitle>
                    <CardDescription>
                      Cihazların kategori bazlı durum dağılımı
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon">
                          <SlidersHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Grafik Ayarları</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="showLegend" checked={showLegend} onCheckedChange={setShowLegend} />
                            <Label htmlFor="showLegend">Göstergeyi Göster</Label>
                          </div>
                          <div className="flex items-center space-x-2 mt-2">
                            <Checkbox id="showGrid" checked={showGrid} onCheckedChange={setShowGrid} />
                            <Label htmlFor="showGrid">Izgarayı Göster</Label>
                          </div>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button variant="outline" size="icon" onClick={exportAsPDF} title="PDF olarak indir">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Grafik Kartı */}
            <Card className="w-full">
              <CardContent className="px-6 py-6">
                <div className="h-[550px] chart-container w-full" ref={groupChartRef}>
                  <GroupChart
                    data={groupChartData}
                    options={groupChartOptions}
                    isLoading={isStatusesLoading || isDevicesLoading}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Graphs;
