import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../services/api';
import { useSocket } from '../contexts/SocketContext';
import { useAuth } from '../contexts/AuthContext';
import { showSuccess, showError } from '../services/notification-service';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Badge } from '../components/ui/badge';
import { Skeleton } from '../components/ui/skeleton';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON><PERSON>ooter } from '../components/ui/dialog';
import { Textarea } from '../components/ui/textarea';

import { Checkbox } from '../components/ui/checkbox';
import FloatingActionBar from '../components/ui/floating-action-bar';
import PaginationComponent from '../components/PaginationComponent';

// Theme
import {
  SEVERITY_TYPES,
  getSeverityBadgeClass,
  getNotificationRowClass,
  getNotificationTextClass,
  getSeverityLabel,
  getSeverityIcon,
  TEXT_STYLES
} from '../lib/theme';

// Icons
import {
  Bell, Search, Eye, CheckCircle, AlertTriangle, Info,
  Server, Settings, Circle, XCircle, RotateCcw, X, RefreshCw,
  ArrowUp, ArrowDown
} from 'lucide-react';

// ✅ Basit Constants - Inline
const SOURCES = {
  DEVICE: 'device',
  SYSTEM: 'system'
};

const SEVERITIES = {
  CRITICAL: 'critical',
  WARNING: 'warning',
  INFO: 'info',
  SUCCESS: 'success'
};

const STATUSES = {
  NEW: 'new',
  READ: 'read',
  RESOLVED: 'resolved'
};

// ✅ Basit Icon Mapping
const getSourceIcon = (source) => {
  switch (source) {
    case SOURCES.DEVICE: return <Server className="h-4 w-4 text-blue-500" />; // ✅ Küçük ikon - tablo içi
    case SOURCES.SYSTEM: return <Settings className="h-4 w-4 text-green-500" />;
    default: return <Circle className="h-4 w-4 text-gray-500" />;
  }
};



const getStatusBadge = (status) => {
  // ✅ Güvenli kontrol
  if (!status) return <Badge variant="outline">Bilinmiyor</Badge>;

  switch (status) {
    case STATUSES.NEW:
      return (
        <Badge variant="destructive" className="flex items-center gap-1.5 px-2.5 py-1">
          <Circle className="h-4 w-4" />
          Yeni
        </Badge>
      );
    case STATUSES.READ:
      return (
        <Badge variant="secondary" className="flex items-center gap-1.5 px-2.5 py-1">
          <Eye className="h-4 w-4" />
          Okundu
        </Badge>
      );
    case STATUSES.RESOLVED:
      return (
        <Badge variant="default" className="flex items-center gap-1.5 px-2.5 py-1">
          <CheckCircle className="h-4 w-4" />
          Çözüldü
        </Badge>
      );
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

// ✅ Basit Date Formatting - Güvenli
const formatDate = (timestamp) => {
  if (!timestamp) return 'Tarih Yok';
  try {
    return new Date(timestamp).toLocaleDateString('tr-TR');
  } catch (error) {
    return 'Geçersiz Tarih';
  }
};

const formatTime = (timestamp) => {
  if (!timestamp) return 'Saat Yok';
  try {
    return new Date(timestamp).toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Geçersiz Saat';
  }
};

// ✅ Relative time formatı
const getRelativeTime = (timestamp) => {
  if (!timestamp) return '';
  try {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) {
      return 'Az önce';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} dk önce`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} sa önce`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} gün önce`;
    } else {
      return formatDate(timestamp);
    }
  } catch (error) {
    return '';
  }
};

const NotificationsPage = () => {
  const navigate = useNavigate();
  const socket = useSocket();
  const { user } = useAuth();

  // ✅ Basit State
  const [notifications, setNotifications] = useState([]);
  const [counts, setCounts] = useState({
    total: 0,
    unread: 0,
    sources: { device: 0, system: 0 },
    severities: { critical: 0, warning: 0, info: 0, success: 0 },
    statuses: { new: 0, read: 0, resolved: 0 }
  });

  const [filters, setFilters] = useState({
    search: '',
    source: 'all',
    severity: 'all',
    status: 'all'
  });

  // Sıralama state'leri
  const [sortField, setSortField] = useState('timestamp');
  const [sortDirection, setSortDirection] = useState('desc');

  // Sıralama işlemi
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // const [loading, setLoading] = useState(true); // ✅ Unused - removed
  const [initialLoading, setInitialLoading] = useState(true);
  const [searchInput, setSearchInput] = useState('');

  // ✅ Selection State for Floating Action Bar
  const [selectedNotifications, setSelectedNotifications] = useState([]);
  const [actionLoading, setActionLoading] = useState({
    bulkRead: false
  });

  // ✅ Sayfalama State
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Resolve Dialog State
  const [resolveDialog, setResolveDialog] = useState({
    open: false,
    notification: null,
    note: ''
  });

  // ✅ Basit API Calls
  const loadNotifications = useCallback(async () => {
    try {
      // setLoading(true); // ✅ Unused - removed

      const params = new URLSearchParams();
      if (filters.search) params.append('search', filters.search);
      if (filters.source !== 'all') params.append('source', filters.source);
      if (filters.severity !== 'all') params.append('severity', filters.severity);
      if (filters.status !== 'all') params.append('status', filters.status);
      params.append('includeCounts', 'true');
      params.append('limit', '100'); // Basit - hep 100 al

      const response = await api.get(`/notifications?${params.toString()}`);

      // ✅ API response format düzeltmesi
      if (response.data.data) {
        // includeCounts=true durumu: {data: [], meta: {}, pagination: {}}
        setNotifications(response.data.data || []);
        setCounts(response.data.meta || counts);
      } else {
        // includeCounts=false durumu: doğrudan array
        setNotifications(Array.isArray(response.data) ? response.data : []);
        // Counts ayrıca yüklenecek
        loadCounts();
      }

    } catch (error) {
      console.error('Error loading notifications:', error);
      showError('Bildirimler yüklenirken hata oluştu');
    } finally {
      // setLoading(false); // ✅ Unused - removed
      setInitialLoading(false);
    }
  }, [filters]);

  const loadCounts = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (filters.search) params.append('search', filters.search);
      if (filters.source !== 'all') params.append('source', filters.source);
      if (filters.severity !== 'all') params.append('severity', filters.severity);
      if (filters.status !== 'all') params.append('status', filters.status);

      const response = await api.get(`/notifications/counts?${params.toString()}`);
      setCounts(response.data);
    } catch (error) {
      console.error('Error loading counts:', error);
    }
  }, [filters]);

  // ✅ Basit Actions
  const markAsRead = async (id, showToast = true) => {
    try {
      await api.post(`/notifications/${id}/read`);

      // Optimistic update
      setNotifications(prev =>
        prev.map(n => n.id === id ? { ...n, status: STATUSES.READ } : n)
      );

      // Sadece manuel işaretlemede toast göster
      if (showToast) {
        showSuccess('Bildirim okundu olarak işaretlendi');
      }
      loadCounts(); // Sayıları güncelle
    } catch (error) {
      console.error('Error marking as read:', error);
      showError('Bildirim işaretlenirken hata oluştu');
    }
  };

  const markAsResolved = async (id, note = '') => {
    try {
      // ✅ User bilgilerini ekle
      const payload = {
        resolution: note, // Backend 'resolution' bekliyor
        userId: user?.id || 'unknown',
        username: user?.username || 'unknown'
      };

      await api.post(`/notifications/${id}/resolve`, payload);

      // Optimistic update
      setNotifications(prev =>
        prev.map(n => n.id === id ? { ...n, status: STATUSES.RESOLVED } : n)
      );

      showSuccess('Bildirim çözüldü olarak işaretlendi');
      loadCounts(); // Sayıları güncelle
    } catch (error) {
      console.error('Error marking as resolved:', error);
      showError('Bildirim çözülürken hata oluştu');
    }
  };

  const markAllAsRead = async () => {
    try {
      await api.post('/notifications/read-all');

      // Optimistic update
      setNotifications(prev =>
        prev.map(n => n.status === STATUSES.NEW ? { ...n, status: STATUSES.READ } : n)
      );

      showSuccess('Tüm bildirimler okundu olarak işaretlendi');
      loadCounts(); // Sayıları güncelle
    } catch (error) {
      console.error('Error marking all as read:', error);
      showError('Bildirimler işaretlenirken hata oluştu');
    }
  };

  // ✅ Basit Filter Handlers
  const updateFilter = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    // Filtre değiştiğinde ilk sayfaya dön ve seçimi temizle
    setCurrentPage(1);
    setSelectedNotifications([]);
  };

  const resetFilters = () => {
    setFilters({
      search: '',
      source: 'all',
      severity: 'all',
      status: 'all'
    });
    setSearchInput('');
    // Filtre temizlendiğinde ilk sayfaya dön ve seçimi temizle
    setCurrentPage(1);
    setSelectedNotifications([]);
  };

  // ✅ Debounce search ref
  const searchTimeoutRef = useRef(null);

  const handleSearchChange = (value) => {
    setSearchInput(value);

    // ✅ Önceki timeout'u temizle
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // ✅ Yeni timeout ayarla
    searchTimeoutRef.current = setTimeout(() => {
      updateFilter('search', value);
    }, 500);
  };

  // ✅ Selection Handlers
  const handleSelectNotification = (notificationId, checked) => {
    if (checked) {
      setSelectedNotifications(prev => [...prev, notificationId]);
    } else {
      setSelectedNotifications(prev => prev.filter(id => id !== notificationId));
    }
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      // Sadece mevcut sayfadaki bildirimleri seç
      const selectableNotifications = currentItems.filter(n => n && n.id);
      setSelectedNotifications(selectableNotifications.map(n => n.id));
    } else {
      setSelectedNotifications([]);
    }
  };

  // ✅ Bildirime tıklama - Cihaza yönlendirme
  const getNotificationRedirectUrl = (notification) => {
    // Cihaz bildirimi için cihaz detay sayfasına yönlendir
    if (notification.source?.type === 'device' && notification.source?.id) {
      return `/devices/${notification.source.id}`;
    }

    // Kullanıcı bildirimi için kullanıcı sayfasına yönlendir
    if (notification.source?.type === 'user' && notification.source?.id) {
      return `/users/${notification.source.id}`;
    }

    // Sistem bildirimi için ayarlar sayfasına yönlendir
    return '/settings';
  };

  const handleNotificationClick = async (notification) => {
    // Sistem kaynaklı bildirimlerde tıklama özelliğini devre dışı bırak
    if (notification.source?.type === 'system') {
      return;
    }

    // Sadece yönlendirme yap, otomatik okundu işaretleme yapma
    // Kullanıcı manuel olarak okundu işaretlemeli
    const redirectUrl = getNotificationRedirectUrl(notification);
    navigate(redirectUrl);
  };

  // ✅ Dialog Handlers
  const openResolveDialog = (notification) => {
    setResolveDialog({
      open: true,
      notification,
      note: ''
    });
  };

  const closeResolveDialog = () => {
    setResolveDialog({
      open: false,
      notification: null,
      note: ''
    });
    // FAB'dan açıldıysa seçimi temizle
    if (selectedNotifications.length === 1) {
      setSelectedNotifications([]);
    }
  };

  const handleResolve = async () => {
    if (resolveDialog.notification) {
      await markAsResolved(resolveDialog.notification.id, resolveDialog.note);
      closeResolveDialog();
    }
  };

  // ✅ Bulk Actions
  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.length === 0) return;

    try {
      setActionLoading(prev => ({ ...prev, bulkRead: true }));

      // Sadece 'new' durumundaki bildirimleri işaretle
      const newNotifications = selectedNotifications.filter(id => {
        const notification = notifications.find(n => n.id === id);
        return notification && notification.status === 'new';
      });

      if (newNotifications.length === 0) {
        showError('Seçilen bildirimler arasında okunmamış bildirim yok');
        return;
      }

      // Her bildirimi tek tek işaretle
      for (const notificationId of newNotifications) {
        await markAsRead(notificationId, false); // Toast gösterme
      }

      showSuccess(`${newNotifications.length} bildirim okundu olarak işaretlendi`);
      setSelectedNotifications([]);
    } catch (error) {
      console.error('Bulk mark as read error:', error);
      showError('Bildirimler işaretlenirken hata oluştu');
    } finally {
      setActionLoading(prev => ({ ...prev, bulkRead: false }));
    }
  };

  // ✅ Single Notification Resolve from FAB
  const handleSingleResolveFromFAB = () => {
    if (selectedNotifications.length === 1) {
      const notificationId = selectedNotifications[0];
      const notification = notifications.find(n => n.id === notificationId);
      if (notification) {
        openResolveDialog(notification);
        // Seçimi temizleme, dialog kapandığında yapılacak
      }
    }
  };

  // ✅ Effects
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // ✅ Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // ✅ Socket Events
  useEffect(() => {
    if (!socket) return;

    const handleNewNotification = (data) => {
      // ✅ Güvenli data kontrolü
      if (data && data.notification && data.notification.id) {
        setNotifications(prev => [data.notification, ...prev]);
        loadCounts();
      } else {
        console.warn('notification:new event received with invalid data:', data);
      }
    };

    const handleUpdateNotification = (data) => {
      // ✅ Güvenli data kontrolü
      if (data && data.notification && data.notification.id) {
        setNotifications(prev =>
          prev.map(n => n.id === data.notification.id ? data.notification : n)
        );
        loadCounts();
      } else {
        console.warn('notification:update event received with invalid data:', data);
      }
    };

    const handleMarkAllAsRead = (data) => {
      // ✅ Toplu okundu işaretleme
      setNotifications(prev =>
        prev.map(n => n.status === STATUSES.NEW ? { ...n, status: STATUSES.READ } : n)
      );
      loadCounts();
      console.log(`${data?.count || 0} bildirim okundu olarak işaretlendi`);
    };

    const handleCountsUpdated = (data) => {
      setCounts(data.counts);
    };

    socket.on('notification:new', handleNewNotification);
    socket.on('notification:update', handleUpdateNotification);
    socket.on('notifications:mark-all-read', handleMarkAllAsRead);
    socket.on('notification:counts-updated', handleCountsUpdated);

    return () => {
      socket.off('notification:new', handleNewNotification);
      socket.off('notification:update', handleUpdateNotification);
      socket.off('notifications:mark-all-read', handleMarkAllAsRead);
      socket.off('notification:counts-updated', handleCountsUpdated);
    };
  }, [socket]);

  // ✅ Filtreleme ve sıralama
  const filteredAndSortedNotifications = notifications.sort((a, b) => {
    let comparison = 0;

    switch(sortField) {
      case 'title':
        comparison = (a.title || '').localeCompare(b.title || '');
        break;
      case 'severity':
        // Severity sıralaması: critical > warning > info > success
        const severityOrder = { 'critical': 0, 'warning': 1, 'info': 2, 'success': 3 };
        comparison = (severityOrder[a.severity] || 4) - (severityOrder[b.severity] || 4);
        break;
      case 'status':
        // Status sıralaması: new > read > resolved
        const statusOrder = { 'new': 0, 'read': 1, 'resolved': 2 };
        comparison = (statusOrder[a.status] || 3) - (statusOrder[b.status] || 3);
        break;
      case 'source':
        const sourceA = a.source?.name || 'Sistem';
        const sourceB = b.source?.name || 'Sistem';
        comparison = sourceA.localeCompare(sourceB);
        break;
      case 'timestamp':
      default:
        comparison = new Date(a.timestamp) - new Date(b.timestamp);
        break;
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // ✅ Sayfalama için hesaplamalar
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredAndSortedNotifications.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredAndSortedNotifications.length / itemsPerPage);

  // Sayfa değiştirme fonksiyonu
  const paginate = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Sayfa değiştiğinde seçimi temizle
    setSelectedNotifications([]);
  };

  // Sayfa başına öğe sayısını değiştirme fonksiyonu
  const handlePageSizeChange = (newPageSize) => {
    // Yeni sayfa boyutuna göre mevcut sayfayı ayarla
    const newTotalPages = Math.ceil(notifications.length / newPageSize);
    const newCurrentPage = Math.min(currentPage, newTotalPages);

    setItemsPerPage(newPageSize);
    setCurrentPage(newCurrentPage);
    // Sayfa boyutu değiştiğinde seçimi temizle
    setSelectedNotifications([]);
  };

  // ✅ Computed Values
  const hasFilters = filters.search || filters.source !== 'all' ||
                    filters.severity !== 'all' || filters.status !== 'all';

  const unreadCount = counts.statuses?.new || 0;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Bildirimler</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Sistem ve cihaz bildirimlerini görüntüleyin ve yönetin
          </p>
        </div>

        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button onClick={markAllAsRead} variant="secondary" size="default">
              <Eye className="h-4 w-4 mr-2" />
              Tümünü Okundu İşaretle ({unreadCount})
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-all">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Toplam Bildirim</p>
                <p className="text-2xl font-bold tracking-tight">{counts.total}</p>
              </div>
              <Bell className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-all">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Okunmamış Bildirim</p>
                <p className="text-2xl font-bold tracking-tight text-red-600">{counts.unread}</p>
              </div>
              <Circle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-all">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Cihaz Bildirimi</p>
                <p className="text-2xl font-bold tracking-tight text-blue-600">{counts.sources?.device || 0}</p>
              </div>
              <Server className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-all">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Sistem Bildirimi</p>
                <p className="text-2xl font-bold tracking-tight text-green-600">{counts.sources?.system || 0}</p>
              </div>
              <Settings className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-6">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Bildirim ara..."
                value={searchInput}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filter Dropdowns */}
            <div className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-[200px]">
                <Select value={filters.source} onValueChange={(value) => updateFilter('source', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Kaynak Seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Kaynaklar</SelectItem>
                    <SelectItem value="device">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4 text-blue-500" />
                        Cihaz
                      </div>
                    </SelectItem>
                    <SelectItem value="system">
                      <div className="flex items-center gap-2">
                        <Settings className="h-4 w-4 text-green-500" />
                        Sistem
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1 min-w-[200px]">
                <Select value={filters.severity} onValueChange={(value) => updateFilter('severity', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Önem Derecesi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Önem Dereceleri</SelectItem>
                    <SelectItem value="critical">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        Kritik
                      </div>
                    </SelectItem>
                    <SelectItem value="warning">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                        Uyarı
                      </div>
                    </SelectItem>
                    <SelectItem value="info">
                      <div className="flex items-center gap-2">
                        <Info className="h-4 w-4 text-blue-500" />
                        Bilgi
                      </div>
                    </SelectItem>
                    <SelectItem value="success">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Başarılı
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1 min-w-[200px]">
                <Select value={filters.status} onValueChange={(value) => updateFilter('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Durum" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Durumlar</SelectItem>
                    <SelectItem value="new">
                      <div className="flex items-center gap-2">
                        <Circle className="h-4 w-4 text-red-500" />
                        Yeni
                      </div>
                    </SelectItem>
                    <SelectItem value="read">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4 text-gray-500" />
                        Okundu
                      </div>
                    </SelectItem>
                    <SelectItem value="resolved">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Çözüldü
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {hasFilters && (
                <Button onClick={resetFilters} variant="secondary" size="default" className="shrink-0">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Temizle
                </Button>
              )}
            </div>

            {/* Active Filters */}
            {hasFilters && (
              <div className="flex flex-wrap gap-4">
                {filters.source !== 'all' && (
                  <Badge variant="outline" className="flex items-center gap-2 px-3 py-1.5 text-sm">
                    {filters.source === 'device' ? (
                      <Server className="h-4 w-4 text-blue-500" />
                    ) : (
                      <Settings className="h-4 w-4 text-green-500" />
                    )}
                    Kaynak: {filters.source === 'device' ? 'Cihaz' : 'Sistem'}
                    <XCircle
                      className="h-4 w-4 cursor-pointer hover:text-red-500 transition-colors"
                      onClick={() => updateFilter('source', 'all')}
                    />
                  </Badge>
                )}
                {filters.severity !== 'all' && (
                  <Badge variant="outline" className="flex items-center gap-2 px-3 py-1.5 text-sm">
                    {getSeverityIcon(filters.severity, 'sm')}
                    Önem: {getSeverityLabel(filters.severity)}
                    <XCircle
                      className="h-4 w-4 cursor-pointer hover:text-red-500 transition-colors"
                      onClick={() => updateFilter('severity', 'all')}
                    />
                  </Badge>
                )}
                {filters.status !== 'all' && (
                  <Badge variant="outline" className="flex items-center gap-2 px-3 py-1.5 text-sm">
                    {filters.status === 'new' ? (
                      <Circle className="h-4 w-4 text-red-500" />
                    ) : filters.status === 'read' ? (
                      <Eye className="h-4 w-4 text-gray-500" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    Durum: {filters.status === 'new' ? 'Yeni' :
                            filters.status === 'read' ? 'Okundu' : 'Çözüldü'}
                    <XCircle
                      className="h-4 w-4 cursor-pointer hover:text-red-500 transition-colors"
                      onClick={() => updateFilter('status', 'all')}
                    />
                  </Badge>
                )}
                {filters.search && (
                  <Badge variant="outline" className="flex items-center gap-2 px-3 py-1.5 text-sm">
                    <Search className="h-4 w-4 text-gray-500" />
                    Arama: "{filters.search}"
                    <XCircle
                      className="h-4 w-4 cursor-pointer hover:text-red-500 transition-colors"
                      onClick={() => {
                        updateFilter('search', '');
                        setSearchInput('');
                      }}
                    />
                  </Badge>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="flex flex-col">
              <CardTitle className="text-xl font-semibold tracking-tight">Bildirim Listesi</CardTitle>
              <p className="text-sm text-muted-foreground mt-2 leading-relaxed">
                <Info className="inline-block h-4 w-4 mr-1" /> Bildirimleri seçerek toplu işlemler yapabilirsiniz
              </p>
            </div>
            {hasFilters && (
              <span className="text-sm font-medium text-muted-foreground">
                {notifications.length} sonuç bulundu
              </span>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12 py-3 px-4">
                    <Checkbox
                      checked={selectedNotifications.length === currentItems.filter(n => n && n.id).length && currentItems.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead className="w-20 py-3 px-3 hidden sm:table-cell cursor-pointer" onClick={() => handleSort('severity')}>
                    <div className="flex items-center">
                      Önem
                      {sortField === 'severity' && (
                        <span className="ml-2">
                          {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                        </span>
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="w-20 py-3 px-3 cursor-pointer" onClick={() => handleSort('status')}>
                    <div className="flex items-center">
                      Durum
                      {sortField === 'status' && (
                        <span className="ml-2">
                          {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                        </span>
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="py-3 px-4 cursor-pointer" onClick={() => handleSort('title')}>
                    <div className="flex items-center">
                      Bildirim
                      {sortField === 'title' && (
                        <span className="ml-2">
                          {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                        </span>
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="w-32 py-3 px-3 hidden md:table-cell cursor-pointer" onClick={() => handleSort('source')}>
                    <div className="flex items-center">
                      Kaynak
                      {sortField === 'source' && (
                        <span className="ml-2">
                          {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                        </span>
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="w-48 py-3 px-4 hidden lg:table-cell">Çözüm Notu</TableHead>
                  <TableHead className="w-36 py-3 px-4 hidden md:table-cell cursor-pointer" onClick={() => handleSort('timestamp')}>
                    <div className="flex items-center">
                      Zaman
                      {sortField === 'timestamp' && (
                        <span className="ml-2">
                          {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                        </span>
                      )}
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {initialLoading ? (
                  // Loading skeleton - Gerçek içerikle eşleşen boyutlar
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`}>
                      {/* Checkbox */}
                      <TableCell className="py-4 px-4">
                        <Skeleton className="h-4 w-4 rounded-sm" />
                      </TableCell>
                      {/* Önem + Label - Responsive */}
                      <TableCell className="py-4 px-3 hidden sm:table-cell">
                        <div className="flex flex-col items-center space-y-2">
                          <Skeleton className="h-5 w-5 rounded-md" />
                          <Skeleton className="h-6 w-16 rounded-full" />
                        </div>
                      </TableCell>
                      {/* Durum */}
                      <TableCell className="py-4 px-3">
                        <div className="flex flex-col space-y-2">
                          <Skeleton className="h-6 w-20 rounded-full" />
                          {/* Mobilde zaman skeleton */}
                          <div className="md:hidden">
                            <Skeleton className="h-4 w-16" />
                          </div>
                        </div>
                      </TableCell>
                      {/* Bildirim */}
                      <TableCell className="py-4 px-4">
                        <div className="space-y-2">
                          <div className="flex items-start gap-2">
                            {/* Mobilde severity ikon skeleton */}
                            <div className="sm:hidden">
                              <Skeleton className="h-4 w-4 rounded-md" />
                            </div>
                            <Skeleton className="h-5 w-48" />
                          </div>
                          <Skeleton className="h-4 w-64" />
                          {/* Mobilde kaynak skeleton */}
                          <div className="md:hidden flex items-center gap-2">
                            <Skeleton className="h-4 w-4 rounded-md" />
                            <Skeleton className="h-4 w-20" />
                          </div>
                        </div>
                      </TableCell>
                      {/* Kaynak - Responsive */}
                      <TableCell className="py-4 px-3 hidden md:table-cell">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-4 w-4 rounded-md" />
                            <Skeleton className="h-4 w-16" />
                          </div>
                          <Skeleton className="h-4 w-24" />
                        </div>
                      </TableCell>
                      {/* Çözüm Notu - Responsive */}
                      <TableCell className="py-4 px-4 hidden lg:table-cell">
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      {/* Zaman - Responsive */}
                      <TableCell className="py-4 px-4 hidden md:table-cell">
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-20" />
                          <Skeleton className="h-4 w-16" />
                          <Skeleton className="h-4 w-12" />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : notifications.length === 0 ? (
                  // Empty state
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                      <div className="text-lg font-medium">Bildirim Bulunamadı</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {hasFilters ? "Filtre kriterlerinize uygun bildirim bulunamadı." : "Henüz bildirim bulunmuyor."}
                      </div>
                      {hasFilters && (
                        <Button variant="secondary" size="default" className="mt-4" onClick={resetFilters}>
                          Filtreleri Temizle
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  // Notifications - Güvenli rendering - Sayfalanmış veriler
                  currentItems.filter(notification => notification && notification.id).map(notification => (
                    <TableRow
                      key={notification.id}
                      className={`${getNotificationRowClass(notification.status)} cursor-pointer`}
                      onClick={(e) => {
                        // Checkbox'a veya butonlara tıklandığında satır tıklamasını engelle
                        if (e.target.closest('button') || e.target.type === 'checkbox' || e.target.closest('[role="checkbox"]')) {
                          return;
                        }
                        handleNotificationClick(notification);
                      }}
                    >
                      {/* Checkbox */}
                      <TableCell className="py-4 px-4" onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={selectedNotifications.includes(notification.id)}
                          onCheckedChange={(checked) => handleSelectNotification(notification.id, checked)}
                        />
                      </TableCell>

                      {/* Önem Derecesi + Severity Label */}
                      <TableCell className="py-4 px-3 hidden sm:table-cell">
                        <div className="flex flex-col items-center justify-center space-y-2">
                          {getSeverityIcon(notification.severity, 'md')}
                          <span className={getSeverityBadgeClass(notification.severity)}>
                            {getSeverityLabel(notification.severity)}
                          </span>
                        </div>
                      </TableCell>

                      {/* Durum */}
                      <TableCell className="py-4 px-3">
                        <div className="flex flex-col space-y-2">
                          {getStatusBadge(notification.status)}
                          {/* Mobilde zaman bilgisi burada göster */}
                          <div className="md:hidden text-xs text-muted-foreground">
                            {getRelativeTime(notification.timestamp)}
                          </div>
                        </div>
                      </TableCell>

                      {/* Ana Bildirim İçeriği */}
                      <TableCell className="py-4 px-4">
                        <div className="flex flex-col space-y-2">
                          <div className="flex items-start gap-2">
                            {/* Mobilde severity ikonu burada göster */}
                            <div className="sm:hidden">
                              {getSeverityIcon(notification.severity, 'sm')}
                            </div>
                            <span className={`${getNotificationTextClass(notification.status)} leading-tight`}>
                              {notification.title || 'Başlık Yok'}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed mt-1">
                            {notification.message || 'Mesaj Yok'}
                          </p>
                          {/* Mobilde kaynak bilgisi burada göster */}
                          <div className="md:hidden flex items-center gap-2 text-xs text-muted-foreground">
                            {getSourceIcon(notification.source?.type)}
                            <span>{notification.source?.name || 'Sistem'}</span>
                          </div>
                        </div>
                      </TableCell>

                      {/* Kaynak */}
                      <TableCell className="py-4 px-3 hidden md:table-cell">
                        <div className="flex flex-col space-y-2">
                          <div className="flex items-center gap-2">
                            {getSourceIcon(notification.source?.type)}
                            <span className="text-sm font-medium leading-tight">
                              {notification.source?.type === 'device' ? 'Cihaz' : 'Sistem'}
                            </span>
                          </div>
                          <span className="text-sm text-muted-foreground truncate max-w-[120px] leading-relaxed" title={notification.source?.name || 'Sistem'}>
                            {notification.source?.name || 'Sistem'}
                          </span>
                        </div>
                      </TableCell>

                      {/* Çözüm Notu */}
                      <TableCell className="py-4 px-4 hidden lg:table-cell">
                        {notification.status === 'resolved' && notification.resolution ? (
                          <div className="space-y-2">
                            <p className={`text-sm ${TEXT_STYLES.RESOLUTION_NOTE} line-clamp-2 leading-relaxed`} title={notification.resolution}>
                              {notification.resolution}
                            </p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>Çözen: {notification.resolvedByUsername || 'Bilinmiyor'}</span>
                              {notification.resolvedAt && (
                                <span>• {formatDate(notification.resolvedAt)}</span>
                              )}
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-muted-foreground">-</span>
                        )}
                      </TableCell>

                      {/* Zaman - Daha Kullanıcı Dostu */}
                      <TableCell className="py-4 px-4 hidden md:table-cell">
                        <div className="flex flex-col space-y-1">
                          <span className="text-sm font-medium leading-tight">{formatDate(notification.timestamp)}</span>
                          <span className="text-sm text-muted-foreground leading-relaxed">
                            {formatTime(notification.timestamp)}
                          </span>
                          {/* Relative time */}
                          <span className="text-sm text-blue-600 font-medium">
                            {getRelativeTime(notification.timestamp)}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Sayfalandırma */}
          {filteredAndSortedNotifications.length > 0 && (
            <div className="mt-4">
              <PaginationComponent
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={filteredAndSortedNotifications.length}
                currentPageItems={currentItems.length}
                onPageChange={paginate}
                pageSize={itemsPerPage}
                onPageSizeChange={handlePageSizeChange}
                pageSizeOptions={[5, 10, 25, 50, 100]}
                showPageSizeOptions={true}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resolve Dialog */}
      <Dialog open={resolveDialog.open} onOpenChange={closeResolveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold tracking-tight">Bildirimi Çözüldü Olarak İşaretle</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground mb-2">
                Bu bildirimi çözüldü olarak işaretlemek istediğinizden emin misiniz?
              </p>
              {resolveDialog.notification && (
                <div className="p-3 bg-muted rounded-md">
                  <p className="font-medium">{resolveDialog.notification.title}</p>
                  <p className="text-sm text-muted-foreground">{resolveDialog.notification.message}</p>
                </div>
              )}
            </div>
            <div>
              <label className="text-sm font-medium">Çözüm Notu (Opsiyonel)</label>
              <Textarea
                placeholder="Çözüm hakkında not ekleyebilirsiniz..."
                value={resolveDialog.note}
                onChange={(e) => setResolveDialog(prev => ({ ...prev, note: e.target.value }))}
                className="mt-1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" size="default" onClick={closeResolveDialog}>
              İptal
            </Button>
            <Button variant="secondary" size="default" onClick={handleResolve}>
              Çözüldü Olarak İşaretle
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Floating Action Bar */}
      <FloatingActionBar
        selectedCount={selectedNotifications.length}
        selectedLabel="bildirim"
        onClearSelection={() => setSelectedNotifications([])}
      >
        {/* Okundu İşaretle */}
        <Button
          variant="secondary"
          size="default"
          onClick={handleBulkMarkAsRead}
          disabled={actionLoading.bulkRead}
        >
          {actionLoading.bulkRead ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> İşleniyor...
            </>
          ) : (
            <>
              <Eye className="mr-2 h-4 w-4" /> Okundu İşaretle
            </>
          )}
        </Button>

        {/* Çözüldü İşaretle - Sadece tek seçimde */}
        {selectedNotifications.length === 1 && (
          <Button
            variant="secondary"
            size="default"
            onClick={handleSingleResolveFromFAB}
          >
            <CheckCircle className="mr-2 h-4 w-4" /> Çözüldü İşaretle
          </Button>
        )}
      </FloatingActionBar>
    </div>
  );
};

export default NotificationsPage;
