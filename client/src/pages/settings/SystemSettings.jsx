import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Activity, Server, Database, Save, RefreshCw, Cpu, HardDrive, Clock, MemoryStick } from 'lucide-react';
import { Label } from '../../components/ui/label';
import { Button } from '../../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import SystemStatus from '../../components/dashboard/SystemStatus';
import { monitorService, deviceService, alertService, settingsService, systemService } from '../../services/api';
import { useSystem } from '../../contexts/SystemContext';
import { notificationService } from '../../services/notification-service';

/**
 * Byte cinsinden boyutu insan tarafından okunabilir formata çevirir
 * @param {number} bytes - Byte cinsinden boyut
 * @returns {string} - <PERSON>nsan tarafından okunabilir boyut
 */
const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + units[i];
};

/**
 * Saniye cinsinden çalışma süresini insan tarafından okunabilir formata çevirir
 * @param {number} uptime - Saniye cinsinden çalışma süresi
 * @returns {string} - İnsan tarafından okunabilir çalışma süresi
 */
const formatUptime = (uptime) => {
  const days = Math.floor(uptime / 86400);
  const hours = Math.floor((uptime % 86400) / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);

  let result = '';
  if (days > 0) result += `${days} gün `;
  if (hours > 0) result += `${hours} saat `;
  if (minutes > 0) result += `${minutes} dakika`;

  return result.trim() || '0 dakika';
};

const SystemSettings = () => {
  const { systemHealth, refreshSystemStatus } = useSystem();
  const [stats, setStats] = useState({
    total: 0,
    online: 0,
    offline: 0,
    warning: 0
  });
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    systemHealthCheckInterval: '1'
  });
  const [systemInfo, setSystemInfo] = useState({
    uptime: 0,
    cpu: [],
    memory: {
      total: 0,
      free: 0,
      used: 0
    },
    disk: {
      disks: [],
      total: {
        size: '0B',
        used: '0B',
        avail: '0B',
        pcent: 0
      }
    }
  });

  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);

      // Cihaz istatistiklerini yükle
      const statusesData = await monitorService.getAllStatuses();
      const devicesData = await deviceService.getAll();

      // İstatistikleri hesapla
      let online = 0;
      let offline = 0;
      let warning = 0;

      devicesData.forEach(device => {
        const status = statusesData[device.id]?.icmp?.status || 'unknown';
        if (status === 'up') online++;
        else if (status === 'down') offline++;
        else warning++;
      });

      setStats({
        total: devicesData.length,
        online,
        offline,
        warning
      });

      // Uyarıları yükle
      const alertsData = await alertService.getAll();
      setAlerts(alertsData);

      // Ayarları yükle
      const settingsData = await settingsService.getAll();
      setFormData(prevData => ({
        ...prevData,
        systemHealthCheckInterval: settingsData.systemHealthCheckInterval || '1'
      }));

      // Sistem bilgilerini yükle
      const systemData = await systemService.getStatus();
      setSystemInfo(systemData.systemInfo);

      // Sistem sağlığı bilgilerini güncelle
      refreshSystemStatus();
    } catch (error) {
      console.error('Sistem bilgileri yüklenirken hata oluştu:', error);
      // Hata durumunda toast bildirimi göster
      notificationService.error('Sistem bilgileri yüklenemedi', {
        description: 'Sistem bilgileri yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin.',
        category: 'system'
      });
    } finally {
      setLoading(false);
    }
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Formu gönder
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Mevcut ayarları al
      const currentSettings = await settingsService.getAll();

      // Yeni ayarları mevcut ayarlarla birleştir
      const updatedSettings = {
        ...currentSettings,
        ...formData
      };

      // Ayarları güncelle
      await settingsService.update(updatedSettings);

      // Toast bildirimi göster
      notificationService.success('Sistem ayarları kaydedildi', {
        description: 'Sistem ayarları başarıyla güncellendi.',
        persistent: true, // Bildirim panelinde de göster
        category: 'system'
      });
    } catch (error) {
      console.error('Ayarlar kaydedilirken hata oluştu:', error);

      // Hata bildirimi göster
      notificationService.error('Sistem ayarları kaydedilemedi', {
        description: error.response?.data?.error || 'Sistem ayarları kaydedilirken bir hata oluştu.',
        category: 'system'
      });
    } finally {
      setSaving(false);
    }
  };

  // Sayfa yüklendiğinde verileri getir
  useEffect(() => {
    loadData();
  }, []);

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Sistem Ayarları</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Sistem durumu ve bilgilerini görüntüleyin
          </p>
        </div>
        <div className="flex items-center gap-2 self-end sm:self-auto">
          <Button
            type="button"
            variant="outline"
            onClick={loadData}
            size="default"
          >
            İptal
          </Button>
          <Button
            type="submit"
            disabled={saving}
            size="default"
            form="system-settings-form"
          >
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...
              </>
            ) : (
              'Kaydet'
            )}
          </Button>
        </div>
      </div>

      {/* Sayfa içi hata ve başarı mesajlarını kaldırdık - Toast bildirimleri kullanıyoruz */}

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <div className="flex flex-col items-center gap-2">
            <RefreshCw className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Sistem bilgileri yükleniyor...</p>
          </div>
        </div>
      ) : (
        <form id="system-settings-form" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            {/* Sistem Servisleri Kartı */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" /> Sistem Servisleri
                </CardTitle>
                <CardDescription>
                  Sistem servislerinin durumunu izleyin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SystemStatus />
              </CardContent>
            </Card>

            {/* Sistem Sağlığı Ayarları Kartı */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" /> Sistem Sağlığı Ayarları
                </CardTitle>
                <CardDescription>
                  Sistem sağlığı kontrolü için ayarları yapılandırın
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="systemHealthCheckInterval">Sistem Sağlığı Kontrol Aralığı (dakika)</Label>
                  <Select
                    value={formData.systemHealthCheckInterval}
                    onValueChange={(value) => handleSelectChange('systemHealthCheckInterval', value)}
                  >
                    <SelectTrigger id="systemHealthCheckInterval">
                      <SelectValue placeholder="Aralık seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 dakika</SelectItem>
                      <SelectItem value="2">2 dakika</SelectItem>
                      <SelectItem value="5">5 dakika</SelectItem>
                      <SelectItem value="10">10 dakika</SelectItem>
                      <SelectItem value="15">15 dakika</SelectItem>
                      <SelectItem value="30">30 dakika</SelectItem>
                      <SelectItem value="60">1 saat</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Sistem sağlığı kontrolünün ne sıklıkla yapılacağını belirler. Bu ayar, CPU, bellek ve disk kullanımı gibi sistem kaynaklarının kontrol edilme sıklığını etkiler.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sistem Bilgileri Kartı */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" /> Sistem Bilgileri
              </CardTitle>
              <CardDescription>
                Sunucu ve sistem bilgilerini görüntüleyin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* İşletim Sistemi */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Server className="h-4 w-4 text-primary" />
                    <h3 className="text-sm font-semibold">İşletim Sistemi</h3>
                    <div className="ml-auto text-sm font-medium">
                      {systemInfo.os ?
                        (systemInfo.os.prettyName ||
                          `${systemInfo.os.platform === 'win32' ? 'Windows' :
                            systemInfo.os.platform === 'darwin' ? 'macOS' :
                            systemInfo.os.platform === 'linux' ? (systemInfo.os.distro || 'Linux') :
                            systemInfo.os.platform} ${systemInfo.os.distroVersion || systemInfo.os.release}`) :
                        'N/A'}
                    </div>
                  </div>
                  {systemInfo.os && (
                    <div className="grid grid-cols-2 gap-2 ml-7 mt-2">
                      <div className="flex justify-between">
                        <span className="text-xs text-muted-foreground">Mimari:</span>
                        <span className="text-xs font-medium">{systemInfo.os.arch || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-xs text-muted-foreground">Tür:</span>
                        <span className="text-xs font-medium">{systemInfo.os.type || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-xs text-muted-foreground">Sunucu Adı:</span>
                        <span className="text-xs font-medium">{systemInfo.os.hostname || 'N/A'}</span>
                      </div>
                      {systemInfo.os.kernelVersion && (
                        <div className="flex justify-between">
                          <span className="text-xs text-muted-foreground">Kernel:</span>
                          <span className="text-xs font-medium">{systemInfo.os.kernelVersion}</span>
                        </div>
                      )}
                      {systemInfo.os.distro && !systemInfo.os.prettyName && (
                        <div className="flex justify-between">
                          <span className="text-xs text-muted-foreground">Dağıtım:</span>
                          <span className="text-xs font-medium">{systemInfo.os.distro}</span>
                        </div>
                      )}
                      {systemInfo.os.distroVersion && !systemInfo.os.prettyName && (
                        <div className="flex justify-between">
                          <span className="text-xs text-muted-foreground">Dağıtım Sürümü:</span>
                          <span className="text-xs font-medium">{systemInfo.os.distroVersion}</span>
                        </div>
                      )}
                      {systemInfo.os.version && (
                        <div className="flex justify-between">
                          <span className="text-xs text-muted-foreground">Versiyon:</span>
                          <span className="text-xs font-medium">{systemInfo.os.version}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Çalışma Süresi */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-primary" />
                    <h3 className="text-sm font-semibold">Çalışma Süresi</h3>
                    <div className="ml-auto text-sm font-medium">
                      {formatUptime(systemInfo.uptime)}
                    </div>
                  </div>
                </div>

                {/* CPU Bilgileri */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Cpu className="h-4 w-4 text-primary" />
                    <h3 className="text-sm font-semibold">CPU</h3>
                    <div className="ml-auto text-sm font-medium">
                      {systemInfo.cpu && systemInfo.cpu.length > 0 ? `${systemInfo.cpu.length} Çekirdek` : 'N/A'}
                    </div>
                  </div>
                  {systemInfo.cpu && systemInfo.cpu.length > 0 && (
                    <div className="text-xs text-muted-foreground ml-7">
                      {systemInfo.cpu[0].model}
                    </div>
                  )}
                </div>

                {/* Bellek Bilgileri */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <MemoryStick className="h-4 w-4 text-primary" />
                    <h3 className="text-sm font-semibold">Bellek</h3>
                    <div className="ml-auto text-sm font-medium">
                      {formatBytes(systemInfo.memory?.total || 0)}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2 ml-7">
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">Kullanılan:</span>
                      <span className="text-xs font-medium">{formatBytes(systemInfo.memory?.used || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">Boş:</span>
                      <span className="text-xs font-medium">{formatBytes(systemInfo.memory?.free || 0)}</span>
                    </div>
                  </div>
                </div>

                {/* Disk Bilgileri */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <HardDrive className="h-4 w-4 text-primary" />
                    <h3 className="text-sm font-semibold">Disk</h3>
                    <div className="ml-auto text-sm font-medium">
                      {systemInfo.disk?.total?.size || 'N/A'}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2 ml-7">
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">Kullanılan:</span>
                      <span className="text-xs font-medium">{systemInfo.disk?.total?.used || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">Boş:</span>
                      <span className="text-xs font-medium">{systemInfo.disk?.total?.avail || 'N/A'}</span>
                    </div>
                  </div>
                  {systemInfo.disk?.disks && systemInfo.disk.disks.length > 0 && (
                    <div className="mt-2 ml-7">
                      <div className="text-xs text-muted-foreground mb-1">Disk Birimleri:</div>
                      <div className="space-y-1">
                        {systemInfo.disk.disks.map((disk, index) => {
                          // Disk hedef yolunu düzelt
                          const displayPath = disk.target.replace('sf_', 'sf_');
                          return (
                            <div key={index} className="text-xs grid grid-cols-2">
                              <span className="truncate">{displayPath}</span>
                              <span className="text-right font-medium">{disk.size}</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>


      </form>
      )}
    </div>
  );
};

export default SystemSettings;
