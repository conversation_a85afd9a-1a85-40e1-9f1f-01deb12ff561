import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Label } from '../../components/ui/label';
import { Switch } from '../../components/ui/switch';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Save, RefreshCw, Moon, Sun, Check, Settings, Palette } from 'lucide-react';
import { settingsService } from '../../services/api';
import { useTheme } from '../../contexts/ThemeContext';
import { notificationService } from '../../services/notification-service';
import { usePageLoading } from '../../hooks/useSmartLoading';
import { SmartPageLoading } from '../../components/ui/smart-loading';

const GeneralSettings = () => {
  const { theme, toggleTheme } = useTheme();
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    darkMode: theme === 'dark',
    language: 'tr',
    timezone: 'Europe/Istanbul',
    dateFormat: 'DD.MM.YYYY',
    timeFormat: '24h',
    appTitle: 'NetWatch',
    companyName: 'NetWatch',
  });
  const [saving, setSaving] = useState(false);

  // Smart loading hook
  const pageLoading = usePageLoading(formData, loading);

  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const settingsData = await settingsService.getAll();
      setFormData(prevData => ({
        ...prevData,
        ...settingsData
      }));
    } catch (error) {
      console.error('Ayarlar yüklenirken hata oluştu:', error);
      setError('Ayarlar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Sayfa yüklendiğinde verileri getir
  useEffect(() => {
    loadData();
  }, []);

  // Form değişikliklerini işle
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Switch değişikliklerini işle
  const handleSwitchChange = (name, checked) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));

    // Dark mode değişikliği
    if (name === 'darkMode') {
      toggleTheme();
    }
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Tema seçimi için
  const handleThemeSelect = (selectedTheme) => {
    const isDark = selectedTheme === 'dark';
    setFormData((prev) => ({ ...prev, darkMode: isDark }));

    // Eğer mevcut tema seçilen temadan farklıysa değiştir
    if ((theme === 'dark') !== isDark) {
      toggleTheme();
    }
  };

  // Formu gönder
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    // Başarı mesajını sayfada göstermeyi kaldırdık
    // setSuccess(null);

    try {
      // Dark mode ayarını formdan çıkar
      const { darkMode, ...settingsData } = formData;

      // Ayarları kaydet
      await settingsService.update(settingsData);

      setSaving(false);
      // Sayfada başarı mesajı göstermeyi kaldırdık
      // setSuccess('Ayarlar başarıyla kaydedildi.');

      // Sadece toast bildirimi göster
      notificationService.success('Ayarlar kaydedildi', {
        description: 'Genel ayarlar başarıyla güncellendi.'
      });

      // Artık sayfada mesaj göstermediğimiz için timeout'a gerek yok
      // setTimeout(() => {
      //   setSuccess(null);
      // }, 3000);
    } catch (error) {
      console.error('Ayarlar kaydedilirken hata oluştu:', error);
      setError('Ayarlar kaydedilirken bir hata oluştu.');

      // Hata bildirimi göster
      notificationService.error('Ayarlar kaydedilemedi', {
        description: error.response?.data?.error || 'Ayarlar kaydedilirken bir hata oluştu.'
      });

      setSaving(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Genel Ayarlar</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Uygulama görünümü ve davranışı ile ilgili genel ayarları yapılandırın
          </p>
        </div>
        <div className="flex items-center gap-2 self-end sm:self-auto">
          <Button
            type="button"
            variant="outline"
            onClick={loadData}
            size="default"
          >
            İptal
          </Button>
          <Button
            type="submit"
            disabled={saving}
            size="default"
            form="general-settings-form"
          >
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...
              </>
            ) : (
              'Kaydet'
            )}
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-lg border border-destructive/30">
          <div className="flex items-start">
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Başarı mesajını kaldırdık */}

      {pageLoading.shouldShowFullPageLoading() ? (
        <SmartPageLoading
          loadingType="initial"
          pageTitle="Genel Ayarlar"
          skeletonLayout="default"
        />
      ) : (
        <form id="general-settings-form" onSubmit={handleSubmit}>
        {/* İki sütunlu kart düzeni */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Görünüm ve Kişiselleştirme Kartı */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" /> Görünüm ve Kişiselleştirme
              </CardTitle>
              <CardDescription>
                Uygulamanızın görünümünü, adını ve temasını özelleştirin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="appTitle">Başlık</Label>
                <p className="text-sm text-muted-foreground mb-2">
                  Sidebar'da görüntülenecek başlık.
                </p>
                <Input
                  id="appTitle"
                  name="appTitle"
                  value={formData.appTitle}
                  onChange={handleChange}
                  placeholder="NetWatch"
                  className="mb-4"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="companyName">Uygulama Adı</Label>
                <p className="text-sm text-muted-foreground mb-2">
                  PDF raporlarında görüntülenecek ad.
                </p>
                <Input
                  id="companyName"
                  name="companyName"
                  value={formData.companyName}
                  onChange={handleChange}
                  placeholder="NetWatch"
                  className="mb-4"
                />
              </div>

              <div className="space-y-2">
                <Label>Tema</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  Kontrol paneli için tema seçin.
                </p>
                <div className="grid grid-cols-2 gap-3">
                  {/* Açık Tema Kartı */}
                  <div
                    className={`relative cursor-pointer rounded-lg border-2 transition-all ${theme === 'light' ? 'border-primary' : 'border-transparent hover:border-muted'}`}
                    onClick={() => handleThemeSelect('light')}
                  >
                    {/* Seçim İşareti */}
                    {theme === 'light' && (
                      <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1">
                        <Check className="h-4 w-4" />
                      </div>
                    )}

                    {/* Tema Önizleme */}
                    <div className="bg-white rounded-lg border border-gray-200 p-3">
                      <div className="space-y-2">
                        <div className="h-2 w-4/5 bg-gray-200 rounded"></div>
                        <div className="h-2 w-full bg-gray-200 rounded"></div>
                      </div>
                      <div className="mt-3 space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-gray-300"></div>
                          <div className="h-2 w-3/4 bg-gray-200 rounded"></div>
                        </div>
                      </div>
                    </div>
                    <div className="text-center py-2 font-medium">Light</div>
                  </div>

                  {/* Koyu Tema Kartı */}
                  <div
                    className={`relative cursor-pointer rounded-lg border-2 transition-all ${theme === 'dark' ? 'border-primary' : 'border-transparent hover:border-muted'}`}
                    onClick={() => handleThemeSelect('dark')}
                  >
                    {/* Seçim İşareti */}
                    {theme === 'dark' && (
                      <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1">
                        <Check className="h-4 w-4" />
                      </div>
                    )}

                    {/* Tema Önizleme */}
                    <div className="bg-gray-900 rounded-lg border border-gray-700 p-3">
                      <div className="space-y-2">
                        <div className="h-2 w-4/5 bg-gray-700 rounded"></div>
                        <div className="h-2 w-full bg-gray-700 rounded"></div>
                      </div>
                      <div className="mt-3 space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-gray-600"></div>
                          <div className="h-2 w-3/4 bg-gray-700 rounded"></div>
                        </div>
                      </div>
                    </div>
                    <div className="text-center py-2 font-medium">Dark</div>
                  </div>
                </div>
              </div>

            </CardContent>
          </Card>

          {/* Uygulama Ayarları Kartı */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" /> Uygulama Ayarları
              </CardTitle>
              <CardDescription>
                Genel uygulama ayarlarını yapılandırın
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="language">Dil</Label>
                <Select
                  value={formData.language}
                  onValueChange={(value) => handleSelectChange('language', value)}
                >
                  <SelectTrigger id="language">
                    <SelectValue placeholder="Dil seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tr">Türkçe</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timezone">Zaman Dilimi</Label>
                <Select
                  value={formData.timezone}
                  onValueChange={(value) => handleSelectChange('timezone', value)}
                >
                  <SelectTrigger id="timezone">
                    <SelectValue placeholder="Zaman dilimi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Europe/Istanbul">İstanbul (UTC+3)</SelectItem>
                    <SelectItem value="Europe/London">Londra (UTC+0/+1)</SelectItem>
                    <SelectItem value="America/New_York">New York (UTC-5/-4)</SelectItem>
                    <SelectItem value="Asia/Tokyo">Tokyo (UTC+9)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dateFormat">Tarih Formatı</Label>
                <Select
                  value={formData.dateFormat}
                  onValueChange={(value) => handleSelectChange('dateFormat', value)}
                >
                  <SelectTrigger id="dateFormat">
                    <SelectValue placeholder="Tarih formatı seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DD.MM.YYYY">31.12.2023</SelectItem>
                    <SelectItem value="MM/DD/YYYY">12/31/2023</SelectItem>
                    <SelectItem value="YYYY-MM-DD">2023-12-31</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeFormat">Saat Formatı</Label>
                <Select
                  value={formData.timeFormat}
                  onValueChange={(value) => handleSelectChange('timeFormat', value)}
                >
                  <SelectTrigger id="timeFormat">
                    <SelectValue placeholder="Saat formatı seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="24h">24 Saat (14:30)</SelectItem>
                    <SelectItem value="12h">12 Saat (2:30 PM)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>


        </div>


      </form>
      )}
    </div>
  );
};

export default GeneralSettings;
