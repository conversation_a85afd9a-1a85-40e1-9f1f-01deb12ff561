import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Label } from '../../components/ui/label';
import { Switch } from '../../components/ui/switch';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Save, RefreshCw, Bell, Mail, MessageSquare, Volume2, Send } from 'lucide-react';
import { settingsService, api } from '../../services/api';
import { notificationService } from '../../services/notification-service';

const NotificationSettings = () => {
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    emailNotifications: false,
    emailServer: '',
    emailPort: '',
    emailUser: '',
    emailPassword: '',
    emailFrom: '',
    emailTo: '',
    emailSecure: true,

    notificationRetentionDays: '30',
    // ✅ Yeni bildirim sistemi - Severity bazlı ayarlar
    notifyOnCritical: true,      // Kritik Uyarılar (sistem down, güvenlik)
    notifyOnWarning: true,       // Genel Uyarılar (performans, bağlantı sorunları)
    notifyOnInfo: true,          // Bilgi Mesajları (durum değişiklikleri, güncellemeler)
    notifyOnSuccess: true,       // Başarı Mesajları (işlem tamamlandı, bağlantı kuruldu)
    // ✅ Source bazlı ayarlar
    notifyOnDevice: true,        // Cihaz bildirimleri
    notifyOnSystem: true,        // Sistem bildirimleri
    // Toast ayarları
    toastsEnabled: true,
    toastDuration: '5000',
    toastDeduplication: true,
    toastGrouping: true,
    toastSounds: true,
  });
  const [saving, setSaving] = useState(false);
  const [testingEmail, setTestingEmail] = useState(false);

  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const settingsData = await settingsService.getAll();

      // Eğer toastDuration "0" (manuel kapatma) ise varsayılan değere çevir
      if (settingsData.toastDuration === "0") {
        settingsData.toastDuration = "5000"; // Varsayılan 5 saniye
      }

      setFormData(prevData => ({
        ...prevData,
        ...settingsData
      }));
    } catch (error) {
      console.error('Ayarlar yüklenirken hata oluştu:', error);
      setError('Ayarlar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Sayfa yüklendiğinde verileri getir
  useEffect(() => {
    loadData();
  }, []);

  // Form değişikliklerini işle
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Switch değişikliklerini işle
  const handleSwitchChange = (name, checked) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // E-posta adres sayısını hesapla
  const getEmailCount = () => {
    if (!formData.emailTo) return 0;
    return formData.emailTo
      .split(',')
      .map(email => email.trim())
      .filter(email => email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))
      .length;
  };

  // Test e-postası gönder
  const handleTestEmail = async () => {
    setTestingEmail(true);
    setError(null);

    try {
      const response = await api.post('/settings/test-email');

      if (response.data.success) {
        notificationService.success('Test e-postası gönderildi', {
          description: 'E-posta ayarlarınız doğru şekilde yapılandırılmıştır.',
          persistent: true,
          category: 'system'
        });
      }
    } catch (error) {
      console.error('Test e-postası gönderme hatası:', error);
      const errorMessage = error.response?.data?.error || 'Test e-postası gönderilemedi';

      setError(errorMessage);
      notificationService.error('Test e-postası gönderilemedi', {
        description: errorMessage
      });
    } finally {
      setTestingEmail(false);
    }
  };

  // Formu gönder
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    // Başarı mesajını sayfada göstermeyi kaldırdık
    // setSuccess(null);

    try {
      await settingsService.update(formData);
      setSaving(false);
      // Sayfada başarı mesajı göstermeyi kaldırdık
      // setSuccess('Ayarlar başarıyla kaydedildi.');

      // Sadece toast bildirimi göster
      notificationService.success('Bildirim ayarları kaydedildi', {
        description: 'Bildirim ayarları başarıyla güncellendi.',
        persistent: true, // Bildirim panelinde de göster
        category: 'system'
      });

      // Artık sayfada mesaj göstermediğimiz için timeout'a gerek yok
      // setTimeout(() => {
      //   setSuccess(null);
      // }, 3000);
    } catch (error) {
      console.error('Ayarlar kaydedilirken hata oluştu:', error);
      setError('Ayarlar kaydedilirken bir hata oluştu.');

      // Hata bildirimi göster
      notificationService.error('Bildirim ayarları kaydedilemedi', {
        description: error.response?.data?.error || 'Bildirim ayarları kaydedilirken bir hata oluştu.'
      });

      setSaving(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Bildirim Ayarları</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Bildirim tercihleri ve kanallarını yapılandırın
          </p>
        </div>
        <div className="flex items-center gap-2 self-end sm:self-auto">
          <Button
            type="button"
            variant="outline"
            onClick={loadData}
            size="default"
          >
            İptal
          </Button>
          <Button
            type="submit"
            disabled={saving}
            size="default"
            form="notification-settings-form"
          >
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...
              </>
            ) : (
              'Kaydet'
            )}
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-lg border border-destructive/30">
          <div className="flex items-start">
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Başarı mesajını kaldırdık */}

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <div className="flex flex-col items-center gap-2">
            <RefreshCw className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Ayarlar yükleniyor...</p>
          </div>
        </div>
      ) : (
        <form id="notification-settings-form" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Bildirim Tercihleri */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" /> Bildirim Tercihleri
              </CardTitle>
              <CardDescription>
                Hangi olaylar için bildirim almak istediğinizi yapılandırın
              </CardDescription>
            </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifyOnCritical">
                    Kritik Uyarılar
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Sistem çökmesi, güvenlik uyarıları, kritik cihaz arızaları
                  </p>
                </div>
                <Switch
                  id="notifyOnCritical"
                  checked={formData.notifyOnCritical}
                  onCheckedChange={(checked) => handleSwitchChange('notifyOnCritical', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifyOnWarning">
                    Genel Uyarılar
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Performans düşüşü, bağlantı sorunları, cihaz uyarıları
                  </p>
                </div>
                <Switch
                  id="notifyOnWarning"
                  checked={formData.notifyOnWarning}
                  onCheckedChange={(checked) => handleSwitchChange('notifyOnWarning', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifyOnInfo">
                    Bilgi Mesajları
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Durum değişiklikleri, sistem güncellemeleri, genel bilgiler
                  </p>
                </div>
                <Switch
                  id="notifyOnInfo"
                  checked={formData.notifyOnInfo}
                  onCheckedChange={(checked) => handleSwitchChange('notifyOnInfo', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifyOnSuccess">
                    Başarı Mesajları
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    İşlem tamamlandı, bağlantı kuruldu, başarılı operasyonlar
                  </p>
                </div>
                <Switch
                  id="notifyOnSuccess"
                  checked={formData.notifyOnSuccess}
                  onCheckedChange={(checked) => handleSwitchChange('notifyOnSuccess', checked)}
                />
              </div>

              <div className="border-t pt-4 mt-4">
                <h4 className="text-sm font-medium mb-3">Kaynak Türü Filtreleri</h4>

                <div className="flex items-center justify-between mb-3">
                  <div className="space-y-0.5">
                    <Label htmlFor="notifyOnDevice">
                      Cihaz Bildirimleri
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Cihaz durumu, performans, bağlantı sorunları
                    </p>
                  </div>
                  <Switch
                    id="notifyOnDevice"
                    checked={formData.notifyOnDevice}
                    onCheckedChange={(checked) => handleSwitchChange('notifyOnDevice', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="notifyOnSystem">
                      Sistem Bildirimleri
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Sistem durumu, kullanıcı işlemleri, güvenlik olayları
                    </p>
                  </div>
                  <Switch
                    id="notifyOnSystem"
                    checked={formData.notifyOnSystem}
                    onCheckedChange={(checked) => handleSwitchChange('notifyOnSystem', checked)}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

          {/* E-posta Bildirimleri */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" /> E-posta Bildirimleri
              </CardTitle>
              <CardDescription>
                E-posta bildirim ayarlarını yapılandırın
              </CardDescription>
            </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="emailNotifications">E-posta Bildirimleri</Label>
                <p className="text-sm text-muted-foreground">
                  Uyarılar için e-posta bildirimleri alın
                </p>
              </div>
              <Switch
                id="emailNotifications"
                checked={formData.emailNotifications}
                onCheckedChange={(checked) => handleSwitchChange('emailNotifications', checked)}
              />
            </div>

            {formData.emailNotifications && (
              <div className="space-y-4 pt-4 border-t">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="emailServer">SMTP Sunucusu</Label>
                    <Input
                      id="emailServer"
                      name="emailServer"
                      value={formData.emailServer}
                      onChange={handleChange}
                      placeholder="Örn: smtp.gmail.com"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="emailPort">SMTP Port</Label>
                    <Input
                      id="emailPort"
                      name="emailPort"
                      value={formData.emailPort}
                      onChange={handleChange}
                      placeholder="Örn: 587"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="emailUser">SMTP Kullanıcı Adı</Label>
                    <Input
                      id="emailUser"
                      name="emailUser"
                      value={formData.emailUser}
                      onChange={handleChange}
                      placeholder="Örn: <EMAIL>"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="emailPassword">SMTP Şifresi</Label>
                    <Input
                      id="emailPassword"
                      name="emailPassword"
                      type="password"
                      value={formData.emailPassword}
                      onChange={handleChange}
                      placeholder="••••••••"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="emailFrom">Gönderen E-posta</Label>
                    <Input
                      id="emailFrom"
                      name="emailFrom"
                      value={formData.emailFrom}
                      onChange={handleChange}
                      placeholder="Örn: <EMAIL>"
                    />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Label htmlFor="emailTo">Alıcı E-posta Adresleri</Label>
                      <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                        Çoklu Adres Destekli
                      </span>
                      {getEmailCount() > 0 && (
                        <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">
                          {getEmailCount()} Adres
                        </span>
                      )}
                    </div>
                    <Input
                      id="emailTo"
                      name="emailTo"
                      value={formData.emailTo}
                      onChange={handleChange}
                      placeholder="Örn: <EMAIL>, <EMAIL>"
                      className="min-h-[40px]"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="emailSecure">Güvenli Bağlantı (SSL/TLS)</Label>
                    <p className="text-sm text-muted-foreground">
                      SMTP sunucusuna güvenli bağlantı kullanın
                    </p>
                  </div>
                  <Switch
                    id="emailSecure"
                    checked={formData.emailSecure}
                    onCheckedChange={(checked) => handleSwitchChange('emailSecure', checked)}
                  />
                </div>

                {/* Test E-postası Butonu */}
                <div className="pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleTestEmail}
                    disabled={testingEmail || !formData.emailServer || !formData.emailUser}
                    className="w-full"
                  >
                    {testingEmail ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Test E-postası Gönderiliyor...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-4 w-4" /> Test E-postası Gönder
                      </>
                    )}
                  </Button>
                  <p className="text-xs text-muted-foreground mt-2 text-center">
                    E-posta ayarlarınızı test etmek için bu butonu kullanın
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {/* Toast Bildirimleri */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" /> Toast Bildirimleri
              </CardTitle>
              <CardDescription>
                Ekran bildirimleri (toast) ayarlarını yapılandırın
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="toastsEnabled">Toast Bildirimleri</Label>
                  <p className="text-sm text-muted-foreground">
                    Ekranda görünen bildirim mesajlarını etkinleştirin
                  </p>
                </div>
                <Switch
                  id="toastsEnabled"
                  checked={formData.toastsEnabled}
                  onCheckedChange={(checked) => handleSwitchChange('toastsEnabled', checked)}
                />
              </div>

              {formData.toastsEnabled && (
                <div className="space-y-4 pt-4 border-t">


                  <div className="space-y-2">
                    <Label htmlFor="toastDuration">Toast Süresi</Label>
                    <Select
                      value={formData.toastDuration}
                      onValueChange={(value) => handleSelectChange('toastDuration', value)}
                    >
                      <SelectTrigger id="toastDuration">
                        <SelectValue placeholder="Süre seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="3000">3 saniye</SelectItem>
                        <SelectItem value="5000">5 saniye (Varsayılan)</SelectItem>
                        <SelectItem value="8000">8 saniye</SelectItem>
                        <SelectItem value="10000">10 saniye</SelectItem>
                        <SelectItem value="15000">15 saniye</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="toastDeduplication">Tekrar Önleme</Label>
                      <p className="text-sm text-muted-foreground">
                        Aynı bildirimden birden fazla toast göstermeyi engelle
                      </p>
                    </div>
                    <Switch
                      id="toastDeduplication"
                      checked={formData.toastDeduplication}
                      onCheckedChange={(checked) => handleSwitchChange('toastDeduplication', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="toastGrouping">Akıllı Gruplama</Label>
                      <p className="text-sm text-muted-foreground">
                        Benzer bildirimleri tek toast'ta grupla
                      </p>
                    </div>
                    <Switch
                      id="toastGrouping"
                      checked={formData.toastGrouping}
                      onCheckedChange={(checked) => handleSwitchChange('toastGrouping', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="toastSounds">Ses Bildirimleri</Label>
                      <p className="text-sm text-muted-foreground">
                        Toast bildirimleri için ses çal
                      </p>
                    </div>
                    <Switch
                      id="toastSounds"
                      checked={formData.toastSounds}
                      onCheckedChange={(checked) => handleSwitchChange('toastSounds', checked)}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Bildirim Saklama */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" /> Bildirim Saklama
              </CardTitle>
              <CardDescription>
                Bildirim saklama ve temizleme ayarlarını yapılandırın
              </CardDescription>
            </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="notificationRetentionDays">Bildirim Saklama Süresi</Label>
              <Select
                value={formData.notificationRetentionDays}
                onValueChange={(value) => handleSelectChange('notificationRetentionDays', value)}
              >
                <SelectTrigger id="notificationRetentionDays">
                  <SelectValue placeholder="Saklama süresi seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 gün</SelectItem>
                  <SelectItem value="7">7 gün</SelectItem>
                  <SelectItem value="14">14 gün</SelectItem>
                  <SelectItem value="30">30 gün</SelectItem>
                  <SelectItem value="60">60 gün</SelectItem>
                  <SelectItem value="90">90 gün</SelectItem>
                  <SelectItem value="180">180 gün</SelectItem>
                  <SelectItem value="365">1 yıl</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Bildirimlerin ne kadar süre saklanacağını belirler. Bu süreden daha eski bildirimler her saat başı otomatik olarak temizlenecektir.
              </p>
            </div>
          </CardContent>
        </Card>
        </div>


      </form>
      )}
    </div>
  );
};

export default NotificationSettings;
