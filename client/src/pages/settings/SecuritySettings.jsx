import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Label } from '../../components/ui/label';
import { Switch } from '../../components/ui/switch';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Save, RefreshCw, Shield, Lock, Key, UserCog, AlertTriangle } from 'lucide-react';
import { settingsService } from '../../services/api';
import { notificationService } from '../../services/notification-service';

const SecuritySettings = () => {
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    sessionTimeout: '60', // Dakika cinsinden
    passwordPolicy: 'medium',
    passwordExpiryDays: '90',
    maxLoginAttempts: '5',
    accountLockDuration: '30', // Dakika cinsinden

    bruteForceProtection: true,
    autoLogout: true,
  });
  const [saving, setSaving] = useState(false);

  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const settingsData = await settingsService.getAll();
      setFormData(prevData => ({
        ...prevData,
        ...settingsData
      }));
    } catch (error) {
      console.error('Ayarlar yüklenirken hata oluştu:', error);
      setError('Ayarlar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Sayfa yüklendiğinde verileri getir
  useEffect(() => {
    loadData();
  }, []);

  // Form değişikliklerini işle
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Switch değişikliklerini işle
  const handleSwitchChange = (name, checked) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Formu gönder
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    // Başarı mesajını sayfada göstermeyi kaldırdık
    // setSuccess(null);

    try {
      // Ayarları kaydet
      await settingsService.update(formData);

      setSaving(false);
      // Sayfada başarı mesajı göstermeyi kaldırdık
      // setSuccess('Güvenlik ayarları başarıyla kaydedildi.');

      // Sadece toast bildirimi göster
      notificationService.success('Güvenlik ayarları kaydedildi', {
        description: 'Güvenlik ayarları başarıyla güncellendi.',
        persistent: true, // Bildirim panelinde de göster
        category: 'security'
      });

      // Artık sayfada mesaj göstermediğimiz için timeout'a gerek yok
      // setTimeout(() => {
      //   setSuccess(null);
      // }, 3000);
    } catch (error) {
      console.error('Ayarlar kaydedilirken hata oluştu:', error);
      setError('Ayarlar kaydedilirken bir hata oluştu.');

      // Hata bildirimi göster
      notificationService.error('Güvenlik ayarları kaydedilemedi', {
        description: error.response?.data?.error || 'Güvenlik ayarları kaydedilirken bir hata oluştu.',
        category: 'security'
      });

      setSaving(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Güvenlik Ayarları</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Uygulama güvenliği ve erişim kontrolü ile ilgili ayarları yapılandırın
          </p>
        </div>
        <div className="flex items-center gap-2 self-end sm:self-auto">
          <Button
            type="button"
            variant="outline"
            onClick={loadData}
            size="default"
          >
            İptal
          </Button>
          <Button
            type="submit"
            disabled={saving}
            size="default"
            form="security-settings-form"
          >
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...
              </>
            ) : (
              'Kaydet'
            )}
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-lg border border-destructive/30">
          <div className="flex items-start">
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Başarı mesajını kaldırdık */}

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <div className="flex flex-col items-center gap-2">
            <RefreshCw className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Ayarlar yükleniyor...</p>
          </div>
        </div>
      ) : (
        <form id="security-settings-form" onSubmit={handleSubmit}>
        {/* İki sütunlu kart düzeni */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Oturum Güvenliği Kartı */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" /> Oturum Güvenliği
              </CardTitle>
              <CardDescription>
                Kullanıcı oturumları ve erişim kontrolü ayarları
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sessionTimeout">Oturum Zaman Aşımı</Label>
                <Select
                  value={formData.sessionTimeout}
                  onValueChange={(value) => handleSelectChange('sessionTimeout', value)}
                >
                  <SelectTrigger id="sessionTimeout">
                    <SelectValue placeholder="Oturum zaman aşımı seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 dakika (Yüksek güvenlik)</SelectItem>
                    <SelectItem value="30">30 dakika (Orta güvenlik)</SelectItem>
                    <SelectItem value="60">60 dakika (Normal)</SelectItem>
                    <SelectItem value="120">120 dakika (Düşük güvenlik)</SelectItem>
                    <SelectItem value="240">240 dakika (Çok düşük güvenlik)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Kullanıcı hareketsizliğinde oturumun sonlandırılacağı süre
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="autoLogout">Otomatik Çıkış</Label>
                  <p className="text-sm text-muted-foreground">
                    Belirtilen süre sonunda otomatik çıkış yap
                  </p>
                </div>
                <Switch
                  id="autoLogout"
                  checked={formData.autoLogout}
                  onCheckedChange={(checked) => handleSwitchChange('autoLogout', checked)}
                />
              </div>



              <div className="space-y-2">
                <Label htmlFor="maxLoginAttempts">Maksimum Giriş Denemesi</Label>
                <Select
                  value={formData.maxLoginAttempts}
                  onValueChange={(value) => handleSelectChange('maxLoginAttempts', value)}
                >
                  <SelectTrigger id="maxLoginAttempts">
                    <SelectValue placeholder="Maksimum giriş denemesi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">3 deneme (Yüksek güvenlik)</SelectItem>
                    <SelectItem value="5">5 deneme (Normal)</SelectItem>
                    <SelectItem value="10">10 deneme (Düşük güvenlik)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Hesabın kilitlenmesi için maksimum başarısız giriş denemesi sayısı
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="accountLockDuration">Hesap Kilitleme Süresi</Label>
                <Select
                  value={formData.accountLockDuration}
                  onValueChange={(value) => handleSelectChange('accountLockDuration', value)}
                >
                  <SelectTrigger id="accountLockDuration">
                    <SelectValue placeholder="Hesap kilitleme süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 dakika (Kısa)</SelectItem>
                    <SelectItem value="15">15 dakika (Orta)</SelectItem>
                    <SelectItem value="30">30 dakika (Normal)</SelectItem>
                    <SelectItem value="60">60 dakika (Uzun)</SelectItem>
                    <SelectItem value="120">120 dakika (Çok uzun)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Hesabın kilitli kalacağı süre
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Şifre ve Erişim Güvenliği Kartı */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" /> Şifre ve Erişim Güvenliği
              </CardTitle>
              <CardDescription>
                Şifre politikaları ve erişim kısıtlamaları
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="passwordPolicy">Şifre Politikası</Label>
                <Select
                  value={formData.passwordPolicy}
                  onValueChange={(value) => handleSelectChange('passwordPolicy', value)}
                >
                  <SelectTrigger id="passwordPolicy">
                    <SelectValue placeholder="Şifre politikası seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Düşük (En az 6 karakter)</SelectItem>
                    <SelectItem value="medium">Orta (En az 8 karakter, 1 büyük harf, 1 rakam)</SelectItem>
                    <SelectItem value="high">Yüksek (En az 10 karakter, 1 büyük harf, 1 rakam, 1 özel karakter)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Kullanıcı şifrelerinin karmaşıklık seviyesi
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="passwordExpiryDays">Şifre Geçerlilik Süresi</Label>
                <Select
                  value={formData.passwordExpiryDays}
                  onValueChange={(value) => handleSelectChange('passwordExpiryDays', value)}
                >
                  <SelectTrigger id="passwordExpiryDays">
                    <SelectValue placeholder="Şifre geçerlilik süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Süresiz (Güvenlik riski)</SelectItem>
                    <SelectItem value="30">30 gün (Yüksek güvenlik)</SelectItem>
                    <SelectItem value="60">60 gün (Orta güvenlik)</SelectItem>
                    <SelectItem value="90">90 gün (Normal)</SelectItem>
                    <SelectItem value="180">180 gün (Düşük güvenlik)</SelectItem>
                    <SelectItem value="365">365 gün (Çok düşük güvenlik)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Şifrelerin değiştirilmesi gereken süre
                </p>
              </div>




            </CardContent>
          </Card>
        </div>


      </form>
      )}
    </div>
  );
};

export default SecuritySettings;
