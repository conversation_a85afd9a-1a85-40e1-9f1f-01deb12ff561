import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
// Form içi hata mesajları kaldırıldığı için Alert bileşenleri artık gerekli değil
import { UserPlus } from 'lucide-react';
import { notificationService } from '../services/notification-service';
import { settingsService } from '../services/api';


const Register = () => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  // Form içi hata mesajları kaldırıldı - sadece toast bildirimleri kullanılıyor
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [passwordPlaceholder, setPasswordPlaceholder] = useState('En az 8 karakter, 1 büyük harf, 1 rakam'); // Varsayılan medium policy

  const { register } = useAuth();
  const navigate = useNavigate();

  // Şifre politikasını yükle ve placeholder'ı güncelle
  useEffect(() => {
    const loadPasswordPolicy = async () => {
      try {
        const settings = await settingsService.getAll();
        const policy = settings.passwordPolicy || 'medium';

        // Şifre politikasına göre placeholder'ı ayarla
        const placeholders = {
          low: 'En az 6 karakter',
          medium: 'En az 8 karakter, 1 büyük harf, 1 rakam',
          high: 'En az 10 karakter, 1 büyük harf, 1 rakam, 1 özel karakter'
        };

        // Sadece farklıysa güncelle (yanıp sönmeyi önlemek için)
        const newPlaceholder = placeholders[policy] || placeholders.medium;
        if (newPlaceholder !== passwordPlaceholder) {
          setPasswordPlaceholder(newPlaceholder);
        }
      } catch (error) {
        console.error('Şifre politikası yüklenirken hata:', error);
        // Hata durumunda varsayılan placeholder (zaten medium olarak ayarlı)
      }
    };

    loadPasswordPolicy();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    if (!formData.username.trim() || !formData.email.trim() || !formData.password || !formData.confirmPassword) {
      notificationService.error('Eksik bilgi', {
        description: 'Tüm alanları doldurun'
      });
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      notificationService.error('Şifre hatası', {
        description: 'Şifreler eşleşmiyor'
      });
      return false;
    }

    // Şifre politikası kontrolü backend'de yapılıyor, burada sadece boş olup olmadığını kontrol ediyoruz

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      notificationService.error('E-posta hatası', {
        description: 'Geçerli bir e-posta adresi girin'
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // HTML5 validasyonunu devre dışı bırak ve kendi validasyonumuzu kullan
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Şifre doğrulama alanını kaldır
      const { confirmPassword, ...userData } = formData;

      await register(userData);

      // Başarı bildirimi göster
      notificationService.success('Kayıt başarılı', {
        description: 'Hesabınız başarıyla oluşturuldu. Hoş geldiniz!',
        persistent: true,
        category: 'user'
      });

      navigate('/');
    } catch (err) {
      console.error('Register error:', err);

      // Backend'den gelen hata mesajını parse et
      let errorMessage = 'Kayıt işlemi başarısız oldu. Lütfen tekrar deneyin.';

      if (err.response?.data?.errors && Array.isArray(err.response.data.errors)) {
        // Validation hatalarını birleştir
        errorMessage = err.response.data.errors.map(error => error.msg).join(', ');
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      }

      // Spesifik hata türlerine göre farklı başlıklar
      let errorTitle = 'Kayıt başarısız';
      if (errorMessage.includes('kullanıcı adı') || errorMessage.includes('username')) {
        errorTitle = 'Kullanıcı adı hatası';
      } else if (errorMessage.includes('e-posta') || errorMessage.includes('email')) {
        errorTitle = 'E-posta hatası';
      } else if (errorMessage.includes('şifre') || errorMessage.includes('password')) {
        errorTitle = 'Şifre hatası';
      } else if (errorMessage.includes('mevcut') || errorMessage.includes('zaten')) {
        errorTitle = 'Kullanıcı zaten mevcut';
      }

      // Hata bildirimi göster
      notificationService.error(errorTitle, {
        description: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">

      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold flex items-center gap-2">
            <UserPlus className="h-6 w-6" />
            Kayıt Ol
          </CardTitle>
          <CardDescription>
            NetWatch'u kullanmak için hesap oluşturun
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Kullanıcı Adı</Label>
              <Input
                id="username"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleChange}
                placeholder="Kullanıcı adınızı girin"
                disabled={isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">E-posta</Label>
              <Input
                id="email"
                name="email"
                type="text"
                value={formData.email}
                onChange={handleChange}
                placeholder="E-posta adresinizi girin"
                disabled={isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Şifre</Label>
              <Input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                placeholder={passwordPlaceholder}
                disabled={isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Şifre Tekrar</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Şifrenizi tekrar girin"
                disabled={isSubmitting}
              />
            </div>

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? 'Kayıt Yapılıyor...' : 'Kayıt Ol'}
            </Button>
          </form>
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center text-muted-foreground">
            Zaten hesabınız var mı?{' '}
            <Link to="/login" className="text-primary hover:underline">
              Giriş Yap
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Register;
