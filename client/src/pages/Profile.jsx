import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { userService, settingsService } from '../services/api';
import authService from '../services/auth';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { User, Key } from 'lucide-react';
import { Separator } from '../components/ui/separator';
import { notificationService } from '../services/notification-service';

const Profile = () => {
  const { user, updateUser } = useAuth();

  const [profileData, setProfileData] = useState({
    username: user?.username || '',
    email: user?.email || ''
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Form içi hata mesajları kaldırıldı - sadece toast bildirimleri kullanılıyor

  const [isProfileSubmitting, setIsProfileSubmitting] = useState(false);
  const [isPasswordSubmitting, setIsPasswordSubmitting] = useState(false);

  // Şifre politikası için state
  const [passwordPlaceholder, setPasswordPlaceholder] = useState('En az 8 karakter, 1 büyük harf, 1 rakam'); // Varsayılan medium policy

  // Şifre politikasını yükle ve placeholder'ı güncelle
  useEffect(() => {
    const loadPasswordPolicy = async () => {
      try {
        const settings = await settingsService.getAll();
        const policy = settings.passwordPolicy || 'medium';

        // Şifre politikasına göre placeholder'ı ayarla
        const placeholders = {
          low: 'En az 6 karakter',
          medium: 'En az 8 karakter, 1 büyük harf, 1 rakam',
          high: 'En az 10 karakter, 1 büyük harf, 1 rakam, 1 özel karakter'
        };

        // Sadece farklıysa güncelle (yanıp sönmeyi önlemek için)
        const newPlaceholder = placeholders[policy] || placeholders.medium;
        if (newPlaceholder !== passwordPlaceholder) {
          setPasswordPlaceholder(newPlaceholder);
        }
      } catch (error) {
        console.error('Şifre politikası yüklenirken hata:', error);
        // Hata durumunda varsayılan placeholder (zaten medium olarak ayarlı)
      }
    };

    loadPasswordPolicy();
  }, []);

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({ ...prev, [name]: value }));
  };

  const handleProfileSubmit = async (e) => {
    e.preventDefault();

    setIsProfileSubmitting(true);

    try {
      const updatedUser = await userService.update(user.id, profileData);
      updateUser(updatedUser);

      // Başarı bildirimi göster
      notificationService.success('Profil güncellendi', {
        description: 'Profil bilgileriniz başarıyla güncellendi.'
      });
    } catch (err) {
      // Hata bildirimi göster
      notificationService.error('Profil güncellenemedi', {
        description: err.response?.data?.error || 'Profil bilgileriniz güncellenirken bir hata oluştu.'
      });
    } finally {
      setIsProfileSubmitting(false);
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      notificationService.error('Şifre hatası', {
        description: 'Şifreler eşleşmiyor'
      });
      return;
    }

    // Şifre politikası kontrolü backend'de yapılıyor

    setIsPasswordSubmitting(true);

    try {
      await authService.changePassword(passwordData.currentPassword, passwordData.newPassword);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      // Başarı bildirimi göster
      notificationService.success('Şifre değiştirildi', {
        description: 'Şifreniz başarıyla değiştirildi.',
        persistent: true,
        category: 'security'
      });
    } catch (err) {
      // Hata bildirimi göster
      notificationService.error('Şifre değiştirilemedi', {
        description: err.response?.data?.error || 'Şifreniz değiştirilirken bir hata oluştu.'
      });
    } finally {
      setIsPasswordSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <h1 className="text-2xl font-bold tracking-tight mb-6 flex items-center gap-3">
        <User className="h-6 w-6" />
        Profil Ayarları
      </h1>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Profil Bilgileri
          </TabsTrigger>
          <TabsTrigger value="password" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            Şifre Değiştir
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profil Bilgileri
              </CardTitle>
              <CardDescription>
                Kişisel bilgilerinizi güncelleyin
              </CardDescription>
            </CardHeader>

            <CardContent>
              <form onSubmit={handleProfileSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Kullanıcı Adı</Label>
                  <Input
                    id="username"
                    name="username"
                    value={profileData.username}
                    onChange={handleProfileChange}
                    disabled={isProfileSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-posta</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={profileData.email}
                    onChange={handleProfileChange}
                    disabled={isProfileSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Rol</Label>
                  <Input
                    value={user?.role === 'admin' ? 'Yönetici' : 'Kullanıcı'}
                    disabled
                  />
                  <p className="text-sm text-muted-foreground">
                    Rol değişikliği için yönetici ile iletişime geçin
                  </p>
                </div>

                <Separator className="my-4" />

                <Button type="submit" disabled={isProfileSubmitting}>
                  {isProfileSubmitting ? 'Kaydediliyor...' : 'Değişiklikleri Kaydet'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="password">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Şifre Değiştir
              </CardTitle>
              <CardDescription>
                Hesabınızın güvenliği için şifrenizi düzenli olarak değiştirin
              </CardDescription>
            </CardHeader>

            <CardContent>
              <form onSubmit={handlePasswordSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Mevcut Şifre</Label>
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    disabled={isPasswordSubmitting}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="newPassword">Yeni Şifre</Label>
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    disabled={isPasswordSubmitting}
                    placeholder={passwordPlaceholder}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Yeni Şifre Tekrar</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    disabled={isPasswordSubmitting}
                    required
                  />
                </div>

                <Separator className="my-4" />

                <Button type="submit" disabled={isPasswordSubmitting}>
                  {isPasswordSubmitting ? 'Şifre Değiştiriliyor...' : 'Şifreyi Değiştir'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Profile;
