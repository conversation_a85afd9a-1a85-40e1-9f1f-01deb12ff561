import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription } from '../components/ui/alert';
import { notificationService } from '../services/notification-service';
import authService from '../services/auth';
import { settingsService } from '../services/api';
import { ArrowLeft, CheckCircle, AlertTriangle, Eye, EyeOff } from 'lucide-react';

const ResetPassword = () => {
  const { token } = useParams();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');
  const [tokenData, setTokenData] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordPlaceholder, setPasswordPlaceholder] = useState('En az 8 karakter, 1 büyük harf, 1 rakam');

  // Token doğrulama ve şifre politikası yükleme
  useEffect(() => {
    const verifyTokenAndLoadPolicy = async () => {
      try {
        // Şifre politikasını yükle
        const settings = await settingsService.getAll();
        const policy = settings.passwordPolicy || 'medium';
        
        const placeholders = {
          low: 'En az 6 karakter',
          medium: 'En az 8 karakter, 1 büyük harf, 1 rakam',
          high: 'En az 10 karakter, 1 büyük harf, 1 rakam, 1 özel karakter'
        };
        
        setPasswordPlaceholder(placeholders[policy] || placeholders.medium);

        // Token'ı doğrula
        const response = await authService.verifyResetToken(token);
        setTokenData(response);
        
      } catch (err) {
        console.error('Token verification error:', err);
        
        let errorMessage = 'Geçersiz veya süresi dolmuş bağlantı';
        
        if (err.response?.data?.code === 'INVALID_TOKEN') {
          errorMessage = 'Bu şifre sıfırlama bağlantısı geçersiz veya süresi dolmuş.';
        } else if (err.response?.data?.code === 'USER_NOT_FOUND') {
          errorMessage = 'Kullanıcı bulunamadı.';
        }
        
        setError(errorMessage);
        
        notificationService.error('Geçersiz bağlantı', {
          description: errorMessage,
          persistent: true
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (token) {
      verifyTokenAndLoadPolicy();
    } else {
      setError('Geçersiz şifre sıfırlama bağlantısı');
      setIsLoading(false);
    }
  }, [token]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Form validasyonu
    if (!formData.password || !formData.confirmPassword) {
      notificationService.error('Eksik bilgi', {
        description: 'Tüm alanları doldurunuz'
      });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      notificationService.error('Şifre uyumsuzluğu', {
        description: 'Şifreler eşleşmiyor'
      });
      return;
    }

    if (formData.password.length < 6) {
      notificationService.error('Geçersiz şifre', {
        description: 'Şifre en az 6 karakter olmalıdır'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      await authService.resetPassword(token, formData.password);
      
      setIsSuccess(true);
      
      notificationService.success('Şifre sıfırlandı', {
        description: 'Şifreniz başarıyla sıfırlandı. Artık yeni şifrenizle giriş yapabilirsiniz.',
        persistent: true,
        category: 'security'
      });

      // 3 saniye sonra login sayfasına yönlendir
      setTimeout(() => {
        navigate('/login');
      }, 3000);

    } catch (err) {
      console.error('Reset password error:', err);

      let errorMessage = 'Şifre sıfırlanırken bir hata oluştu';

      if (err.response?.data?.code === 'INVALID_TOKEN') {
        errorMessage = 'Bu şifre sıfırlama bağlantısı geçersiz veya süresi dolmuş.';
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      }

      notificationService.error('Şifre sıfırlanamadı', {
        description: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Bağlantı doğrulanıyor...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Başarı durumu
  if (isSuccess) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1 text-center">
            <div className="flex justify-center mb-4">
              <CheckCircle className="h-16 w-16 text-green-500" />
            </div>
            <CardTitle className="text-2xl font-bold">Şifre Sıfırlandı</CardTitle>
            <CardDescription>
              Şifreniz başarıyla sıfırlandı
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Şifreniz başarıyla sıfırlandı. Artık yeni şifrenizle giriş yapabilirsiniz.
                3 saniye sonra giriş sayfasına yönlendirileceksiniz.
              </AlertDescription>
            </Alert>
          </CardContent>

          <CardFooter>
            <Link to="/login" className="w-full">
              <Button className="w-full gap-2">
                <ArrowLeft className="h-4 w-4" />
                Giriş Sayfasına Git
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Hata durumu
  if (error && !tokenData) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1 text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="h-16 w-16 text-destructive" />
            </div>
            <CardTitle className="text-2xl font-bold">Geçersiz Bağlantı</CardTitle>
            <CardDescription>
              Bu şifre sıfırlama bağlantısı kullanılamıyor
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>

            <div className="text-sm text-muted-foreground space-y-2">
              <p>• Bağlantının süresi dolmuş olabilir (1 saat)</p>
              <p>• Bağlantı daha önce kullanılmış olabilir</p>
              <p>• Yeni bir şifre sıfırlama talebi yapabilirsiniz</p>
            </div>
          </CardContent>

          <CardFooter className="flex flex-col space-y-2">
            <Link to="/forgot-password" className="w-full">
              <Button className="w-full">
                Yeni Şifre Sıfırlama Talebi
              </Button>
            </Link>
            <Link to="/login" className="w-full">
              <Button variant="outline" className="w-full gap-2">
                <ArrowLeft className="h-4 w-4" />
                Giriş Sayfasına Dön
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Şifre sıfırlama formu
  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Yeni Şifre Belirleyin</CardTitle>
          <CardDescription>
            {tokenData?.email && (
              <>
                <strong>{tokenData.email}</strong> için yeni şifrenizi belirleyin
              </>
            )}
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">Yeni Şifre</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder={passwordPlaceholder}
                  disabled={isSubmitting}
                  autoComplete="new-password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isSubmitting}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Yeni Şifre (Tekrar)</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="Şifrenizi tekrar girin"
                  disabled={isSubmitting}
                  autoComplete="new-password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isSubmitting}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? 'Şifre Sıfırlanıyor...' : 'Şifremi Sıfırla'}
            </Button>
          </form>
        </CardContent>

        <CardFooter>
          <Link to="/login" className="w-full">
            <Button variant="outline" className="w-full gap-2">
              <ArrowLeft className="h-4 w-4" />
              Giriş Sayfasına Dön
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ResetPassword;
