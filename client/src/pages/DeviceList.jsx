import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { notificationService } from '../services/notification-service';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import { Label } from '../components/ui/label';
import { Checkbox } from '../components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../components/ui/tooltip';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../components/ui/table';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../components/ui/dialog';
import { STATUS_TYPES, STATUS_BG_COLORS } from '../lib/theme';

// Dialog bileşenleri
import DeviceAddDialog from '../components/DeviceAddDialog';
import DeviceEditDialog from '../components/DeviceEditDialog';
import {
  // UI İkonları
  AlertTriangle,
  ArrowDown,
  ArrowUp,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Edit,
  ExternalLink,
  Eye,
  Filter,
  Layers,
  Plus,
  RefreshCw,
  Search,
  Trash2,
  X,
  XCircle,
  // Kategori İkonları
  Activity,
  BarChart3,
  Battery,
  Building2,
  Cloud,
  Cog,
  Cpu,
  Database,
  Globe,
  Globe2,
  HardDrive,
  Info as InfoIcon,
  Mail,
  Network,
  Package,
  Radio,
  Router,
  Server,
  ServerCrash,
  Share2,
  Shield,
  ShieldCheck,
  Terminal,
  Monitor,
  Container,
  Webhook,
  Wifi
} from 'lucide-react';

import StatusIndicator from '../components/StatusIndicator';
import DeviceCategoryIcon from '../components/DeviceCategoryIcon';
import MonitorTypeIcon from '../components/MonitorTypeIcon';
import { getDefaultReasonForStatus } from '../utils/statusUtils';
import { getFormattedSubCategory, getIconComponent, getColorClass } from '../utils/categoryUtils';
import PaginationComponent from '../components/PaginationComponent';

// API servisleri
import { deviceService, monitorService, categoryService } from '../services/api';

// Context
import { useDevices } from '../contexts/DeviceContext';
import { usePageLoading, useTableLoading } from '../hooks/useSmartLoading';
import { SmartPageLoading, SmartTableLoading } from '../components/ui/smart-loading';
import { Skeleton } from '../components/ui/skeleton';
import FloatingActionBar from '../components/ui/floating-action-bar';

// Yardımcı fonksiyonlar - categoryUtils'ten import edilen fonksiyonlar yukarıda birleştirildi



const DeviceList = () => {
  const navigate = useNavigate();
  const { devices, setDevices, statuses, loading, error: deviceError, loadData } = useDevices();


  // Smart loading hooks
  const pageLoading = usePageLoading(devices, loading);
  const tableLoading = useTableLoading(devices, false, false);

  const [actionLoading, setActionLoading] = useState({
    // check ve bulkCheck kaldırıldı, artık Socket.io ile gerçek zamanlı güncellemeler kullanılıyor
    bulkGroup: false,
    delete: false,
    bulkDelete: false
    // refreshing kaldırıldı, artık Socket.io ile gerçek zamanlı güncellemeler kullanılıyor
  });
  // Gruplar ve filtreler için yerel state kullan
  const [groups, setGroups] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGroup, setSelectedGroup] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [selectedDevices, setSelectedDevices] = useState([]);
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    deviceId: null,
    deviceName: '',
    isBulkDelete: false
  });
  const [bulkGroupDialog, setBulkGroupDialog] = useState({
    open: false,
    group: ''
  });
  const [bulkGroupError, setBulkGroupError] = useState('');

  // Yeni cihaz ekleme diyaloğu için state
  const [addDeviceDialogOpen, setAddDeviceDialogOpen] = useState(false);

  // Cihaz düzenleme diyaloğu için state
  const [editDialog, setEditDialog] = useState({
    open: false,
    device: null
  });

  // Sayfalandırma için state'ler
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Expandable rows için state
  const [expandedRows, setExpandedRows] = useState(new Set());

  // Kategori verileri için state
  const [categories, setCategories] = useState(null);
  const [loadingCategories, setLoadingCategories] = useState(true);

  // Kategorileri yükleme için (sayfa açıldığında)
  const loadGroupsData = useCallback(async () => {
    try {
      setLoadingCategories(true);

      // Kategorileri paralel olarak yükle
      const [groupsData, categoryData] = await Promise.all([
        deviceService.getAllGroups(),
        categoryService.getAll()
      ]);

      setGroups(groupsData);
      setCategories(categoryData.categories);

      console.log('DeviceList: Kategoriler yüklendi:', categoryData.metadata);
    } catch (err) {
      console.error('DeviceList: Kategoriler yüklenirken hata oluştu:', err);
    } finally {
      setLoadingCategories(false);
    }
  }, []);

  // Yenileme fonksiyonu kaldırıldı, artık Socket.io ile gerçek zamanlı güncellemeler kullanılıyor

  // Sayfa yüklendiğinde kategorileri yükle
  useEffect(() => {
    // Kategorileri yükle
    loadGroupsData();
  }, [loadGroupsData]);

  // Filtreleme değiştiğinde sayfa numarasını sıfırla
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedGroup, selectedStatus]);

  // Cihazı sil
  const handleDelete = async () => {
    try {
      setActionLoading(prev => ({ ...prev, delete: true }));
      await deviceService.delete(deleteDialog.deviceId);
      setDeleteDialog({ open: false, deviceId: null, deviceName: '', isBulkDelete: false });

      // Silinen cihazı yerel state'ten kaldır
      setDevices(prev => prev.filter(device => device.id !== deleteDialog.deviceId));

      // Başarılı silme bildirimi göster
      notificationService.success(`${deleteDialog.deviceName} cihazı silindi`, {
        description: 'Cihaz başarıyla silindi.',
        action: {
          label: 'Geri Al',
          onClick: () => {
            notificationService.info('Bu özellik henüz desteklenmiyor', {
              description: 'Silinen cihazı geri alma özelliği yakında eklenecek.'
            });
          }
        }
      });



      setActionLoading(prev => ({ ...prev, delete: false }));
    } catch (err) {
      console.error('Error deleting device:', err);

      // Hata bildirimi göster
      notificationService.error('Cihaz silinemedi', {
        description: err.response?.data?.message || 'Cihaz silinirken bir hata oluştu.'
      });

      setActionLoading(prev => ({ ...prev, delete: false }));
    }
  };

  // Cihaz kontrol fonksiyonları kaldırıldı, artık Socket.io ile gerçek zamanlı güncellemeler kullanılıyor

  // Toplu silme işlemi
  const handleBulkDelete = async () => {
    try {
      setActionLoading(prev => ({ ...prev, bulkDelete: true }));
      await deviceService.bulkDelete(selectedDevices);

      // Silinen cihazları yerel state'ten kaldır
      setDevices(prev => prev.filter(device => !selectedDevices.includes(device.id)));

      // Başarılı silme bildirimi göster
      notificationService.success(`${selectedDevices.length} cihaz silindi`, {
        description: 'Seçili cihazlar başarıyla silindi.'
      });

      setDeleteDialog({ open: false, deviceId: null, deviceName: '', isBulkDelete: false });
      setSelectedDevices([]);

      setActionLoading(prev => ({ ...prev, bulkDelete: false }));
    } catch (err) {
      console.error('Error deleting devices:', err);

      // Hata bildirimi göster
      notificationService.error('Cihazlar silinemedi', {
        description: err.response?.data?.message || 'Cihazlar silinirken bir hata oluştu.'
      });

      setActionLoading(prev => ({ ...prev, bulkDelete: false }));
    }
  };

  // Toplu kategori değiştirme işlemi
  const handleBulkGroupChange = async () => {
    try {
      // Kategori seçilmemişse hata göster
      if (!bulkGroupDialog.group || bulkGroupDialog.group.trim() === '') {
        setBulkGroupError('Kategori seçimi zorunludur');
        return;
      }

      // Seçili cihaz yoksa hata göster
      if (selectedDevices.length === 0) {
        notificationService.error('Cihaz seçilmedi', {
          description: 'Lütfen en az bir cihaz seçin.'
        });
        return;
      }

      // Hata varsa temizle
      setBulkGroupError('');

      setActionLoading(prev => ({ ...prev, bulkGroup: true }));

      // Seçilen kategoriyi direkt kullan (artık boş string yok)
      await deviceService.bulkUpdateGroup(selectedDevices, bulkGroupDialog.group);

      // Cihazların kategorilerini yerel olarak güncelle
      setDevices(prev => prev.map(device => {
        if (selectedDevices.includes(device.id)) {
          return { ...device, group: bulkGroupDialog.group };
        }
        return device;
      }));


      setBulkGroupDialog({ open: false, group: '' });
      setBulkGroupError('');
      setSelectedDevices([]);

      // Başarı bildirimi göster
      const categoryName = bulkGroupDialog.group;
      notificationService.success('Kategoriler güncellendi', {
        description: `${selectedDevices.length} cihazın kategorisi "${categoryName}" olarak güncellendi.`
      });

      setActionLoading(prev => ({ ...prev, bulkGroup: false }));
    } catch (err) {
      console.error('Error updating device categories:', err);

      // Hata bildirimi göster
      notificationService.error('Kategoriler güncellenemedi', {
        description: err.response?.data?.error || 'Cihaz kategorileri güncellenirken bir hata oluştu.'
      });

      setActionLoading(prev => ({ ...prev, bulkGroup: false }));
    }
  };

  // Tek bir cihazı seçme/seçimi kaldırma
  const handleSelectDevice = (deviceId, checked) => {
    if (checked === true) {
      setSelectedDevices(prev => [...prev, deviceId]);
    } else {
      setSelectedDevices(prev => prev.filter(id => id !== deviceId));
    }
  };

  // Tüm cihazları seçme/seçimi kaldırma
  const handleSelectAll = (checked) => {
    // Checkbox bileşeni bazen boolean yerine 'indeterminate' değeri de döndürebilir
    // Bu durumda seçimi kaldırıyoruz
    if (checked === true) {
      setSelectedDevices(filteredDevices.map(device => device.id));
    } else {
      setSelectedDevices([]);
    }
  };

  // Sıralama işlemi
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Durum badge'i
  const StatusBadge = ({ status, reason }) => {

    // Durum tooltip'i için kullanılacak açıklama
    const tooltipContent = reason || getDefaultReasonForStatus(status);

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span>
              <StatusIndicator status={status} type="badge" size="sm" />
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipContent}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };



  // Cihazın genel durumunu al (sadece backend'den)
  const getDeviceStatus = (deviceId) => {
    const deviceStatus = statuses[deviceId];
    if (!deviceStatus) return 'unknown';

    // Backend'den hesaplanmış durum varsa, onu kullan
    if (deviceStatus.calculatedStatus) {
      return deviceStatus.calculatedStatus;
    }

    // Backend'den henüz hesaplanmış durum gelmemişse 'unknown' döndür
    // Frontend'te hesaplama yapmıyoruz - sadece backend'e güveniyoruz
    return 'unknown';
  };

  // Expandable rows toggle fonksiyonu
  const toggleExpand = (deviceId) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(deviceId)) {
      newExpandedRows.delete(deviceId);
    } else {
      newExpandedRows.add(deviceId);
    }
    setExpandedRows(newExpandedRows);
  };

  // İzleyici sayma fonksiyonları (eski versiyon kaldırıldı)
  const getTotalMonitorCount = () => 5; // Toplam 5 izleyici türü

  // Kategori bazlı monitor sayma
  const getNetworkMonitorCount = (device) => {
    let count = 1; // ICMP her zaman aktif
    const networkTypes = ['http', 'tcp', 'dns'];
    networkTypes.forEach(type => {
      if (device.monitors?.[type]?.enabled) count++;
    });
    return count;
  };

  const getSecurityMonitorCount = (device) => {
    let count = 0;
    const securityTypes = ['ssl'];
    securityTypes.forEach(type => {
      if (device.monitors?.[type]?.enabled) count++;
    });
    return count;
  };

  const getAppMonitorCount = (device) => {
    let count = 0;
    const appTypes = ['database', 'api', 'smtp', 'ssl', 'docker'];
    appTypes.forEach(type => {
      if (device.monitors?.[type]?.enabled) count++;
    });
    return count;
  };

  // Static monitor groups - platform-agnostic approach
  const getMonitorGroupsForDevice = (device) => {
    // Fixed 2 group structure - same for all devices
    const groups = {
      network: {
        title: 'Ağ İzleyicileri',
        icon: 'network', // MonitorTypeIcon grup ikonu
        monitors: ['icmp', 'tcp', 'http', 'dns']
      },
      security: {
        title: 'Güvenlik İzleyicileri',
        icon: 'security', // MonitorTypeIcon grup ikonu
        monitors: ['ssl']
      }
    };

    return groups;
  };

  // Tüm mevcut monitor türleri
  const allMonitorTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl'];

  // İzleme türleri için başlıklar
  const monitorTitles = {
    icmp: 'ICMP Ping',
    tcp: 'TCP Port',
    http: 'HTTP/HTTPS',
    dns: 'DNS Sorgusu',
    ssl: 'SSL Sertifikası'
  };

  // Aktif monitor sayısını hesapla (geliştirilmiş)
  const getActiveMonitorCount = (device) => {
    if (!device?.monitors) return 0;

    let count = 1; // ICMP her zaman aktif
    allMonitorTypes.forEach(type => {
      if (type !== 'icmp' && device.monitors[type]?.enabled) {
        count++;
      }
    });
    return count;
  };



  // Grup için aktif izleyici sayısını hesapla
  const getActiveMonitorCountForGroup = (device, groupMonitors) => {
    if (!device?.monitors) return { active: 0, total: groupMonitors.length };

    const active = groupMonitors.filter(monitor => {
      if (monitor === 'icmp') return true; // ICMP her zaman aktif
      return device.monitors[monitor]?.enabled;
    }).length;
    return { active, total: groupMonitors.length };
  };

  // Filtreleme ve sıralama
  const filteredAndSortedDevices = devices.filter(device => {
    // Arama filtresi
    const searchMatch = device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        device.host.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        (device.description && device.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Kategori filtresi - Hiyerarşik yapıyı destekler
    let groupMatch = selectedGroup === 'all';
    if (!groupMatch) {
      if (selectedGroup.includes('/')) {
        // Alt kategori seçilmiş (örn. "Ağ Cihazları/Router")
        groupMatch = device.group === selectedGroup;
      } else {
        // Ana kategori seçilmiş (hem kendisini hem alt kategorilerini göster)
        groupMatch = device.group === selectedGroup || (device.group && device.group.startsWith(selectedGroup + '/'));
      }
    }

    // Durum filtresi
    let statusMatch = true;
    if (selectedStatus !== 'all') {
      const deviceStatus = getDeviceStatus(device.id);
      statusMatch = deviceStatus === selectedStatus;
    }

    return searchMatch && groupMatch && statusMatch;
  }).sort((a, b) => {
    // Sıralama
    let comparison = 0;

    switch(sortField) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'host':
        comparison = a.host.localeCompare(b.host);
        break;
      case 'group':
        comparison = (a.group || '').localeCompare(b.group || '');
        break;
      case 'location':
        comparison = (a.location || '').localeCompare(b.location || '');
        break;
      case 'status':
        const statusA = getDeviceStatus(a.id);
        const statusB = getDeviceStatus(b.id);
        // Durum sıralaması: Çevrimiçi > Uyarı > Kritik > Performans Düşük > Çevrimdışı > Kararsız > Bilinmiyor
        const statusOrder = {
          'up': 0,
          'warning': 1,
          'critical': 2,
          'degraded': 3,
          'down': 4,
          'flapping': 5,
          'unknown': 6
        };
        comparison = statusOrder[statusA] - statusOrder[statusB];
        break;
      case 'responseTime':
        const responseTimeA = statuses[a.id]?.icmp?.responseTime || 0;
        const responseTimeB = statuses[b.id]?.icmp?.responseTime || 0;
        comparison = responseTimeA - responseTimeB;
        break;

      default:
        comparison = 0;
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // Filtrelenmiş cihazlar
  const filteredDevices = filteredAndSortedDevices;

  // Sayfalandırma için hesaplamalar
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredDevices.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredDevices.length / itemsPerPage);

  // Sayfa değiştirme fonksiyonu
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Sayfa başına öğe sayısını değiştirme fonksiyonu
  const handlePageSizeChange = (newPageSize) => {
    // Yeni sayfa boyutuna göre mevcut sayfayı ayarla
    const newTotalPages = Math.ceil(filteredDevices.length / newPageSize);
    const newCurrentPage = Math.min(currentPage, newTotalPages);

    setItemsPerPage(newPageSize);
    setCurrentPage(newCurrentPage);
  };

  // Smart loading - sayfa seviyesi
  if (pageLoading.shouldShowFullPageLoading()) {
    return (
      <SmartPageLoading
        loadingType="initial"
        pageTitle="Cihazlar"
        skeletonLayout="table"
      />
    );
  }

  // DeviceContext veya yerel hata varsa göster


  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Cihazlar</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Ağınızdaki cihazları izleyin, yönetin ve durumlarını takip edin
          </p>
        </div>
        <div className="flex items-center gap-2 self-end sm:self-auto">
          <Button size="default" onClick={() => setAddDeviceDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Cihaz Ekle
          </Button>
        </div>
      </div>

      {/* Stats Cards - İzleme Odaklı */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-all">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Toplam İzleme Noktası</p>
                <p className="text-2xl font-bold tracking-tight text-blue-600">
                  {devices.reduce((total, device) => total + getActiveMonitorCount(device), 0)}
                </p>
                <p className="text-xs text-muted-foreground mt-1">Aktif tüm servisler</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-all">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">SSL Sertifika İzleme</p>
                <p className="text-2xl font-bold tracking-tight text-orange-600">
                  {devices.filter(device => device.monitors?.ssl?.enabled).length}
                </p>
                <p className="text-xs text-muted-foreground mt-1">Güvenlik izlemesi</p>
              </div>
              <Shield className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        {/* SNMP monitoring kaldırıldı - Lean monitoring yaklaşımı */}

        <Card className="hover:shadow-md transition-all">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">İzleme Derinliği</p>
                <p className="text-2xl font-bold tracking-tight text-green-600">
                  {devices.length > 0 ? (
                    devices.reduce((total, device) => total + getActiveMonitorCount(device), 0) / devices.length
                  ).toFixed(1) : '0.0'}
                </p>
                <p className="text-xs text-muted-foreground mt-1">Ortalama servis/cihaz</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtreler */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-6">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cihaz ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filter Dropdowns */}
            <div className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-[200px]">
                <Select value={selectedGroup} onValueChange={setSelectedGroup}>
                  <SelectTrigger>
                    <SelectValue placeholder="Kategori Seçin" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    <SelectItem value="all">Tüm Kategoriler</SelectItem>

                    {/* Ağ Cihazları */}
                    <SelectItem value="Ağ Cihazları">
                      <div className="flex items-center gap-2">
                        <Network className="h-4 w-4 text-blue-500" />
                        <span className="font-semibold">Ağ Cihazları</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/Router">
                      <div className="flex items-center gap-2 ml-4">
                        <Router className="h-4 w-4 text-blue-400" />
                        Router'lar
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/Switch">
                      <div className="flex items-center gap-2 ml-4">
                        <Share2 className="h-4 w-4 text-blue-400" />
                        Switch'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/Firewall">
                      <div className="flex items-center gap-2 ml-4">
                        <Shield className="h-4 w-4 text-blue-400" />
                        Firewall'lar
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/AccessPoint">
                      <div className="flex items-center gap-2 ml-4">
                        <Wifi className="h-4 w-4 text-blue-400" />
                        Access Point'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/Modem">
                      <div className="flex items-center gap-2 ml-4">
                        <Radio className="h-4 w-4 text-blue-400" />
                        Modem'ler
                      </div>
                    </SelectItem>

                    {/* Sunucular */}
                    <SelectItem value="Sunucular">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4 text-green-500" />
                        <span className="font-semibold">Sunucular & Hizmetler</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Fiziksel">
                      <div className="flex items-center gap-2 ml-4">
                        <ServerCrash className="h-4 w-4 text-green-400" />
                        Fiziksel Sunucular
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Sanal">
                      <div className="flex items-center gap-2 ml-4">
                        <Cloud className="h-4 w-4 text-green-400" />
                        Sanal Sunucular
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Container">
                      <div className="flex items-center gap-2 ml-4">
                        <Package className="h-4 w-4 text-green-400" />
                        Container'lar
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Veritabanı">
                      <div className="flex items-center gap-2 ml-4">
                        <Database className="h-4 w-4 text-green-400" />
                        Veri Tabanı Sunucuları
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Depolama">
                      <div className="flex items-center gap-2 ml-4">
                        <HardDrive className="h-4 w-4 text-green-400" />
                        Depolama Cihazları
                      </div>
                    </SelectItem>

                    {/* Web Servisleri */}
                    <SelectItem value="Web">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-purple-500" />
                        <span className="font-semibold">Web & Uygulama Servisleri</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/WebServer">
                      <div className="flex items-center gap-2 ml-4">
                        <Globe2 className="h-4 w-4 text-purple-400" />
                        Web Sunucuları
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/API">
                      <div className="flex items-center gap-2 ml-4">
                        <Webhook className="h-4 w-4 text-purple-400" />
                        API'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/Mail">
                      <div className="flex items-center gap-2 ml-4">
                        <Mail className="h-4 w-4 text-purple-400" />
                        Mail Sunucuları
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/CDN">
                      <div className="flex items-center gap-2 ml-4">
                        <Network className="h-4 w-4 text-purple-400" />
                        CDN'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/DNS">
                      <div className="flex items-center gap-2 ml-4">
                        <Search className="h-4 w-4 text-purple-400" />
                        DNS Sunucuları
                      </div>
                    </SelectItem>

                    {/* IoT Cihazları */}
                    <SelectItem value="IoT">
                      <div className="flex items-center gap-2">
                        <Cpu className="h-4 w-4 text-orange-500" />
                        <span className="font-semibold">IoT & Endüstriyel Cihazlar</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="IoT/Sensör">
                      <div className="flex items-center gap-2 ml-4">
                        <Activity className="h-4 w-4 text-orange-400" />
                        Akıllı Sensörler
                      </div>
                    </SelectItem>
                    <SelectItem value="IoT/PLC">
                      <div className="flex items-center gap-2 ml-4">
                        <Cog className="h-4 w-4 text-orange-400" />
                        Endüstriyel PLC'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="IoT/BinaSistemi">
                      <div className="flex items-center gap-2 ml-4">
                        <Building2 className="h-4 w-4 text-orange-400" />
                        Akıllı Bina Sistemleri
                      </div>
                    </SelectItem>
                    <SelectItem value="IoT/UPS">
                      <div className="flex items-center gap-2 ml-4">
                        <Battery className="h-4 w-4 text-orange-400" />
                        UPS & Güç Yönetim Cihazları
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1 min-w-[200px]">
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Durum Seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Durumlar</SelectItem>
                    <SelectItem value="up">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${STATUS_BG_COLORS[STATUS_TYPES.UP]}`}></span>
                        Çevrimiçi
                      </div>
                    </SelectItem>
                    <SelectItem value="warning">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${STATUS_BG_COLORS[STATUS_TYPES.WARNING]}`}></span>
                        Uyarı Veren
                      </div>
                    </SelectItem>
                    <SelectItem value="down">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${STATUS_BG_COLORS[STATUS_TYPES.DOWN]}`}></span>
                        Çevrimdışı
                      </div>
                    </SelectItem>
                    <SelectItem value="critical">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${STATUS_BG_COLORS[STATUS_TYPES.CRITICAL]}`}></span>
                        Kritik
                      </div>
                    </SelectItem>
                    <SelectItem value="partial">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${STATUS_BG_COLORS[STATUS_TYPES.PARTIAL]}`}></span>
                        Kısmi Veri
                      </div>
                    </SelectItem>
                    <SelectItem value="degraded">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${STATUS_BG_COLORS[STATUS_TYPES.DEGRADED]}`}></span>
                        Performans Düşük
                      </div>
                    </SelectItem>
                    <SelectItem value="flapping">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${STATUS_BG_COLORS[STATUS_TYPES.FLAPPING]}`}></span>
                        Kararsız
                      </div>
                    </SelectItem>
                    <SelectItem value="unknown">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${STATUS_BG_COLORS[STATUS_TYPES.UNKNOWN]}`}></span>
                        Bilinmiyor
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {(searchTerm || selectedGroup !== 'all' || selectedStatus !== 'all') && (
                <Button onClick={() => {
                  setSearchTerm('');
                  setSelectedGroup('all');
                  setSelectedStatus('all');
                }} variant="secondary" size="default" className="shrink-0">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Temizle
                </Button>
              )}
            </div>

            {/* Active Filters */}
            {(searchTerm || selectedGroup !== 'all' || selectedStatus !== 'all') && (
              <div className="flex flex-wrap gap-4">
                {selectedGroup !== 'all' && (
                  <Badge variant="outline" className="flex items-center gap-2 px-3 py-1.5 text-sm">
                    <DeviceCategoryIcon category={selectedGroup} size="sm" useInheritedColor={true} />
                    Kategori: {selectedGroup.includes('/') ? getFormattedSubCategory(selectedGroup) : selectedGroup}
                    <XCircle
                      className="h-4 w-4 cursor-pointer hover:text-red-500 transition-colors"
                      onClick={() => setSelectedGroup('all')}
                    />
                  </Badge>
                )}
                {selectedStatus !== 'all' && (
                  <Badge variant="outline" className="flex items-center gap-2 px-3 py-1.5 text-sm">
                    <StatusIndicator status={selectedStatus} type="dot" size="sm" withLabel={false} />
                    Durum: <StatusIndicator status={selectedStatus} type="text" />
                    <XCircle
                      className="h-4 w-4 cursor-pointer hover:text-red-500 transition-colors"
                      onClick={() => setSelectedStatus('all')}
                    />
                  </Badge>
                )}
                {searchTerm && (
                  <Badge variant="outline" className="flex items-center gap-2 px-3 py-1.5 text-sm">
                    <Search className="h-4 w-4 text-gray-500" />
                    Arama: "{searchTerm}"
                    <XCircle
                      className="h-4 w-4 cursor-pointer hover:text-red-500 transition-colors"
                      onClick={() => setSearchTerm('')}
                    />
                  </Badge>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>




      {/* Cihaz Tablosu */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="flex flex-col">
              <CardTitle>Cihaz Listesi</CardTitle>
              <p className="text-sm text-muted-foreground mt-2 leading-relaxed">
                <InfoIcon className="inline-block h-4 w-4 mr-1" /> Cihazları seçerek toplu işlemler yapabilirsiniz
              </p>
            </div>
            {(searchTerm || selectedGroup !== 'all' || selectedStatus !== 'all') && (
              <span className="text-sm font-medium text-muted-foreground">
                {filteredDevices.length} sonuç bulundu
              </span>
            )}
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto w-full">
              <Table className="w-full">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div>
                              <Checkbox
                                checked={filteredDevices.length > 0 && selectedDevices.length === filteredDevices.length}
                                onCheckedChange={handleSelectAll}
                                disabled={filteredDevices.length === 0}
                              />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{selectedDevices.length === filteredDevices.length ? "Tümünü Kaldır" : "Tümünü Seç"}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('name')}>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              Cihaz Adı
                              {sortField === 'name' && (
                                <span className="ml-2">
                                  {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                                </span>
                              )}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Cihaz adına göre sırala</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('host')}>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center">
                              IP/Host
                              {sortField === 'host' && (
                                <span className="ml-2">
                                  {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                                </span>
                              )}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>IP adresi veya host adına göre sırala</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableHead>
                    <TableHead className="hidden sm:table-cell cursor-pointer" onClick={() => handleSort('group')}>
                      <div className="flex items-center">
                        Kategori
                        {sortField === 'group' && (
                          <span className="ml-2">
                            {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="hidden md:table-cell cursor-pointer" onClick={() => handleSort('location')}>
                      <div className="flex items-center">
                        Bölge
                        {sortField === 'location' && (
                          <span className="ml-2">
                            {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('status')}>
                      <div className="flex items-center">
                        Durum
                        {sortField === 'status' && (
                          <span className="ml-2">
                            {sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                          </span>
                        )}
                      </div>
                    </TableHead>

                    <TableHead className="hidden md:table-cell">
                      <div className="flex items-center">
                        Detay
                      </div>
                    </TableHead>

                    <TableHead className="hidden lg:table-cell">
                      <div className="flex items-center">
                        İzleyiciler
                      </div>
                    </TableHead>

                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tableLoading.shouldShowSkeleton() ? (
                    // Smart Skeleton Loading Rows
                    Array.from({ length: itemsPerPage }).map((_, index) => (
                      <TableRow key={`skeleton-${index}`}>
                        <TableCell><Skeleton width="w-4" height="h-4" /></TableCell>
                        <TableCell><Skeleton width="w-24" height="h-4" /></TableCell>
                        <TableCell><Skeleton width="w-20" height="h-4" /></TableCell>
                        <TableCell className="hidden sm:table-cell"><Skeleton width="w-16" height="h-6" rounded="rounded-full" /></TableCell>
                        <TableCell className="hidden md:table-cell"><Skeleton width="w-12" height="h-4" /></TableCell>
                        <TableCell><Skeleton width="w-14" height="h-6" rounded="rounded-full" /></TableCell>
                        <TableCell className="hidden md:table-cell"><Skeleton width="w-20" height="h-4" /></TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <div className="flex items-center justify-between">
                            <Skeleton width="w-12" height="h-4" />
                            <Skeleton width="w-8" height="h-8" rounded="rounded-md" />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : filteredDevices.length > 0 ? (
                    currentItems.flatMap((device) => [
                      <TableRow
                        key={device.id}
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={(e) => {
                          // Checkbox'a veya butonlara tıklandığında satır tıklamasını engelle
                          if (e.target.type === 'checkbox' || e.target.closest('button') || e.target.closest('[role="checkbox"]')) {
                            return;
                          }
                          toggleExpand(device.id);
                        }}
                      >
                        <TableCell onClick={(e) => e.stopPropagation()}>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div>
                                  <Checkbox
                                    checked={selectedDevices.includes(device.id)}
                                    onCheckedChange={(checked) => handleSelectDevice(device.id, checked)}
                                  />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{selectedDevices.includes(device.id) ? "Seçimi Kaldır" : "Seç"}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-left">
                            {device.name}
                          </span>
                        </TableCell>
                        <TableCell>
                          {device.host}
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <Badge variant="outline" className="flex items-center gap-2 px-3 py-1.5 text-sm font-normal">
                            <DeviceCategoryIcon category={device.group} size="sm" useInheritedColor={true} />
                            {device.group.includes('/') ? getFormattedSubCategory(device.group) : device.group}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {device.location ? device.location : <span className="text-muted-foreground">-</span>}
                        </TableCell>
                        <TableCell>
                          <StatusBadge
                            status={getDeviceStatus(device.id)}
                            reason={statuses[device.id]?.reason}
                          />
                        </TableCell>

                        <TableCell className="hidden md:table-cell text-sm text-muted-foreground">
                          {statuses[device.id]?.reason || getDefaultReasonForStatus(getDeviceStatus(device.id))}
                        </TableCell>

                        <TableCell className="hidden lg:table-cell">
                          <div className="flex items-center justify-between">
                            <Badge variant="outline" className="text-sm h-6 px-2">
                              {getActiveMonitorCount(device)}/{getTotalMonitorCount()}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleExpand(device.id);
                              }}
                              className="h-8 w-8 p-0"
                            >
                              {expandedRows.has(device.id) ?
                                <ChevronUp className="h-4 w-4" /> :
                                <ChevronDown className="h-4 w-4" />
                              }
                            </Button>
                          </div>
                        </TableCell>

                      </TableRow>,
                      ...(expandedRows.has(device.id) ? [
                        <TableRow key={`${device.id}-expanded`} className="animate-in slide-in-from-top-2 duration-300">
                          <TableCell colSpan={8} className="p-0 bg-slate-50/50 dark:bg-slate-900/50">
                            <div className="border-l-4 border-l-slate-300 dark:border-l-slate-600">
                              <div className="px-6 py-8 w-full">
                                {(() => {
                                  const monitorGroups = getMonitorGroupsForDevice(device);
                                  const groupCount = Object.keys(monitorGroups).length;

                                  // Grup sayısına göre grid sütun sayısını belirle
                                  const getGridCols = (count) => {
                                    if (count === 1) return 'grid-cols-1';
                                    if (count === 2) return 'grid-cols-1 lg:grid-cols-2';
                                    if (count === 3) return 'grid-cols-1 lg:grid-cols-2 xl:grid-cols-3';
                                    if (count === 4) return 'grid-cols-1 lg:grid-cols-2 xl:grid-cols-4';
                                    if (count === 5) return 'grid-cols-1 lg:grid-cols-2 xl:grid-cols-5';
                                    return 'grid-cols-1 lg:grid-cols-2 xl:grid-cols-3'; // 6+ için 3 sütun max
                                  };

                                  return (
                                    <div className={`grid ${getGridCols(groupCount)} gap-6 w-full`}>
                                      {Object.entries(monitorGroups).map(([groupKey, group]) => {
                                    const { active, total } = getActiveMonitorCountForGroup(device, group.monitors);

                                    return (
                                      <Card key={groupKey} className="flex flex-col">
                                        <CardHeader className="py-4 border-b border-border">
                                          <CardTitle className="text-base flex items-center gap-2">
                                            {group.icon && (
                                              typeof group.icon === 'string' ? (
                                                <MonitorTypeIcon
                                                  type={group.icon}
                                                  size="md"
                                                  useInheritedColor={true}
                                                  showTooltip={false}
                                                  className="text-muted-foreground"
                                                />
                                              ) : (
                                                <group.icon className="h-4 w-4 text-muted-foreground" />
                                              )
                                            )}
                                            <span>{group.title}</span>
                                            <Badge variant="outline" className="text-xs ml-auto">
                                              {active}/{total}
                                            </Badge>
                                          </CardTitle>
                                        </CardHeader>
                                        <CardContent className="pt-4">
                                          <div className="space-y-3 pl-2">
                                            {group.monitors.map((monitorType) => {
                                              const isActive = monitorType === 'icmp' ? true : device?.monitors?.[monitorType]?.enabled || false;

                                              return (
                                                <div key={monitorType} className="flex items-center justify-between">
                                                  <div className="flex items-center gap-2">
                                                    <MonitorTypeIcon
                                                      type={monitorType}
                                                      size="md"
                                                      active={isActive}
                                                    />
                                                    <span className="text-sm">{monitorTitles[monitorType]}</span>
                                                  </div>
                                                  <div className="flex items-center gap-2">
                                                    <div className={`w-2.5 h-2.5 rounded-full ${isActive ? 'bg-emerald-500' : 'bg-slate-400'}`}></div>
                                                    <span className={`text-sm font-medium ${isActive ? 'text-emerald-700 dark:text-emerald-300' : 'text-slate-500'}`}>
                                                      {isActive ? 'Aktif' : 'Pasif'}
                                                    </span>
                                                  </div>
                                                </div>
                                              );
                                            })}
                                          </div>
                                        </CardContent>
                                      </Card>
                                    );
                                  })}

                                      {/* Detay Butonu */}
                                      <div className="col-span-full flex items-center justify-center py-3">
                                        <Button
                                          onClick={() => navigate(`/devices/${device.id}`)}
                                          variant="outline"
                                          className="flex items-center gap-2 text-sm h-9 px-4"
                                        >
                                          <ExternalLink className="h-4 w-4" />
                                          Cihaz detaylarını görüntüle
                                        </Button>
                                      </div>
                                    </div>
                                  );
                                })()}
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ] : [])
                    ])
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        {devices.length === 0 ? (
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <Server className="h-10 w-10 mb-3 opacity-70" />
                            <p className="text-base font-medium">Henüz cihaz eklenmemiş</p>
                            <p className="text-sm mt-2">Cihaz eklemek için "Cihaz Ekle" butonunu kullanın</p>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <Filter className="h-10 w-10 mb-3 opacity-70" />
                            <p className="text-base font-medium">Filtrelere uygun cihaz bulunamadı</p>
                            <p className="text-sm mt-2">Lütfen arama kriterlerinizi değiştirin</p>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Sayfalandırma */}
          <div className="mt-4">
            <PaginationComponent
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={filteredDevices.length}
              onPageChange={paginate}
              pageSize={itemsPerPage}
              onPageSizeChange={handlePageSizeChange}
              pageSizeOptions={[5, 10, 25, 50, 100]}
              showPageSizeOptions={true}
            />
          </div>
        </CardContent>
      </Card>

      {/* Silme Onay Dialogu */}
      <AlertDialog open={deleteDialog.open} onOpenChange={(open) => !open && setDeleteDialog({ ...deleteDialog, open: false })}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{deleteDialog.isBulkDelete ? 'Sil' : 'Cihazı Sil'}</AlertDialogTitle>
            <AlertDialogDescription>
              {deleteDialog.isBulkDelete ? (
                <>
                  <strong>{deleteDialog.deviceName}</strong> silmek istediğinize emin misiniz? Bu işlem geri alınamaz.
                </>
              ) : (
                <>
                  <strong>{deleteDialog.deviceName}</strong> cihazını silmek istediğinize emin misiniz? Bu işlem geri alınamaz.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialog({ ...deleteDialog, open: false })}>
              İptal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={deleteDialog.isBulkDelete ? handleBulkDelete : handleDelete}
              disabled={actionLoading.delete || actionLoading.bulkDelete}
              className="bg-destructive text-destructive-foreground"
            >
              {deleteDialog.isBulkDelete ? (
                actionLoading.bulkDelete ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Siliniyor...
                  </>
                ) : (
                  'Sil'
                )
              ) : (
                actionLoading.delete ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Siliniyor...
                  </>
                ) : (
                  'Sil'
                )
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Kategori Değiştirme Dialogu */}
      <Dialog open={bulkGroupDialog.open} onOpenChange={(open) => !open && setBulkGroupDialog({ ...bulkGroupDialog, open: false })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Kategori Değiştir</DialogTitle>
            <DialogDescription>
              Seçili {selectedDevices.length} cihaz için kategori değiştir
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="bulkGroup">
                  Kategori <span className="text-destructive">*</span>
                </Label>
                <Select
                  value={bulkGroupDialog.group || ''}
                  onValueChange={(value) => {
                    setBulkGroupDialog({ ...bulkGroupDialog, group: value });
                    // Hata varsa temizle
                    if (bulkGroupError) {
                      setBulkGroupError('');
                    }
                  }}
                >
                  <SelectTrigger id="bulkGroup" className={bulkGroupError ? "border-destructive" : ""}>
                    <SelectValue placeholder="Kategori seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingCategories ? (
                      <SelectItem value="" disabled>
                        <div className="flex items-center gap-2">
                          <RefreshCw className="h-4 w-4 animate-spin" />
                          Kategoriler yükleniyor...
                        </div>
                      </SelectItem>
                    ) : categories ? (
                      Object.entries(categories).map(([mainCategory, categoryData]) => {
                        const MainIcon = getIconComponent(categoryData.icon);
                        return (
                          <React.Fragment key={mainCategory}>
                            {/* Ana kategori başlığı */}
                            <SelectItem value={mainCategory} disabled className="font-semibold">
                              <div className="flex items-center gap-2">
                                <MainIcon className={`h-4 w-4 ${getColorClass(categoryData.color, true)}`} />
                                <span className="font-semibold">{mainCategory}</span>
                              </div>
                            </SelectItem>

                            {/* Alt kategoriler */}
                            {Object.entries(categoryData.subcategories).map(([subCategory, subData]) => {
                              const SubIcon = getIconComponent(subData.icon);
                              const categoryValue = `${mainCategory}/${subCategory}`;
                              return (
                                <SelectItem key={categoryValue} value={categoryValue}>
                                  <div className="flex items-center gap-2 ml-4">
                                    <SubIcon className={`h-4 w-4 ${getColorClass(categoryData.color, false)}`} />
                                    {subData.label}
                                  </div>
                                </SelectItem>
                              );
                            })}
                          </React.Fragment>
                        );
                      })
                    ) : (
                      <SelectItem value="" disabled>
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-red-500" />
                          Kategoriler yüklenemedi
                        </div>
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {bulkGroupError && (
                  <p className="text-sm text-destructive">{bulkGroupError}</p>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setBulkGroupDialog({ ...bulkGroupDialog, open: false });
              setBulkGroupError('');
            }}>
              İptal
            </Button>
            <Button
              onClick={handleBulkGroupChange}
              disabled={actionLoading.bulkGroup}
            >
              {actionLoading.bulkGroup ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Uygulanıyor...
                </>
              ) : (
                'Uygula'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Yeni cihaz ekleme diyaloğu */}
      <DeviceAddDialog
        open={addDeviceDialogOpen}
        onOpenChange={setAddDeviceDialogOpen}
        onSuccess={() => {
          // Cihaz başarıyla eklendiğinde cihaz listesini yenile
          loadData();

        }}
      />

      {/* Cihaz Düzenleme Diyaloğu */}
      {editDialog.device && (
        <DeviceEditDialog
          open={editDialog.open}
          onOpenChange={(open) => setEditDialog({ ...editDialog, open })}
          onSuccess={() => {
            // Cihaz başarıyla güncellendiğinde cihaz listesini yenile
            loadData();

          }}
          device={editDialog.device}
        />
      )}

      {/* Floating Action Bar */}
      <FloatingActionBar
        selectedCount={selectedDevices.length}
        selectedLabel="cihaz"
        onClearSelection={() => setSelectedDevices([])}
      >
        {/* Düzenle butonu - sadece tek seçim */}
        {selectedDevices.length === 1 && (
          <Button
            variant="outline"
            size="default"
            onClick={() => {
              const deviceId = selectedDevices[0];
              const selectedDevice = devices.find(d => d.id === deviceId);
              if (selectedDevice) {
                setEditDialog({
                  open: true,
                  device: selectedDevice
                });
              }
            }}
          >
            <Edit className="mr-2 h-4 w-4" /> Düzenle
          </Button>
        )}

        {/* Kategori değiştir */}
        <Button
          variant="outline"
          size="default"
          onClick={() => {
            setBulkGroupDialog({ open: true, group: '' });
            setBulkGroupError('');
          }}
          disabled={actionLoading.bulkGroup}
        >
          <Server className="mr-2 h-4 w-4" /> Kategori Değiştir
        </Button>

        {/* Sil butonu */}
        <Button
          variant="destructive"
          size="default"
          onClick={() => setDeleteDialog({
            open: true,
            deviceId: null,
            deviceName: `${selectedDevices.length} cihaz`,
            isBulkDelete: true
          })}
          disabled={actionLoading.bulkDelete}
        >
          <Trash2 className="mr-2 h-4 w-4" /> Sil
        </Button>
      </FloatingActionBar>
    </div>
  );
};

export default DeviceList;
