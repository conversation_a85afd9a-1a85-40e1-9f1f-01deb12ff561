import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Progress } from '../components/ui/progress';
import { Switch } from '../components/ui/switch';
import { Tabs as TabsComponent, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import MonitorTypeIcon from '../components/MonitorTypeIcon';
// StatusBadge local component olarak tanımlanacak
import MonitorTimesDisplay from '../components/MonitorTimesDisplay';
import {
  Activity,
  History,
  Settings,
  AlertCircle,
  Info,
  ArrowLeft
} from 'lucide-react';

const DeviceDetailTest = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('status');

  // Test cihazı - tüm izleyiciler aktif
  const testDevice = {
    id: 'test-device',
    name: 'Test Sunucusu',
    ip: '*************',
    platform: 'linux',
    group: 'Sunucular/Fiziksel',
    monitors: {
      icmp: { enabled: true },
      http: { enabled: true, url: 'https://test.example.com' },
      tcp: { enabled: true, ports: [80, 443, 22] },
      snmp: { enabled: true, community: 'public' },
      dns: { enabled: true, hostname: 'test.example.com' },
      ssl: { enabled: true, hostname: 'test.example.com' },
      database: { enabled: true, type: 'mysql', host: '*************', port: 3306 },
      api: { enabled: true, url: 'https://api.test.example.com/health' },
      smtp: { enabled: true, host: 'mail.test.example.com', port: 587 },
      system: { enabled: true, platform: 'linux', username: 'monitor' },
      docker: { enabled: true },
      ipmi: { enabled: true, host: '*************', username: 'admin' }
    }
  };

  // Test status verileri - tüm izleyiciler UP durumunda
  const testStatus = {
    icmp: {
      status: 'up',
      responseTime: 15,
      packetLoss: 0,
      lastCheck: Date.now().toString()
    },
    http: {
      status: 'up',
      responseTime: 156,
      lastCheck: Date.now().toString(),
      details: { statusCode: 200, statusText: 'OK' }
    },
    tcp: {
      '80': { status: 'up', responseTime: 23, lastCheck: Date.now().toString() },
      '443': { status: 'up', responseTime: 28, lastCheck: Date.now().toString() },
      '22': { status: 'up', responseTime: 12, lastCheck: Date.now().toString() }
    },
    snmp: {
      status: 'up',
      responseTime: 34,
      lastCheck: Date.now().toString(),
      details: {
        cpu: 25,
        memory: 67,
        sysName: 'test-server.local',
        sysUpTime: 8640000,
        interfaces: [
          { name: 'eth0', status: 'up' },
          { name: 'eth1', status: 'up' },
          { name: 'lo', status: 'up' }
        ]
      }
    },
    dns: {
      status: 'up',
      responseTime: 67,
      lastCheck: Date.now().toString(),
      details: {
        answers: [{ data: '*************' }, { data: '*************' }]
      }
    },
    ssl: {
      status: 'up',
      responseTime: 123,
      lastCheck: Date.now().toString(),
      details: {
        daysRemaining: 45,
        validFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        validTo: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
        expiryStatus: 'valid'
      }
    },
    database: {
      status: 'up',
      responseTime: 12,
      lastCheck: Date.now().toString()
    },
    api: {
      status: 'up',
      responseTime: 89,
      lastCheck: Date.now().toString(),
      details: { statusCode: 200, statusText: 'OK' }
    },
    smtp: {
      status: 'up',
      responseTime: 234,
      lastCheck: Date.now().toString()
    },
    system: {
      status: 'up',
      responseTime: 45,
      lastCheck: Date.now().toString(),
      details: {
        cpu: 23,
        memory: 67,
        disk: 45,
        hostname: 'test-server',
        platform: 'Linux Ubuntu 20.04',
        uptime: 7200
      }
    },
    docker: {
      status: 'up',
      responseTime: 78,
      lastCheck: Date.now().toString(),
      details: {
        runningContainers: 5,
        totalContainers: 8,
        version: '20.10.17'
      }
    },
    ipmi: {
      status: 'up',
      responseTime: 156,
      lastCheck: Date.now().toString(),
      details: {
        temperature: 45,
        fanStatus: 'OK',
        powerStatus: 'ON'
      }
    }
  };

  // Monitor başlıkları
  const monitorTitles = {
    icmp: 'ICMP Ping',
    http: 'HTTP/HTTPS',
    tcp: 'TCP Port',
    snmp: 'SNMP',
    dns: 'DNS Sorgusu',
    ssl: 'SSL Sertifikası',
    database: 'Veritabanı',
    api: 'REST API',
    smtp: 'SMTP E-posta',
    system: 'Sistem Monitoring',
    docker: 'Docker Konteyner',
    ipmi: 'IPMI Donanım'
  };

  // Yeni sabit grup yapısı
  const getMonitorGroups = () => {
    const groups = {
      network: {
        title: 'Ağ İzleyicileri',
        icon: 'network',
        monitors: ['icmp', 'tcp', 'http']
      },
      system: {
        title: 'Sistem İzleyicileri', 
        icon: 'system',
        monitors: ['system', 'snmp', 'ipmi']
      },
      services: {
        title: 'Servis İzleyicileri',
        icon: 'services',
        monitors: ['database', 'api', 'smtp', 'docker']
      },
      security: {
        title: 'Güvenlik İzleyicileri',
        icon: 'security',
        monitors: ['ssl', 'dns']
      }
    };

    return groups;
  };

  const monitorGroups = getMonitorGroups();

  // StatusBadge component (DeviceDetail.jsx'ten kopyalandı)
  const StatusBadge = ({ status, reason }) => {
    // Durum tooltip'i için kullanılacak açıklama
    const getDefaultReasonForStatus = (status) => {
      switch (status) {
        case 'up': return 'Servis çalışıyor';
        case 'down': return 'Servis çalışmıyor';
        case 'warning': return 'Uyarı durumu';
        case 'critical': return 'Kritik durum';
        case 'unknown': return 'Durum bilinmiyor';
        default: return 'Durum bilgisi yok';
      }
    };

    const tooltipContent = reason || getDefaultReasonForStatus(status);

    // StatusIndicator'ı Badge olarak kullan
    const getStatusColor = (status) => {
      switch (status) {
        case 'up': return 'bg-green-500';
        case 'down': return 'bg-red-500';
        case 'warning': return 'bg-yellow-500';
        case 'critical': return 'bg-red-600';
        case 'unknown': return 'bg-gray-500';
        default: return 'bg-gray-400';
      }
    };

    const getStatusText = (status) => {
      switch (status) {
        case 'up': return 'Çevrimiçi';
        case 'down': return 'Çevrimdışı';
        case 'warning': return 'Uyarı';
        case 'critical': return 'Kritik';
        case 'unknown': return 'Bilinmiyor';
        default: return 'Veri Yok';
      }
    };

    return (
      <Badge variant="outline" className={`${getStatusColor(status)} text-white border-0`}>
        {getStatusText(status)}
      </Badge>
    );
  };

  const toggleMonitor = (monitorType, checked) => {
    console.log(`Test sayfası: ${monitorType} ${checked ? 'etkinleştirildi' : 'devre dışı bırakıldı'}`);
  };

  const handleSettingsClick = (monitorType) => {
    console.log(`Test sayfası: ${monitorType} ayarları açılacak`);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate('/devices')}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Test Sayfası - Tüm İzleyiciler</h1>
          <p className="text-muted-foreground">
            Tüm izleyici türlerinin nasıl göründüğünü test etmek için oluşturulmuş sayfa
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sol Sütun - Cihaz Bilgileri ve İzleyici Grupları */}
        <div className="space-y-6">
          {/* Cihaz Bilgileri */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-muted-foreground" />
                Test Cihazı Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="font-medium">Cihaz Adı:</span>
                <span>{testDevice.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">IP Adresi:</span>
                <span className="font-mono">{testDevice.ip}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Platform:</span>
                <span>{testDevice.platform}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Kategori:</span>
                <span>{testDevice.group}</span>
              </div>
            </CardContent>
          </Card>

          {/* İzleyici Grupları */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-muted-foreground" />
                İzleyici Grupları
              </CardTitle>
              <CardDescription>
                Tüm izleyiciler test için etkinleştirilmiş durumda
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(monitorGroups).map(([groupKey, group]) => {
                  const active = group.monitors.length;
                  const total = group.monitors.length;

                  return (
                    <Card key={groupKey} className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base flex items-center gap-2">
                          <MonitorTypeIcon 
                            type={group.icon} 
                            size="md" 
                            useInheritedColor={true} 
                            showTooltip={false} 
                            className="text-muted-foreground" 
                          />
                          <span>{group.title}</span>
                          <Badge variant="outline" className="text-xs ml-auto">
                            {active}/{total}
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-4">
                        <div className="space-y-3 pl-2">
                          {group.monitors.map((monitorType) => (
                            <div key={monitorType} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <MonitorTypeIcon
                                  type={monitorType}
                                  size="md"
                                  active={true}
                                />
                                <div>
                                  <span className="text-sm">{monitorTitles[monitorType]}</span>
                                </div>
                              </div>
                              <Switch
                                checked={true}
                                disabled={monitorType === 'icmp'}
                                onCheckedChange={(checked) => toggleMonitor(monitorType, checked)}
                              />
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sağ Sütun - Detaylı Bilgiler */}
        <div className="lg:col-span-2">
          {/* Sekmeler */}
          <TabsComponent defaultValue={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="status" className="flex items-center gap-2">
                <Activity className="h-4 w-4" /> Durum
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center gap-2">
                <History className="h-4 w-4" /> Geçmiş
              </TabsTrigger>
            </TabsList>

            {/* Durum Sekmesi */}
            <TabsContent value="status">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-muted-foreground" />
                    Detaylı Durum Bilgileri (Test Verileri)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                    {/* ICMP Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="icmp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            ICMP Ping
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.icmp.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('icmp')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          Erişilebilirlik durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.icmp,
                              deviceId: 'test-device',
                              type: 'icmp'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Yanıt Süresi</span>
                            <span className="font-mono text-lg font-bold">{testStatus.icmp.responseTime} ms</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.icmp.responseTime / 200) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.icmp.responseTime > 150 ? "bg-destructive" :
                                              testStatus.icmp.responseTime > 80 ? "bg-warning" : "bg-success"}
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>İyi (&lt;80ms)</span>
                            <span>Orta (&lt;150ms)</span>
                            <span>Yavaş (&gt;150ms)</span>
                          </div>
                          <div className="mt-4 pt-4 border-t">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Paket Kaybı</span>
                              <span className="font-mono">{testStatus.icmp.packetLoss}%</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* HTTP Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="http" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            HTTP/HTTPS
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.http.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('http')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          Web servisi durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.http,
                              deviceId: 'test-device',
                              type: 'http'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Yanıt Süresi</span>
                            <span className="font-mono text-lg font-bold">{testStatus.http.responseTime} ms</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.http.responseTime / 500) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.http.responseTime > 300 ? "bg-destructive" :
                                              testStatus.http.responseTime > 150 ? "bg-warning" : "bg-success"}
                          />
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Durum Kodu</span>
                              <span className="font-mono text-sm font-bold">
                                {testStatus.http.details.statusCode} - {testStatus.http.details.statusText}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">URL</span>
                              <span className="font-mono text-sm">{testDevice.monitors.http.url}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* TCP Port Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="tcp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            TCP Port
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status="up" />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('tcp')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          Port durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              status: 'up',
                              responseTime: 23,
                              lastCheck: Date.now().toString(),
                              deviceId: 'test-device',
                              type: 'tcp'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {Object.entries(testStatus.tcp).map(([port, portStatus]) => (
                            <div key={port} className="flex justify-between items-center p-2 bg-muted/50 rounded">
                              <span className="text-sm font-medium">Port {port}</span>
                              <div className="flex items-center gap-2">
                                <span className="font-mono text-sm">{portStatus.responseTime} ms</span>
                                <StatusBadge status={portStatus.status} />
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* DNS Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="dns" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            DNS Sorgusu
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.dns.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('dns')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          DNS sorgu durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.dns,
                              deviceId: 'test-device',
                              type: 'dns'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Yanıt Süresi</span>
                            <span className="font-mono text-lg font-bold">{testStatus.dns.responseTime} ms</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.dns.responseTime / 200) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.dns.responseTime > 150 ? "bg-destructive" :
                                              testStatus.dns.responseTime > 80 ? "bg-warning" : "bg-success"}
                          />
                          <div className="mt-4 pt-4 border-t">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Çözümlenen IP</span>
                              <span className="font-mono text-sm">
                                {testStatus.dns.details.answers.map(a => a.data).join(', ')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* SSL Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="ssl" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            SSL Sertifikası
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.ssl.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('ssl')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          Sertifika durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.ssl,
                              deviceId: 'test-device',
                              type: 'ssl'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Geçerlilik</span>
                            <span className="font-mono text-lg font-bold">{testStatus.ssl.details.daysRemaining} gün kaldı</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.ssl.details.daysRemaining / 90) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.ssl.details.daysRemaining < 7 ? "bg-destructive" :
                                              testStatus.ssl.details.daysRemaining < 30 ? "bg-warning" : "bg-success"}
                          />
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Geçerlilik Başlangıcı</span>
                              <span className="font-mono text-sm">{new Date(testStatus.ssl.details.validFrom).toLocaleDateString()}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Geçerlilik Bitişi</span>
                              <span className="font-mono text-sm">{new Date(testStatus.ssl.details.validTo).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Database Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="database" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            Veritabanı
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.database.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('database')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          MySQL veritabanı
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.database,
                              deviceId: 'test-device',
                              type: 'database'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Bağlantı Süresi</span>
                            <span className="font-mono text-lg font-bold">{testStatus.database.responseTime} ms</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.database.responseTime / 500) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.database.responseTime > 300 ? "bg-destructive" :
                                              testStatus.database.responseTime > 150 ? "bg-warning" : "bg-success"}
                          />
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Sunucu</span>
                              <span className="font-mono text-sm">{testDevice.monitors.database.host}:{testDevice.monitors.database.port}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Tür</span>
                              <span className="font-mono text-sm">{testDevice.monitors.database.type.toUpperCase()}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* API Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="api" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            REST API
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.api.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('api')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          REST API endpoint durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.api,
                              deviceId: 'test-device',
                              type: 'api'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Yanıt Süresi</span>
                            <span className="font-mono text-lg font-bold">{testStatus.api.responseTime} ms</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.api.responseTime / 1000) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.api.responseTime > 500 ? "bg-destructive" :
                                              testStatus.api.responseTime > 200 ? "bg-warning" : "bg-success"}
                          />
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Durum Kodu</span>
                              <span className="font-mono text-sm font-bold">
                                {testStatus.api.details.statusCode} - {testStatus.api.details.statusText}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Endpoint</span>
                              <span className="font-mono text-sm">{testDevice.monitors.api.url}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* SMTP Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="smtp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            SMTP E-posta
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.smtp.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('smtp')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          SMTP e-posta sunucu durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.smtp,
                              deviceId: 'test-device',
                              type: 'smtp'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Bağlantı Süresi</span>
                            <span className="font-mono text-lg font-bold">{testStatus.smtp.responseTime} ms</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.smtp.responseTime / 1000) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.smtp.responseTime > 500 ? "bg-destructive" :
                                              testStatus.smtp.responseTime > 200 ? "bg-warning" : "bg-success"}
                          />
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">SMTP Sunucu</span>
                              <span className="font-mono text-sm">{testDevice.monitors.smtp.host}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Port</span>
                              <span className="font-mono text-sm">{testDevice.monitors.smtp.port}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* System Monitoring Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="system" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            Sistem Monitoring
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.system.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('system')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          Sistem performans metrikleri
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.system,
                              deviceId: 'test-device',
                              type: 'system'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">CPU Kullanımı</span>
                              <span className="font-mono text-lg font-bold">{testStatus.system.details.cpu}%</span>
                            </div>
                            <Progress
                              value={testStatus.system.details.cpu}
                              className="h-2"
                              indicatorClassName={testStatus.system.details.cpu > 80 ? "bg-destructive" :
                                                testStatus.system.details.cpu > 60 ? "bg-warning" : "bg-success"}
                            />
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">RAM Kullanımı</span>
                              <span className="font-mono text-lg font-bold">{testStatus.system.details.memory}%</span>
                            </div>
                            <Progress
                              value={testStatus.system.details.memory}
                              className="h-2"
                              indicatorClassName={testStatus.system.details.memory > 85 ? "bg-destructive" :
                                                testStatus.system.details.memory > 70 ? "bg-warning" : "bg-success"}
                            />
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Disk Kullanımı</span>
                              <span className="font-mono text-lg font-bold">{testStatus.system.details.disk}%</span>
                            </div>
                            <Progress
                              value={testStatus.system.details.disk}
                              className="h-2"
                              indicatorClassName={testStatus.system.details.disk > 90 ? "bg-destructive" :
                                                testStatus.system.details.disk > 75 ? "bg-warning" : "bg-success"}
                            />
                          </div>
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Hostname</span>
                              <span className="font-mono text-sm">{testStatus.system.details.hostname}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Platform</span>
                              <span className="font-mono text-sm">{testStatus.system.details.platform}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Uptime</span>
                              <span className="font-mono text-sm">{Math.floor(testStatus.system.details.uptime / 3600)} saat</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* SNMP Monitoring Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="snmp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            SNMP Monitoring
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.snmp.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('snmp')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          SNMP ağ cihazı metrikleri
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.snmp,
                              deviceId: 'test-device',
                              type: 'snmp'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">CPU Kullanımı</span>
                              <span className="font-mono text-lg font-bold">{testStatus.snmp.details.cpu}%</span>
                            </div>
                            <Progress
                              value={testStatus.snmp.details.cpu}
                              className="h-2"
                              indicatorClassName={testStatus.snmp.details.cpu > 80 ? "bg-destructive" :
                                                testStatus.snmp.details.cpu > 60 ? "bg-warning" : "bg-success"}
                            />
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Memory Kullanımı</span>
                              <span className="font-mono text-lg font-bold">{testStatus.snmp.details.memory}%</span>
                            </div>
                            <Progress
                              value={testStatus.snmp.details.memory}
                              className="h-2"
                              indicatorClassName={testStatus.snmp.details.memory > 85 ? "bg-destructive" :
                                                testStatus.snmp.details.memory > 70 ? "bg-warning" : "bg-success"}
                            />
                          </div>
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="text-sm font-medium mb-2">Ağ Arayüzleri</div>
                            {testStatus.snmp.details.interfaces.slice(0, 3).map((iface, index) => (
                              <div key={index} className="flex justify-between items-center">
                                <span className="text-sm">{iface.name}</span>
                                <span className="font-mono text-sm">
                                  {iface.status === 'up' ? '✅ Aktif' : '❌ Pasif'}
                                </span>
                              </div>
                            ))}
                          </div>
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Sistem Adı</span>
                              <span className="font-mono text-sm">{testStatus.snmp.details.sysName}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Uptime</span>
                              <span className="font-mono text-sm">{Math.floor(testStatus.snmp.details.sysUpTime / 8640000)} gün</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Docker Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="docker" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            Docker Konteyner
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.docker.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('docker')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          Docker konteyner durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.docker,
                              deviceId: 'test-device',
                              type: 'docker'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Bağlantı Süresi</span>
                            <span className="font-mono text-lg font-bold">{testStatus.docker.responseTime} ms</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.docker.responseTime / 1000) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.docker.responseTime > 500 ? "bg-destructive" :
                                              testStatus.docker.responseTime > 200 ? "bg-warning" : "bg-success"}
                          />
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Çalışan Container</span>
                              <span className="font-mono text-sm">{testStatus.docker.details.runningContainers}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Toplam Container</span>
                              <span className="font-mono text-sm">{testStatus.docker.details.totalContainers}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Docker Version</span>
                              <span className="font-mono text-sm">{testStatus.docker.details.version}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* IPMI Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="ipmi" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            IPMI Donanım
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <StatusBadge status={testStatus.ipmi.status} />
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('ipmi')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          IPMI donanım durumu
                          <MonitorTimesDisplay
                            monitorData={{
                              ...testStatus.ipmi,
                              deviceId: 'test-device',
                              type: 'ipmi'
                            }}
                          />
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Bağlantı Süresi</span>
                            <span className="font-mono text-lg font-bold">{testStatus.ipmi.responseTime} ms</span>
                          </div>
                          <Progress
                            value={Math.min(100, (testStatus.ipmi.responseTime / 3000) * 100)}
                            className="h-2"
                            indicatorClassName={testStatus.ipmi.responseTime > 2000 ? "bg-destructive" :
                                              testStatus.ipmi.responseTime > 1000 ? "bg-warning" : "bg-success"}
                          />
                          <div className="mt-4 pt-4 border-t space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Sıcaklık</span>
                              <span className="font-mono text-sm">{testStatus.ipmi.details.temperature}°C</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Fan Durumu</span>
                              <span className="font-mono text-sm">{testStatus.ipmi.details.fanStatus}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Güç Durumu</span>
                              <span className="font-mono text-sm">{testStatus.ipmi.details.powerStatus}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Geçmiş Sekmesi */}
            <TabsContent value="history">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <History className="h-5 w-5 text-muted-foreground" />
                    Test Geçmişi
                  </CardTitle>
                  <CardDescription>
                    Bu test sayfasında geçmiş veriler gösterilmez
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <History className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">Test Sayfası</h3>
                    <p className="text-muted-foreground max-w-md">
                      Bu sayfa sadece tasarım test amaçlıdır. Gerçek geçmiş verileri için normal cihaz detay sayfasını kullanın.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </TabsComponent>
        </div>
      </div>
    </div>
  );
};

export default DeviceDetailTest;
