import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription } from '../components/ui/alert';
import { notificationService } from '../services/notification-service';
import authService from '../services/auth';
import { ArrowLeft, Mail, CheckCircle } from 'lucide-react';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email.trim()) {
      notificationService.error('Eksik bilgi', {
        description: 'E-posta adresi gereklidir'
      });
      return;
    }

    // Basit e-posta formatı kontrolü
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      notificationService.error('Geçersiz e-posta', {
        description: 'Geçerli bir e-posta adresi giriniz'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      await authService.forgotPassword(email);
      
      setIsSuccess(true);
      
      // Başarı bildirimi göster
      notificationService.success('E-posta gönderildi', {
        description: 'Şifre sıfırlama bağlantısı e-posta adresinize gönderildi.',
        persistent: true,
        category: 'security'
      });

    } catch (err) {
      console.error('Forgot password error:', err);

      let errorMessage = 'Bir hata oluştu. Lütfen tekrar deneyin.';

      if (err.response?.status === 429) {
        errorMessage = 'Çok sık şifre sıfırlama talebi yapıyorsunuz. Lütfen 5 dakika bekleyin.';
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      }

      // Hata bildirimi göster
      notificationService.error('İşlem başarısız', {
        description: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1 text-center">
            <div className="flex justify-center mb-4">
              <CheckCircle className="h-16 w-16 text-green-500" />
            </div>
            <CardTitle className="text-2xl font-bold">E-posta Gönderildi</CardTitle>
            <CardDescription>
              Şifre sıfırlama bağlantısı e-posta adresinize gönderildi
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            <Alert>
              <Mail className="h-4 w-4" />
              <AlertDescription>
                <strong>{email}</strong> adresine şifre sıfırlama bağlantısı gönderildi. 
                E-postanızı kontrol edin ve bağlantıya tıklayarak şifrenizi sıfırlayın.
              </AlertDescription>
            </Alert>

            <div className="text-sm text-muted-foreground space-y-2">
              <p>• E-posta gelmemişse spam klasörünüzü kontrol edin</p>
              <p>• Bağlantı 1 saat süreyle geçerlidir</p>
              <p>• Bağlantı sadece bir kez kullanılabilir</p>
            </div>
          </CardContent>

          <CardFooter className="flex flex-col space-y-4">
            <Link to="/login" className="w-full">
              <Button variant="outline" className="w-full gap-2">
                <ArrowLeft className="h-4 w-4" />
                Giriş Sayfasına Dön
              </Button>
            </Link>
            
            <div className="text-sm text-center text-muted-foreground">
              E-posta gelmediyse{' '}
              <button 
                onClick={() => {
                  setIsSuccess(false);
                  setEmail('');
                }}
                className="text-primary hover:underline"
              >
                tekrar deneyin
              </button>
            </div>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Şifremi Unuttum</CardTitle>
          <CardDescription>
            E-posta adresinizi girin, size şifre sıfırlama bağlantısı gönderelim
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">E-posta Adresi</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                disabled={isSubmitting}
                autoComplete="email"
                autoFocus
              />
            </div>

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? 'Gönderiliyor...' : 'Şifre Sıfırlama Bağlantısı Gönder'}
            </Button>
          </form>
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <Link to="/login" className="w-full">
            <Button variant="outline" className="w-full gap-2">
              <ArrowLeft className="h-4 w-4" />
              Giriş Sayfasına Dön
            </Button>
          </Link>
          
          <div className="text-sm text-center text-muted-foreground">
            Hesabınız yok mu?{' '}
            <Link to="/register" className="text-primary hover:underline">
              Kayıt Ol
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ForgotPassword;
