{"name": "network-monitor-client", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.3", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.75.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.501.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.19.0", "react-scripts": "^5.0.1", "socket.io-client": "^4.7.2", "sonner": "^2.0.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "BROWSER=none NODE_OPTIONS=--no-warnings react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "devDependencies": {"autoprefixer": "^10.4.14", "class-variance-authority": "^0.6.1", "clsx": "^1.2.1", "postcss": "^8.4.23", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7"}}