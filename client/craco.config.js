module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      // DevServer yapılandırmasını güncelle
      if (webpackConfig.devServer) {
        // <PERSON><PERSON><PERSON><PERSON><PERSON> kaldırılmış seçenekleri kaldır
        delete webpackConfig.devServer.onBeforeSetupMiddleware;
        delete webpackConfig.devServer.onAfterSetupMiddleware;
        
        // Yeni setupMiddlewares seçeneğini kullan
        webpackConfig.devServer.setupMiddlewares = (middlewares, devServer) => {
          if (!devServer) {
            throw new Error('webpack-dev-server is not defined');
          }
          
          // Burada özel middleware'ler ekleyebilirsiniz
          
          return middlewares;
        };
      }
      
      return webpackConfig;
    },
  },
};
