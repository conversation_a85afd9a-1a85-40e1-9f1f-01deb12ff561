================================================================================
                    NETWATCH - NETWORK MONITOR TEKNOLOJİ STACK'İ
================================================================================

📋 PROJE GENEL BİLGİLERİ:
- Proje Adı: NetWatch
- Açıklama: Agent gerektirmeyen ağ izleme yazılımı
- Lisans: MIT
- Mimari: Full-Stack Web Application
- Versiyon: 1.0.0

================================================================================
🔧 BACKEND TEKNOLOJİLERİ
================================================================================

⚡ CORE FRAMEWORK & RUNTIME:
- Node.js - JavaScript runtime
- Express.js v4.18.2 - Web framework
- Socket.IO v4.7.2 - Real-time communication

🗄️ DATABASE & CACHE:
- Redis (ioredis v4.28.5) - In-memory cache ve session store
- PostgreSQL (pg v8.14.1) - İlişkisel veritabanı desteği
- MySQL (mysql2 v3.14.0) - İlişkisel veritabanı desteği
- MongoDB v6.15.0 - NoSQL veritabanı desteği

🔐 AUTHENTICATION & SECURITY:
- bcryptjs v2.4.3 - Password hashing
- jsonwebtoken v9.0.2 - JWT token management
- express-validator v7.0.1 - Input validation
- cookie-parser v1.4.6 - Cookie handling

📡 NETWORK MONITORING CAPABILITIES:
- net-ping v1.2.4 - ICMP ping monitoring
- snmp-native v1.2.0 - SNMP monitoring
- dns-packet v5.6.1 - DNS monitoring
- ssl-checker v2.0.10 - SSL certificate monitoring
- raw-socket v1.8.1 - Low-level network access

📧 COMMUNICATION & SCHEDULING:
- nodemailer v6.10.1 - Email notifications
- node-cron v3.0.3 - Scheduled tasks
- axios v1.8.4 - HTTP client

🛠️ DEVELOPMENT TOOLS:
- nodemon v3.1.9 - Development server
- concurrently v8.2.2 - Multiple process runner

================================================================================
🎨 FRONTEND TEKNOLOJİLERİ
================================================================================

⚛️ CORE FRAMEWORK:
- React v18.2.0 - UI framework
- React Router DOM v6.19.0 - Client-side routing
- React Scripts v5.0.1 - Build tooling

🎯 STATE MANAGEMENT & DATA FETCHING:
- TanStack React Query v5.75.0 - Server state management
- Socket.IO Client v4.7.2 - Real-time updates
- axios v1.6.2 - HTTP client

🎨 UI COMPONENTS & STYLING:
- Tailwind CSS v3.3.0 - Utility-first CSS framework
- Radix UI Components:
  * @radix-ui/react-alert-dialog v1.0.5
  * @radix-ui/react-checkbox v1.0.4
  * @radix-ui/react-dialog v1.1.11
  * @radix-ui/react-dropdown-menu v2.0.6
  * @radix-ui/react-icons v1.3.0
  * @radix-ui/react-label v2.0.2
  * @radix-ui/react-radio-group v1.3.6
  * @radix-ui/react-scroll-area v1.2.6
  * @radix-ui/react-select v2.2.2
  * @radix-ui/react-separator v1.0.3
  * @radix-ui/react-tabs v1.0.4
  * @radix-ui/react-tooltip v1.2.3

📊 DATA VISUALIZATION:
- Chart.js v4.4.0 - Chart library
- React Chart.js 2 v5.2.0 - React wrapper
- Recharts v2.15.3 - React chart library
- chartjs-adapter-date-fns v3.0.0 - Date handling

📄 DOCUMENT GENERATION:
- React PDF Renderer v4.3.0 - PDF generation
- jsPDF v3.0.1 - PDF creation
- jsPDF AutoTable v5.0.2 - Table generation
- html2canvas v1.4.1 - Screenshot capture
- file-saver v2.0.5 - File download

🔧 UTILITIES:
- date-fns v4.1.0 - Date manipulation
- moment v2.30.1 - Date/time library
- lucide-react v0.501.0 - Icon library
- sonner v2.0.3 - Toast notifications

🎨 STYLING & ANIMATION:
- tailwindcss-animate v1.0.7 - Animation utilities
- class-variance-authority v0.6.1 - Component variants
- clsx v1.2.1 & tailwind-merge v1.14.0 - Conditional styling

🧪 TESTING:
- Jest - Testing framework
- React Testing Library - Component testing

================================================================================
🏗️ PROJE YAPISI
================================================================================

📁 BACKEND STRUCTURE:
server/
├── app.js              # Ana uygulama dosyası
├── config/             # Yapılandırma dosyaları
│   └── redis.js        # Redis yapılandırması
├── constants/          # Sabitler
│   ├── notifications.js
│   └── redis-keys.js
├── cron/              # Zamanlanmış görevler
│   └── cleanupCron.js
├── middleware/        # Express middleware
│   ├── auth.js
│   └── sessionTimeout.js
├── models/            # Veri modelleri
│   └── user.js
├── routes/            # API rotaları
│   ├── alerts.js
│   ├── auth.js
│   ├── devices.js
│   ├── monitors.js
│   ├── notifications.js
│   ├── settings.js
│   ├── system.js
│   └── users.js
├── services/          # İş mantığı servisleri
│   ├── cleanupService.js
│   ├── deviceService.js
│   ├── deviceStatusService.js
│   ├── emailService.js
│   ├── monitors/
│   ├── notification-filter-service.js
│   ├── notificationService.js
│   ├── redis.js
│   ├── scheduler.js
│   ├── settingsService.js
│   └── socketService.js
├── templates/         # Email şablonları
│   ├── device-alert.html
│   ├── password-reset.html
│   ├── system-notification.html
│   └── test-email.html
└── utils/             # Yardımcı fonksiyonlar
    ├── error-handler.js
    ├── helpers.js
    ├── jwt.js
    ├── loginAttempts.js
    ├── monitorUtils.js
    ├── passwordExpiry.js
    ├── passwordPolicy.js
    └── passwordReset.js

📁 FRONTEND STRUCTURE:
client/
├── src/               # React kaynak kodları
├── public/            # Statik dosyalar
├── package.json       # Frontend dependencies
└── tailwind.config.js # Tailwind yapılandırması

================================================================================
🌐 NETWORK MONITORING FEATURES
================================================================================

📡 MONITORING TYPES:
- ICMP - Ping monitoring
- HTTP/HTTPS - Web service monitoring
- TCP - Port connectivity
- DNS - DNS resolution
- SSL - Certificate monitoring
- SNMP - Network device monitoring

🔄 REAL-TIME FEATURES:
- WebSocket - Live updates
- Redis Pub/Sub - Event broadcasting
- Scheduled Monitoring - Cron-based checks

================================================================================
🚀 DEPLOYMENT & PRODUCTION
================================================================================

🐧 SYSTEM SERVICES:
- systemd - Service management
- Oracle Linux 8.10 - Production OS
- Firewall - Network security

🔧 PRODUCTION OPTIMIZATIONS:
- Memory Management - 4GB heap limit
- Process Management - systemd service management
- Auto-restart - Service recovery
- Logging - Systemd journal integration

================================================================================
📊 SYSTEM REQUIREMENTS
================================================================================

MINIMUM REQUIREMENTS:
- OS: Oracle Linux 8+ / RHEL 8+ / CentOS 8+
- RAM: 4GB
- Disk: 10GB
- Network: Internet bağlantısı
- Node.js: v16+
- Redis: v6+

RECOMMENDED:
- RAM: 8GB+
- Disk: 50GB+ (SSD)
- CPU: 4 cores+

================================================================================
🔗 NETWORK PORTS
================================================================================

- Frontend: 3000 (HTTP)
- Backend API: 5000 (HTTP)
- Redis: 6379 (Internal)
- SSH: 22 (Management)

================================================================================
📝 NOTES
================================================================================

Bu teknoloji stack'i modern, ölçeklenebilir ve güvenli bir ağ izleme çözümü 
sunmaktadır. Proje agent gerektirmeden ağ cihazlarını izleyebilir ve 
real-time bildirimler sağlayabilir.

Dosya oluşturulma tarihi: 2025-06-06
Sistem: Oracle Linux 8.10
Kernel: 5.15.0-308.179.6.3.el8uek.x86_64
