/**
 * Kimlik doğrulama middleware'i
 */

const { verifyToken } = require('../utils/jwt');

/**
 * <PERSON><PERSON>ıcının kimliğini doğrular
 * @param {Object} req - Express request nesnesi
 * @param {Object} res - Express response nesnesi
 * @param {Function} next - Express next fonksiyonu
 */
async function authenticate(req, res, next) {
  // Authorization header'ını al
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: 'Kimlik doğrulama gerekli' });
  }

  // Bearer token'ı ayıkla
  const parts = authHeader.split(' ');

  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return res.status(401).json({ error: 'Geçersiz kimlik doğrulama formatı' });
  }

  const token = parts[1];

  // Token'ı doğrula
  const decoded = verifyToken(token);

  if (!decoded) {
    return res.status(401).json({ error: 'Geçersiz veya süresi dolmuş token' });
  }

  try {
    // Kullanıcı durumunu kontrol et
    const userModel = require('../models/user');
    const userStatus = await userModel.getUserStatus(decoded.id);

    // Kullanıcı devre dışı bırakılmış mı kontrol et
    if (userStatus.status === 'disabled') {
      return res.status(401).json({
        error: 'Hesabınız yönetici tarafından devre dışı bırakıldı. Lütfen sistem yöneticisi ile iletişime geçin.',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // Kullanıcı kilitli mi kontrol et
    if (userStatus.locked && userStatus.lockedUntil) {
      const remainingTime = Math.ceil((userStatus.lockedUntil - Date.now()) / (60 * 1000));
      if (remainingTime > 0) {
        return res.status(401).json({
          error: `Hesabınız kilitlendi. ${remainingTime} dakika sonra tekrar deneyin.`,
          code: 'ACCOUNT_LOCKED'
        });
      }
    }
  } catch (error) {
    console.error('Kullanıcı durumu kontrol hatası:', error);
    return res.status(500).json({ error: 'Sunucu hatası' });
  }

  // Kullanıcı bilgilerini request nesnesine ekle
  req.user = decoded;

  next();
}

/**
 * Kullanıcının admin rolüne sahip olup olmadığını kontrol eder
 * @param {Object} req - Express request nesnesi
 * @param {Object} res - Express response nesnesi
 * @param {Function} next - Express next fonksiyonu
 */
function isAdmin(req, res, next) {
  if (!req.user) {
    return res.status(401).json({ error: 'Kimlik doğrulama gerekli' });
  }
  
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Bu işlem için yetkiniz yok' });
  }
  
  next();
}

module.exports = {
  authenticate,
  isAdmin
};
