/**
 * Oturum zaman aşımı middleware'i
 * Bu middleware, kullanıcı oturumlarının zaman aşımını kontrol eder ve
 * ayarlarda belirtilen süre sonunda otomatik çıkış yapar.
 */

const { verifyToken } = require('../utils/jwt');
const settingsService = require('../services/settingsService');
const redisClient = require('../services/redis');

/**
 * <PERSON>llanıcı son aktivite zamanını günceller
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise<void>}
 */
async function updateLastActivity(userId) {
  const now = Date.now();
  await redisClient.set(`user:${userId}:lastActivity`, now);
}

/**
 * Kullanıcının son aktivite zamanını kontrol eder
 * @param {string} userId - Kullanıcı ID'si
 * @param {number} sessionTimeout - Oturum zaman aşımı süresi (dakika)
 * @returns {Promise<boolean>} - Oturum süresi dolmuşsa true
 */
async function checkSessionTimeout(userId, sessionTimeout) {
  const lastActivity = await redisClient.get(`user:${userId}:lastActivity`);

  if (!lastActivity) {
    // Son aktivite kaydı yoksa, şu anki zamanı kaydet ve false döndür
    await updateLastActivity(userId);
    return false;
  }

  const now = Date.now();
  const lastActivityTime = parseInt(lastActivity);
  const timeoutMs = sessionTimeout * 60 * 1000; // Dakika -> milisaniye

  // Oturum süresi dolmuş mu kontrol et
  const isExpired = (now - lastActivityTime) > timeoutMs;

  // Debug log kaldırıldı - çok sık tetikleniyor

  return isExpired;
}

/**
 * Oturum zaman aşımı middleware'i
 * @param {Object} req - Express request nesnesi
 * @param {Object} res - Express response nesnesi
 * @param {Function} next - Express next fonksiyonu
 */
async function sessionTimeoutMiddleware(req, res, next) {
  // Kimlik doğrulama gerektirmeyen rotaları atla
  const publicPaths = ['/api/auth/login', '/api/auth/register', '/api/auth/refresh-token'];
  if (publicPaths.includes(req.path)) {
    return next();
  }

  // Authorization header'ını kontrol et
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return next();
  }

  // Bearer token'ı ayıkla
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return next();
  }

  const token = parts[1];

  // Token'ı doğrula
  const decoded = verifyToken(token);
  if (!decoded) {
    return next();
  }

  // Kullanıcı ID'sini al
  const userId = decoded.id;

  try {
    // Ayarları getir
    const settings = await settingsService.getSettings();

    // Otomatik çıkış devre dışı bırakılmışsa atla
    if (!settings.autoLogout) {
      // Son aktivite zamanını güncelle
      await updateLastActivity(userId);
      return next();
    }

    // Oturum zaman aşımı süresini al
    const sessionTimeout = parseInt(settings.sessionTimeout) || 60; // Varsayılan: 60 dakika

    // Oturum süresini kontrol et
    const isSessionExpired = await checkSessionTimeout(userId, sessionTimeout);

    if (isSessionExpired) {
      // Oturum süresi dolmuşsa 401 hatası döndür
      return res.status(401).json({
        error: 'Oturum süresi doldu',
        code: 'SESSION_TIMEOUT'
      });
    }

    // Son aktivite zamanını güncelle
    await updateLastActivity(userId);

    next();
  } catch (error) {
    console.error('Oturum zaman aşımı kontrolü hatası:', error);
    next();
  }
}

module.exports = {
  sessionTimeoutMiddleware,
  updateLastActivity,
  checkSessionTimeout
};
