/**
 * Şifre politikası yardımcı fonksiyonları
 * Bu modül, farklı karmaşıklık seviyelerine göre şifre doğrulama işlemlerini gerçekleştirir.
 */

const settingsService = require('../services/settingsService');

/**
 * Şifre politikası seviyeleri
 */
const POLICY_LEVELS = {
  LOW: 'low',       // En az 6 karakter
  MEDIUM: 'medium', // En az 8 karakter, 1 büyük harf, 1 rakam
  HIGH: 'high'      // En az 10 karakter, 1 büyük harf, 1 rakam, 1 özel karakter
};

/**
 * Düşük seviye şifre politikası doğrulama
 * @param {string} password - Doğrulanacak şifre
 * @returns {Object} - Doğrulama sonucu { valid: boolean, message: string }
 */
function validateLowPolicy(password) {
  if (!password || password.length < 6) {
    return {
      valid: false,
      message: '<PERSON><PERSON><PERSON> en az 6 karakter uzunluğunda olmalıdır.'
    };
  }
  
  return { valid: true };
}

/**
 * Orta seviye şifre politikası doğrulama
 * @param {string} password - Doğrulanacak şifre
 * @returns {Object} - Doğrulama sonucu { valid: boolean, message: string }
 */
function validateMediumPolicy(password) {
  if (!password || password.length < 8) {
    return {
      valid: false,
      message: 'Şifre en az 8 karakter uzunluğunda olmalıdır.'
    };
  }
  
  if (!/[A-Z]/.test(password)) {
    return {
      valid: false,
      message: 'Şifre en az bir büyük harf içermelidir.'
    };
  }
  
  if (!/[0-9]/.test(password)) {
    return {
      valid: false,
      message: 'Şifre en az bir rakam içermelidir.'
    };
  }
  
  return { valid: true };
}

/**
 * Yüksek seviye şifre politikası doğrulama
 * @param {string} password - Doğrulanacak şifre
 * @returns {Object} - Doğrulama sonucu { valid: boolean, message: string }
 */
function validateHighPolicy(password) {
  if (!password || password.length < 10) {
    return {
      valid: false,
      message: 'Şifre en az 10 karakter uzunluğunda olmalıdır.'
    };
  }
  
  if (!/[A-Z]/.test(password)) {
    return {
      valid: false,
      message: 'Şifre en az bir büyük harf içermelidir.'
    };
  }
  
  if (!/[0-9]/.test(password)) {
    return {
      valid: false,
      message: 'Şifre en az bir rakam içermelidir.'
    };
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return {
      valid: false,
      message: 'Şifre en az bir özel karakter içermelidir.'
    };
  }
  
  return { valid: true };
}

/**
 * Şifre politikasına göre şifreyi doğrula
 * @param {string} password - Doğrulanacak şifre
 * @param {string} policyLevel - Politika seviyesi (low, medium, high)
 * @returns {Object} - Doğrulama sonucu { valid: boolean, message: string }
 */
function validatePassword(password, policyLevel) {
  switch (policyLevel) {
    case POLICY_LEVELS.LOW:
      return validateLowPolicy(password);
    case POLICY_LEVELS.MEDIUM:
      return validateMediumPolicy(password);
    case POLICY_LEVELS.HIGH:
      return validateHighPolicy(password);
    default:
      return validateMediumPolicy(password); // Varsayılan olarak orta seviye
  }
}

/**
 * Ayarlardaki şifre politikasına göre şifreyi doğrula
 * @param {string} password - Doğrulanacak şifre
 * @returns {Promise<Object>} - Doğrulama sonucu { valid: boolean, message: string }
 */
async function validatePasswordWithSettings(password) {
  try {
    // Ayarlardan şifre politikasını al
    const settings = await settingsService.getSettings();
    const policyLevel = settings.passwordPolicy || POLICY_LEVELS.MEDIUM;
    
    return validatePassword(password, policyLevel);
  } catch (error) {
    console.error('Şifre politikası doğrulama hatası:', error);
    // Hata durumunda varsayılan olarak orta seviye politika uygula
    return validatePassword(password, POLICY_LEVELS.MEDIUM);
  }
}

module.exports = {
  POLICY_LEVELS,
  validatePassword,
  validatePasswordWithSettings
};
