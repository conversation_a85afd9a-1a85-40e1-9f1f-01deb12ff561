/**
 * <PERSON><PERSON><PERSON> sıfırlama token yönetimi
 */

const crypto = require('crypto');
const redis = require('../config/redis');

// Token süre sınırı (1 saat)
const TOKEN_EXPIRY = 60 * 60; // 1 saat (saniye)
const TOKEN_PREFIX = 'password_reset:';

/**
 * Şifre sıfırlama token'ı oluşturur
 * @param {string} userId - Kullanıcı ID'si
 * @param {string} email - Kullanıcı e-posta adresi
 * @returns {Promise<string>} - Oluşturulan token
 */
async function generatePasswordResetToken(userId, email) {
  try {
    // Güvenli rastgele token oluştur
    const token = crypto.randomBytes(32).toString('hex');
    
    // Token verilerini hazırla
    const tokenData = {
      userId,
      email,
      createdAt: Date.now(),
      used: false
    };

    // Redis'te token'ı sakla (1 saat süreyle)
    const redisKey = TOKEN_PREFIX + token;
    await redis.setex(redisKey, TOKEN_EXPIRY, JSON.stringify(tokenData));

    console.log(`✅ Şifre sıfırlama token'ı oluşturuldu: ${userId} (${email})`);
    return token;
  } catch (error) {
    console.error('❌ Token oluşturma hatası:', error);
    throw new Error('Token oluşturulamadı');
  }
}

/**
 * Şifre sıfırlama token'ını doğrular
 * @param {string} token - Doğrulanacak token
 * @returns {Promise<Object|null>} - Token verisi veya null
 */
async function verifyPasswordResetToken(token) {
  try {
    if (!token) {
      return null;
    }

    const redisKey = TOKEN_PREFIX + token;
    const tokenDataStr = await redis.get(redisKey);

    if (!tokenDataStr) {
      console.log(`❌ Token bulunamadı veya süresi dolmuş: ${token}`);
      return null;
    }

    const tokenData = JSON.parse(tokenDataStr);

    // Token kullanılmış mı kontrol et
    if (tokenData.used) {
      console.log(`❌ Token zaten kullanılmış: ${token}`);
      return null;
    }

    // Token süre kontrolü (ekstra güvenlik)
    const now = Date.now();
    const tokenAge = now - tokenData.createdAt;
    const maxAge = TOKEN_EXPIRY * 1000; // milisaniyeye çevir

    if (tokenAge > maxAge) {
      console.log(`❌ Token süresi dolmuş: ${token}`);
      await redis.del(redisKey); // Süresi dolmuş token'ı temizle
      return null;
    }

    console.log(`✅ Token doğrulandı: ${tokenData.userId} (${tokenData.email})`);
    return tokenData;
  } catch (error) {
    console.error('❌ Token doğrulama hatası:', error);
    return null;
  }
}

/**
 * Şifre sıfırlama token'ını kullanılmış olarak işaretler
 * @param {string} token - İşaretlenecek token
 * @returns {Promise<boolean>} - İşlem başarısı
 */
async function markTokenAsUsed(token) {
  try {
    const redisKey = TOKEN_PREFIX + token;
    const tokenDataStr = await redis.get(redisKey);

    if (!tokenDataStr) {
      return false;
    }

    const tokenData = JSON.parse(tokenDataStr);
    tokenData.used = true;
    tokenData.usedAt = Date.now();

    // Token'ı kullanılmış olarak güncelle (kalan süreyle)
    const ttl = await redis.ttl(redisKey);
    if (ttl > 0) {
      await redis.setex(redisKey, ttl, JSON.stringify(tokenData));
    }

    console.log(`✅ Token kullanılmış olarak işaretlendi: ${token}`);
    return true;
  } catch (error) {
    console.error('❌ Token işaretleme hatası:', error);
    return false;
  }
}

/**
 * Kullanıcının mevcut şifre sıfırlama token'larını temizler
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise<void>}
 */
async function clearUserPasswordResetTokens(userId) {
  try {
    // Kullanıcının tüm token'larını bul
    const pattern = TOKEN_PREFIX + '*';
    const keys = await redis.keys(pattern);

    for (const key of keys) {
      const tokenDataStr = await redis.get(key);
      if (tokenDataStr) {
        const tokenData = JSON.parse(tokenDataStr);
        if (tokenData.userId === userId) {
          await redis.del(key);
          console.log(`🗑️ Kullanıcı token'ı temizlendi: ${userId}`);
        }
      }
    }
  } catch (error) {
    console.error('❌ Token temizleme hatası:', error);
  }
}

/**
 * Rate limiting için kullanıcının son şifre sıfırlama talebini kontrol eder
 * @param {string} email - E-posta adresi
 * @returns {Promise<boolean>} - Talep yapılabilir mi
 */
async function canRequestPasswordReset(email) {
  try {
    const rateLimitKey = `password_reset_rate:${email}`;
    const lastRequest = await redis.get(rateLimitKey);

    if (lastRequest) {
      const timeSinceLastRequest = Date.now() - parseInt(lastRequest);
      const minInterval = 5 * 60 * 1000; // 5 dakika

      if (timeSinceLastRequest < minInterval) {
        console.log(`❌ Rate limit: ${email} için çok erken talep`);
        return false;
      }
    }

    // Son talep zamanını kaydet (1 saat süreyle)
    await redis.setex(rateLimitKey, 60 * 60, Date.now().toString());
    return true;
  } catch (error) {
    console.error('❌ Rate limit kontrolü hatası:', error);
    return true; // Hata durumunda izin ver
  }
}

module.exports = {
  generatePasswordResetToken,
  verifyPasswordResetToken,
  markTokenAsUsed,
  clearUserPasswordResetTokens,
  canRequestPasswordReset,
  TOKEN_EXPIRY
};
