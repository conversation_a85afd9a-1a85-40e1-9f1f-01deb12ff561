/**
 * <PERSON>rk<PERSON>i hata yönetimi modülü
 */

/**
 * <PERSON>a sınıfları
 */
class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404, 'NOT_FOUND');
  }
}

class ValidationError extends AppError {
  constructor(message = 'Validation failed', errors = {}) {
    super(message, 400, 'VALIDATION_ERROR');
    this.errors = errors;
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Not authorized') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

/**
 * Hata işleme fonksiyonları
 */

/**
 * Hata mesajını loglar
 * @param {Error} error - Hata nesnesi
 */
const logError = (error) => {
  console.error(`[${new Date().toISOString()}] ${error.name}: ${error.message}`);
  
  if (error.stack) {
    console.error(error.stack);
  }
  
  if (error.errors) {
    console.error('Validation errors:', error.errors);
  }
};

/**
 * Express hata işleyici middleware
 * @param {Error} err - Hata nesnesi
 * @param {Object} req - Express request nesnesi
 * @param {Object} res - Express response nesnesi
 * @param {Function} next - Express next fonksiyonu
 */
const errorMiddleware = (err, req, res, next) => {
  // Hata mesajını logla
  logError(err);
  
  // Varsayılan değerler
  const statusCode = err.statusCode || 500;
  const code = err.code || 'INTERNAL_ERROR';
  const message = err.message || 'Internal server error';
  
  // Hata yanıtını oluştur
  const errorResponse = {
    success: false,
    error: {
      code,
      message
    }
  };
  
  // Validasyon hataları varsa ekle
  if (err.errors) {
    errorResponse.error.details = err.errors;
  }
  
  // Geliştirme ortamında stack trace'i ekle
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = err.stack;
  }
  
  // Yanıtı gönder
  res.status(statusCode).json(errorResponse);
};

/**
 * Async fonksiyonları try-catch ile sarar
 * @param {Function} fn - Async fonksiyon
 * @returns {Function} - Express middleware
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  AppError,
  NotFoundError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  logError,
  errorMiddleware,
  asyncHandler
};
