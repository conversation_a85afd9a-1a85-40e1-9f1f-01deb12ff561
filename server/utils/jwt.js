/**
 * JWT (JSON Web Token) yardımcı fonksiyonları
 */

const jwt = require('jsonwebtoken');
const redisClient = require('../services/redis');

// JWT secret key (normalde .env dosyasından alınır)
const JWT_SECRET = process.env.JWT_SECRET || 'network-monitor-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

/**
 * Access token oluşturur
 * @param {Object} payload - Token payload'ı
 * @returns {string} - JWT token
 */
function generateAccessToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

/**
 * Refresh token oluşturur
 * @param {Object} payload - Token payload'ı
 * @returns {string} - JWT refresh token
 */
function generateRefreshToken(payload) {
  const refreshToken = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });
  
  // Refresh token'ı Redis'e kaydet
  const userId = payload.id;
  const expiresIn = parseInt(JWT_REFRESH_EXPIRES_IN) * 24 * 60 * 60; // Gün -> saniye
  redisClient.set(`refresh_token:${userId}`, refreshToken, 'EX', expiresIn);
  
  return refreshToken;
}

/**
 * Token'ı doğrular
 * @param {string} token - Doğrulanacak token
 * @returns {Object|null} - Doğrulanmış payload veya null
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

/**
 * Refresh token'ı doğrular
 * @param {string} refreshToken - Doğrulanacak refresh token
 * @param {string} userId - Kullanıcı ID'si
 * @returns {boolean} - Token geçerli ise true
 */
async function verifyRefreshToken(refreshToken, userId) {
  try {
    // Token'ı doğrula
    const decoded = jwt.verify(refreshToken, JWT_SECRET);
    
    // Kullanıcı ID'si eşleşiyor mu kontrol et
    if (decoded.id !== userId) {
      return false;
    }
    
    // Redis'teki token ile eşleşiyor mu kontrol et
    const storedToken = await redisClient.get(`refresh_token:${userId}`);
    return storedToken === refreshToken;
  } catch (error) {
    return false;
  }
}

/**
 * Refresh token'ı geçersiz kılar
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise<boolean>} - Başarılı ise true
 */
async function invalidateRefreshToken(userId) {
  await redisClient.del(`refresh_token:${userId}`);
  return true;
}

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  verifyToken,
  verifyRefreshToken,
  invalidateRefreshToken
};
