/**
 * <PERSON><PERSON><PERSON> denemeleri yardımcı fonksiyonları
 * B<PERSON> modül, başar<PERSON>s<PERSON>z giriş denemelerini izler ve hesap kilitleme işlemlerini yönetir.
 */

const redisClient = require('../services/redis');
const settingsService = require('../services/settingsService');
const notificationService = require('../services/notificationService');
const userModel = require('../models/user');
const socketService = require('../services/socketService');

/**
 * Başarısız giriş denemesi ekle
 * @param {string} username - <PERSON><PERSON>ıc<PERSON> adı
 * @returns {Promise<number>} - Toplam başarısız deneme sayısı
 */
async function addFailedAttempt(username) {
  const key = `login:failed:${username}`;

  // Başarısız deneme sayısını artır
  const attempts = await redisClient.incr(key);

  // 24 saat sonra otomatik olarak sıfırla
  await redisClient.expire(key, 24 * 60 * 60);

  return attempts;
}

/**
 * Başarısız giriş denemelerini sıfırla
 * @param {string} username - Kullanıcı adı
 * @returns {Promise<void>}
 */
async function resetFailedAttempts(username) {
  const key = `login:failed:${username}`;
  await redisClient.del(key);
}

/**
 * Başarısız giriş denemelerini al
 * @param {string} username - Kullanıcı adı
 * @returns {Promise<number>} - Toplam başarısız deneme sayısı
 */
async function getFailedAttempts(username) {
  const key = `login:failed:${username}`;
  const attempts = await redisClient.get(key);
  return attempts ? parseInt(attempts) : 0;
}

/**
 * Hesabı kilitle
 * @param {string} username - Kullanıcı adı
 * @param {number} lockDuration - Kilit süresi (dakika)
 * @returns {Promise<void>}
 */
async function lockAccount(username, lockDuration = 30) {
  const key = `login:locked:${username}`;
  const lockUntil = Date.now() + (lockDuration * 60 * 1000);

  // Hesabı kilitle
  await redisClient.set(key, lockUntil);

  // Kilit süresini ayarla
  await redisClient.expire(key, lockDuration * 60);
}

/**
 * Hesap kilitli mi kontrol et
 * @param {string} username - Kullanıcı adı
 * @returns {Promise<Object>} - { locked: boolean, remainingTime: number }
 */
async function isAccountLocked(username) {
  const key = `login:locked:${username}`;
  const lockUntil = await redisClient.get(key);

  if (!lockUntil) {
    return { locked: false, remainingTime: 0 };
  }

  const now = Date.now();
  const lockTime = parseInt(lockUntil);

  if (now >= lockTime) {
    // Kilit süresi dolmuş, kilidi kaldır
    await redisClient.del(key);
    return { locked: false, remainingTime: 0 };
  }

  // Kalan süreyi dakika cinsinden hesapla
  const remainingTime = Math.ceil((lockTime - now) / (60 * 1000));

  return { locked: true, remainingTime };
}

/**
 * Başarısız giriş denemesini işle
 * @param {string} username - Kullanıcı adı
 * @param {Object} user - Kullanıcı nesnesi (varsa)
 * @returns {Promise<Object>} - { blocked: boolean, message: string }
 */
async function handleFailedLogin(username, user = null) {
  try {
    // Önce kullanıcı durumu kontrolü yap
    if (user) {
      const userStatus = await userModel.getUserStatus(user.id);

      // Devre dışı kontrolü
      if (userStatus.status === 'disabled') {
        return {
          blocked: true,
          message: 'Hesabınız yönetici tarafından devre dışı bırakıldı. Lütfen sistem yöneticisi ile iletişime geçin.'
        };
      }

      // Geçici kilitleme kontrolü
      if (userStatus.locked && userStatus.lockedUntil) {
        const remainingTime = Math.ceil((userStatus.lockedUntil - Date.now()) / (60 * 1000));
        if (remainingTime > 0) {
          return {
            blocked: true,
            message: `Hesabınız kilitlendi. ${remainingTime} dakika sonra tekrar deneyin.`
          };
        }
      }
    }

    // Eski sistem ile uyumluluk için loginAttempts kontrolü
    const lockStatus = await isAccountLocked(username);

    if (lockStatus.locked) {
      return {
        blocked: true,
        message: `Hesabınız kilitlendi. ${lockStatus.remainingTime} dakika sonra tekrar deneyin.`
      };
    }

    // Başarısız deneme sayısını artır
    const attempts = await addFailedAttempt(username);

    // Ayarlardan maksimum giriş denemesi sayısını al
    const settings = await settingsService.getSettings();
    const maxAttempts = parseInt(settings.maxLoginAttempts) || 5;

    // Maksimum deneme sayısı aşıldı mı kontrol et
    if (attempts >= maxAttempts) {
      // Ayarlardan kilitleme süresini al
      const lockDuration = parseInt(settings.accountLockDuration) || 30;

      // Hesabı kilitle (otomatik kilitleme - süreli)
      await lockAccount(username, lockDuration);

      // Socket ile kullanıcı durum değişikliğini yayınla
      if (user) {
        try {
          const userStatus = await userModel.getUserStatus(user.id);
          socketService.emitToAll('user:status:update', {
            userId: user.id,
            username: user.username,
            status: userStatus.status,
            locked: userStatus.locked,
            lockedUntil: userStatus.lockedUntil,
            action: 'auto_lock',
            timestamp: Date.now(),
            user: user
          });
        } catch (socketError) {
          console.error('Socket event gönderme hatası:', socketError);
        }
      }

      // Bildirim oluştur
      if (user) {
        await notificationService.createSecurityNotification(
          'Hesap Kilitlendi',
          `${username} kullanıcısı için hesap kilitlendi. Çok sayıda başarısız giriş denemesi tespit edildi.`,
          'high',
          user.id,
          {
            attempts,
            maxAttempts,
            lockDuration,
            ipAddress: 'unknown',
            timestamp: Date.now()
          }
        );
      }

      // Başarısız giriş denemesi bildirimi oluştur
      await notificationService.createSecurityNotification(
        'Başarısız Giriş Denemeleri',
        `${username} kullanıcısı için ${attempts} başarısız giriş denemesi tespit edildi. Hesap kilitlendi.`,
        'warning',
        null,
        {
          username,
          attempts,
          maxAttempts,
          action: 'account_locked',
          timestamp: Date.now()
        }
      );

      return {
        blocked: true,
        message: `Çok fazla başarısız giriş denemesi. Hesabınız ${lockDuration} dakika süreyle kilitlendi.`
      };
    }

    // Kalan deneme sayısını hesapla
    const remainingAttempts = maxAttempts - attempts;

    // Eğer başarısız deneme sayısı belirli bir eşiği geçtiyse bildirim oluştur
    // Örneğin, maksimum deneme sayısının yarısını geçtiyse
    if (attempts >= Math.ceil(maxAttempts / 2) && user) {
      await notificationService.createSecurityNotification(
        'Başarısız Giriş Denemeleri',
        `${username} kullanıcısı için ${attempts} başarısız giriş denemesi tespit edildi. Kalan deneme hakkı: ${remainingAttempts}`,
        'warning',
        user.id,
        {
          username,
          attempts,
          maxAttempts,
          remainingAttempts,
          action: 'failed_login_attempt',
          timestamp: Date.now()
        }
      );
    }

    return {
      blocked: false,
      message: `Geçersiz kullanıcı adı veya şifre. Kalan deneme hakkı: ${remainingAttempts}`
    };
  } catch (error) {
    console.error('Giriş denemesi işleme hatası:', error);
    return {
      blocked: false,
      message: 'Geçersiz kullanıcı adı veya şifre'
    };
  }
}

module.exports = {
  addFailedAttempt,
  resetFailedAttempts,
  getFailedAttempts,
  lockAccount,
  isAccountLocked,
  handleFailedLogin
};
