/**
 * <PERSON><PERSON><PERSON> geçerlilik süresi kontrolü
 */

const settingsService = require('../services/settingsService');

/**
 * Kullanıcının şifresinin süresi dolmuş mu kontrol eder
 * @param {Object} user - <PERSON><PERSON><PERSON><PERSON><PERSON> nesnesi
 * @param {Object} settings - <PERSON><PERSON><PERSON> (opsiyonel, performans için)
 * @returns {Promise<boolean>} - Şifre süresi dolmuşsa true
 */
async function isPasswordExpired(user, settings = null) {
  try {
    // Ayarları al (eğer verilmemişse)
    if (!settings) {
      settings = await settingsService.getSettings();
    }

    // Şifre geçerlilik süresi ayarını al
    const passwordExpiryDays = parseInt(settings.passwordExpiryDays) || 0;

    // Eğer 0 ise şifre süresi sınırsız
    if (passwordExpiryDays === 0) {
      return false;
    }

    // Kullanıcının şifre değiştirme zamanını al
    const passwordChangedAt = user.passwordChangedAt;

    // Eğer passwordChangedAt yoksa (eski kullanı<PERSON>ı<PERSON> için), şifre süresi dolmamış kabul et
    if (!passwordChangedAt) {
      console.log(`⚠️ Kullanıcı ${user.id} için passwordChangedAt bulunamadı, şifre süresi dolmamış kabul ediliyor`);
      return false;
    }

    // Şu anki zaman
    const now = Date.now();
    
    // Şifre değiştirme zamanından geçen süre (milisaniye)
    const timeSincePasswordChange = now - parseInt(passwordChangedAt);
    
    // Geçerlilik süresi (milisaniye)
    const expiryTimeMs = passwordExpiryDays * 24 * 60 * 60 * 1000;

    // Süre dolmuş mu?
    const isExpired = timeSincePasswordChange > expiryTimeMs;

    // Debug log
    const daysSinceChange = Math.floor(timeSincePasswordChange / (24 * 60 * 60 * 1000));
    console.log(`🔐 Kullanıcı ${user.username} şifre kontrolü: ${daysSinceChange} gün önce değiştirilmiş, limit ${passwordExpiryDays} gün, sonuç: ${isExpired ? 'Süresi dolmuş' : 'Geçerli'}`);

    return isExpired;
  } catch (error) {
    console.error('Şifre geçerlilik kontrolü hatası:', error);
    // Hata durumunda güvenli tarafta kal, şifre süresi dolmamış kabul et
    return false;
  }
}

/**
 * Kullanıcının şifresinin kaç gün sonra dolacağını hesaplar
 * @param {Object} user - Kullanıcı nesnesi
 * @param {Object} settings - Ayarlar (opsiyonel)
 * @returns {Promise<number>} - Kalan gün sayısı (negatif ise süresi dolmuş)
 */
async function getDaysUntilPasswordExpiry(user, settings = null) {
  try {
    // Ayarları al (eğer verilmemişse)
    if (!settings) {
      settings = await settingsService.getSettings();
    }

    // Şifre geçerlilik süresi ayarını al
    const passwordExpiryDays = parseInt(settings.passwordExpiryDays) || 0;

    // Eğer 0 ise şifre süresi sınırsız
    if (passwordExpiryDays === 0) {
      return Infinity;
    }

    // Kullanıcının şifre değiştirme zamanını al
    const passwordChangedAt = user.passwordChangedAt;

    // Eğer passwordChangedAt yoksa veya geçersizse, sınırsız kabul et
    if (!passwordChangedAt || passwordChangedAt === 'null' || passwordChangedAt === null) {
      console.log(`⚠️ Kullanıcı ${user.username || user.id} için passwordChangedAt geçersiz: ${passwordChangedAt}`);
      return Infinity;
    }

    // Şu anki zaman
    const now = Date.now();

    // passwordChangedAt'ı sayıya çevir
    const passwordChangeTime = parseInt(passwordChangedAt);

    // Geçersiz timestamp kontrolü
    if (isNaN(passwordChangeTime) || passwordChangeTime <= 0) {
      console.log(`⚠️ Kullanıcı ${user.username || user.id} için passwordChangedAt geçersiz timestamp: ${passwordChangedAt}`);
      return Infinity;
    }

    // Şifre değiştirme zamanından geçen süre (gün)
    const daysSinceChange = Math.floor((now - passwordChangeTime) / (24 * 60 * 60 * 1000));

    // Kalan gün sayısı
    const daysRemaining = passwordExpiryDays - daysSinceChange;

    console.log(`🔐 Kullanıcı ${user.username || user.id}: ${daysSinceChange} gün geçmiş, ${daysRemaining} gün kaldı`);

    return daysRemaining;
  } catch (error) {
    console.error('Şifre geçerlilik süresi hesaplama hatası:', error);
    return Infinity;
  }
}

/**
 * Kullanıcının şifresinin yakında dolup dolmayacağını kontrol eder
 * @param {Object} user - Kullanıcı nesnesi
 * @param {number} warningDays - Kaç gün önceden uyarı verilecek (varsayılan: 7)
 * @param {Object} settings - Ayarlar (opsiyonel)
 * @returns {Promise<boolean>} - Şifre yakında dolacaksa true
 */
async function isPasswordExpiringSoon(user, warningDays = 7, settings = null) {
  try {
    const daysRemaining = await getDaysUntilPasswordExpiry(user, settings);
    
    // Sınırsız ise false
    if (daysRemaining === Infinity) {
      return false;
    }

    // Kalan gün sayısı uyarı süresinden az ise true
    return daysRemaining <= warningDays && daysRemaining > 0;
  } catch (error) {
    console.error('Şifre yakında dolacak kontrolü hatası:', error);
    return false;
  }
}

module.exports = {
  isPasswordExpired,
  getDaysUntilPasswordExpiry,
  isPasswordExpiringSoon
};
