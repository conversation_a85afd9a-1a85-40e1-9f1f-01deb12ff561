/**
 * Or<PERSON>k yardımcı fonksiyonlar
 */

/**
 * JSON parse işlemini güvenli bir şekilde yapar
 * @param {string} str - JSON string
 * @param {Object|Array} defaultValue - Hata durumunda dönecek varsay<PERSON>lan de<PERSON>er
 * @returns {Object|Array} - Parse edilmiş nesne veya varsayılan değer
 */
const safeJsonParse = (str, defaultValue = {}) => {
  if (!str || typeof str !== 'string') return defaultValue;
  try {
    return JSON.parse(str);
  } catch (e) {
    console.error('Error parsing JSON:', e);
    return defaultValue;
  }
};

/**
 * JSON stringify işlemini güvenli bir şekilde yapar
 * @param {Object|Array} obj - JSON'a dönüştürülecek nesne
 * @param {string} defaultValue - Hata durumunda dönecek varsayılan değer
 * @returns {string} - JSON string veya varsayılan değer
 */
const safeJsonStringify = (obj, defaultValue = '{}') => {
  if (!obj) return defaultValue;
  try {
    return JSON.stringify(obj);
  } catch (e) {
    console.error('Error stringifying JSON:', e);
    return defaultValue;
  }
};

/**
 * Bir nesnenin belirli alanlarını JSON'dan parse eder
 * @param {Object} obj - İşlenecek nesne
 * @param {Array} fields - Parse edilecek alanlar
 * @param {Object} defaultValues - Alanlar için varsayılan değerler
 * @returns {Object} - Alanları parse edilmiş nesne
 */
const parseJsonFields = (obj, fields, defaultValues = {}) => {
  if (!obj) return obj;
  
  const result = { ...obj };
  
  fields.forEach(field => {
    if (result[field]) {
      const defaultValue = defaultValues[field] || {};
      result[field] = safeJsonParse(result[field], defaultValue);
    }
  });
  
  return result;
};

/**
 * Bir nesnenin belirli alanlarını JSON'a dönüştürür
 * @param {Object} obj - İşlenecek nesne
 * @param {Array} fields - Dönüştürülecek alanlar
 * @returns {Object} - Alanları JSON'a dönüştürülmüş nesne
 */
const stringifyJsonFields = (obj, fields) => {
  if (!obj) return obj;
  
  const result = { ...obj };
  
  fields.forEach(field => {
    if (result[field]) {
      result[field] = safeJsonStringify(result[field]);
    }
  });
  
  return result;
};

/**
 * Bir dizi içindeki nesnelerin belirli alanlarını JSON'dan parse eder
 * @param {Array} array - İşlenecek dizi
 * @param {Array} fields - Parse edilecek alanlar
 * @param {Object} defaultValues - Alanlar için varsayılan değerler
 * @returns {Array} - Alanları parse edilmiş nesnelerden oluşan dizi
 */
const parseJsonFieldsInArray = (array, fields, defaultValues = {}) => {
  if (!Array.isArray(array)) return array;
  
  return array.map(item => parseJsonFields(item, fields, defaultValues));
};

/**
 * Bir IP adresinin geçerli olup olmadığını kontrol eder
 * @param {string} ip - Kontrol edilecek IP adresi
 * @returns {boolean} - IP adresi geçerliyse true, değilse false
 */
const isValidIpAddress = (ip) => {
  return /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/.test(ip);
};

/**
 * Bir tarih nesnesini formatlar
 * @param {Date|number} date - Formatlanacak tarih
 * @param {string} format - Format şablonu
 * @returns {string} - Formatlanmış tarih
 */
const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  const d = new Date(date);
  
  const replacements = {
    'YYYY': d.getFullYear(),
    'MM': String(d.getMonth() + 1).padStart(2, '0'),
    'DD': String(d.getDate()).padStart(2, '0'),
    'HH': String(d.getHours()).padStart(2, '0'),
    'mm': String(d.getMinutes()).padStart(2, '0'),
    'ss': String(d.getSeconds()).padStart(2, '0')
  };
  
  let result = format;
  for (const [key, value] of Object.entries(replacements)) {
    result = result.replace(key, value);
  }
  
  return result;
};

/**
 * Bir süreyi insan tarafından okunabilir formata dönüştürür
 * @param {number} ms - Milisaniye cinsinden süre
 * @returns {string} - Formatlanmış süre
 */
const formatDuration = (ms) => {
  if (!ms) return '0 ms';
  
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days} gün ${hours % 24} saat`;
  } else if (hours > 0) {
    return `${hours} saat ${minutes % 60} dakika`;
  } else if (minutes > 0) {
    return `${minutes} dakika ${seconds % 60} saniye`;
  } else if (seconds > 0) {
    return `${seconds} saniye`;
  } else {
    return `${ms} ms`;
  }
};

/**
 * Bir nesneyi belirli bir süre sonra temizleyen bir önbellek oluşturur
 * @param {number} ttl - Önbellek süresi (ms)
 * @returns {Object} - Önbellek nesnesi
 */
const createCache = (ttl = 60000) => {
  const cache = {};
  
  return {
    get: (key) => {
      const item = cache[key];
      if (!item) return null;
      
      if (Date.now() > item.expiry) {
        delete cache[key];
        return null;
      }
      
      return item.value;
    },
    set: (key, value, customTtl) => {
      const expiry = Date.now() + (customTtl || ttl);
      cache[key] = { value, expiry };
    },
    delete: (key) => {
      delete cache[key];
    },
    clear: () => {
      Object.keys(cache).forEach(key => delete cache[key]);
    }
  };
};

module.exports = {
  safeJsonParse,
  safeJsonStringify,
  parseJsonFields,
  stringifyJsonFields,
  parseJsonFieldsInArray,
  isValidIpAddress,
  formatDate,
  formatDuration,
  createCache
};
