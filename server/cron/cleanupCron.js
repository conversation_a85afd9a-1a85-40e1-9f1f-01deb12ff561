/**
 * Te<PERSON>zleme cron job'ı
 * Eski verileri ve bildirimleri düzenli olarak temizler
 */
const cron = require('node-cron');
const cleanupService = require('../services/cleanupService');

// Her gün gece yarısı çalışacak cron job
const initCleanupCron = () => {
  // Her gün gece yarısı çalış (00:00)
  const job = cron.schedule('0 0 * * *', async () => {
    console.log('Running cleanup tasks...');
    
    try {
      const result = await cleanupService.runCleanupTasks();
      
      if (result.success) {
        console.log('Cleanup tasks completed successfully');
        console.log(`Cleaned up ${result.notifications.count} old notifications`);
      } else {
        console.error('Cleanup tasks failed:', result.error);
      }
    } catch (error) {
      console.error('Error running cleanup tasks:', error);
    }
  });

  console.log('Cleanup cron job initialized');
  return job;
};

module.exports = {
  initCleanupCron
};
