/**
 * Redis anahtarları için sabit değerler
 */

// <PERSON><PERSON>az anahtarları
const DEVICES_SET = 'devices';
const DEVICE_KEY = (id) => `device:${id}`;
const DEVICE_GROUPS_SET = 'device:groups';

// İzleme anahtarları
const MONITOR_KEY = (type, id) => `monitor:${type}:${id}`;
const MONITOR_HISTORY_KEY = (type, id) => `history:${type}:${id}`;

// Durum anahtarları
const DEVICE_STATUS_KEY = (id) => `device:${id}:status`;
const DEVICE_STATUS_CALCULATED_KEY = (id) => `device:${id}:status:calculated`;
const DEVICE_STATUS_HISTORY_KEY = (id) => `device:status:history:${id}`;

// Bildirim anahtarları
const NOTIFICATIONS_LIST = 'notifications';
const NOTIFICATION_KEY = (id) => `notification:${id}`;
const NOTIFICATION_COUNTER = 'notification:counter';

// Kullanıcı anahtarları
const USERS_SET = 'users';
const USER_KEY = (id) => `user:${id}`;
const USER_SESSIONS_KEY = (id) => `user:${id}:sessions`;

// Ayarlar anahtarları
const SETTINGS_KEY = 'settings';
const MONITOR_SETTINGS_KEY = 'settings:monitors';
const NOTIFICATION_SETTINGS_KEY = 'settings:notifications';

// Zamanlayıcı anahtarları
const SCHEDULER_LAST_RUN_KEY = 'scheduler:last_run';
const SCHEDULER_NEXT_RUN_KEY = 'scheduler:next_run';
const SCHEDULER_DEVICE_LAST_CHECK_KEY = (id, type) => `scheduler:device:${id}:${type}:last_check`;
const SCHEDULER_DEVICE_NEXT_CHECK_KEY = (id, type) => `scheduler:device:${id}:${type}:next_check`;

// TTL değerleri (saniye cinsinden)
const TTL = {
  MONITOR_DATA: 86400, // 24 saat
  HISTORY_DATA: 2592000, // 30 gün
  USER_SESSION: 604800, // 7 gün
  NOTIFICATION: 2592000 // 30 gün
};

// Maksimum liste boyutları
const MAX_LIST_SIZE = {
  HISTORY: 100, // Son 100 ölçüm
  STATUS_HISTORY: 10, // Son 10 durum
  NOTIFICATIONS: 1000 // Son 1000 bildirim
};

module.exports = {
  DEVICES_SET,
  DEVICE_KEY,
  DEVICE_GROUPS_SET,
  MONITOR_KEY,
  MONITOR_HISTORY_KEY,
  DEVICE_STATUS_KEY,
  DEVICE_STATUS_CALCULATED_KEY,
  DEVICE_STATUS_HISTORY_KEY,
  NOTIFICATIONS_LIST,
  NOTIFICATION_KEY,
  NOTIFICATION_COUNTER,
  USERS_SET,
  USER_KEY,
  USER_SESSIONS_KEY,
  SETTINGS_KEY,
  MONITOR_SETTINGS_KEY,
  NOTIFICATION_SETTINGS_KEY,
  SCHEDULER_LAST_RUN_KEY,
  SCHEDULER_NEXT_RUN_KEY,
  SCHEDULER_DEVICE_LAST_CHECK_KEY,
  SCHEDULER_DEVICE_NEXT_CHECK_KEY,
  TTL,
  MAX_LIST_SIZE
};
