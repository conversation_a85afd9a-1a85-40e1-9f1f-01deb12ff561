/**
 * <PERSON><PERSON><PERSON><PERSON> sistemi için sabit tanımlamalar
 * Bu dosya hem backend hem de frontend tarafından kullanılabilir
 */

// Bildirim türleri
const NOTIFICATION_TYPES = {
  DEVICE: 'device',
  SYSTEM: 'system',
  USER: 'user'
};

// ✅ Basit Bildirim <PERSON>ileri - Sadece Source Türleri
const NOTIFICATION_SOURCES = {
  DEVICE: 'device',
  SYSTEM: 'system'
};

// ✅ Legacy Support - Eski kod uyumluluğu için
const NOTIFICATION_CATEGORIES = {
  // Basit mapping - sadece source türleri
  DEVICE: 'device',
  SYSTEM: 'system',
  USER: 'user',

  // Legacy uyumluluk
  DEVICE_CONNECTION: 'device',
  DEVICE_PERFORMANCE: 'device',
  DEVICE_SERVICE: 'device',
  DEVICE_SECURITY: 'device',
  DEVICE_HARDWARE: 'device',
  SYSTEM_MONITORING: 'system',
  SYSTEM_USER: 'system',
  SYSTEM_SECURITY: 'system',
  SYSTEM_MAINTENANCE: 'system',
  SYSTEM_ERROR: 'system',

  // Diğer legacy
  STATUS: 'device',
  PERFORMANCE: 'device',
  SECURITY: 'system',
  MAINTENANCE: 'system'
};

// Bildirim önem dereceleri
const NOTIFICATION_SEVERITY = {
  CRITICAL: 'critical',
  WARNING: 'warning',
  INFO: 'info',
  SUCCESS: 'success'
};

// Bildirim durumları
// ✅ Basit Bildirim Durumları
const NOTIFICATION_STATUS = {
  NEW: 'new',
  READ: 'read',
  RESOLVED: 'resolved'
};

// ✅ Basit Kaynak Renkleri
const SOURCE_COLORS = {
  [NOTIFICATION_SOURCES.DEVICE]: 'blue',
  [NOTIFICATION_SOURCES.SYSTEM]: 'green'
};

// ✅ Basit Kaynak İkonları
const SOURCE_ICONS = {
  [NOTIFICATION_SOURCES.DEVICE]: 'Smartphone',
  [NOTIFICATION_SOURCES.SYSTEM]: 'Settings'
};

// ✅ Önem Derecesi Renkleri
const SEVERITY_COLORS = {
  [NOTIFICATION_SEVERITY.CRITICAL]: 'red',
  [NOTIFICATION_SEVERITY.WARNING]: 'orange',
  [NOTIFICATION_SEVERITY.INFO]: 'blue',
  [NOTIFICATION_SEVERITY.SUCCESS]: 'green'
};

// ✅ Önem Derecesi İkonları
const SEVERITY_ICONS = {
  [NOTIFICATION_SEVERITY.CRITICAL]: 'AlertTriangle',
  [NOTIFICATION_SEVERITY.WARNING]: 'AlertCircle',
  [NOTIFICATION_SEVERITY.INFO]: 'Info',
  [NOTIFICATION_SEVERITY.SUCCESS]: 'CheckCircle'
};

// ✅ Durum Renkleri
const STATUS_COLORS = {
  [NOTIFICATION_STATUS.NEW]: 'red',
  [NOTIFICATION_STATUS.READ]: 'gray',
  [NOTIFICATION_STATUS.RESOLVED]: 'green'
};

// ✅ Durum İkonları
const STATUS_ICONS = {
  [NOTIFICATION_STATUS.NEW]: 'Circle',
  [NOTIFICATION_STATUS.READ]: 'Eye',
  [NOTIFICATION_STATUS.RESOLVED]: 'CheckCircle'
};

// ✅ Eski duplicate SEVERITY_COLORS kaldırıldı

module.exports = {
  NOTIFICATION_TYPES,
  NOTIFICATION_SOURCES,
  NOTIFICATION_CATEGORIES,
  NOTIFICATION_SEVERITY,
  NOTIFICATION_STATUS,
  SOURCE_COLORS,
  SOURCE_ICONS,
  SEVERITY_COLORS,
  SEVERITY_ICONS,
  STATUS_COLORS,
  STATUS_ICONS
};
