const express = require('express');
const router = express.Router();
const redisClient = require('../config/redis');
const os = require('os');
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Disk kullanım bilgilerini alır
 * @returns {Object} - Disk kullanım bilgileri
 */
function getDiskInfo() {
  try {
    // Windows veya Linux/Mac için farklı komutlar kullan
    let dfOutput;
    if (process.platform === 'win32') {
      // Windows için disk bilgilerini al
      try {
        // Windows'ta PowerShell komutu kullanarak disk bilgilerini al
        dfOutput = execSync('powershell -command "Get-Volume | Select-Object DriveLetter, FileSystemLabel, Size, SizeRemaining | Format-Table -AutoSize"').toString();
      } catch (err) {
        console.error('Windows disk bilgileri alınamadı:', err);
        // Varsayılan değerler döndür
        return {
          disks: [{
            source: 'C:',
            size: '100GB',
            used: '50GB',
            avail: '50GB',
            pcent: 50,
            target: 'C:'
          }],
          total: {
            size: '100GB',
            used: '50GB',
            avail: '50GB',
            pcent: 50
          }
        };
      }
    } else {
      // Linux/Mac için df komutu kullan - Oracle Linux için daha basit komut
      try {
        dfOutput = execSync('df -h | grep -v "tmpfs\\|udev\\|loop"').toString();
      } catch (err) {
        console.error('Linux df komutu çalıştırılamadı:', err);
        try {
          // Alternatif komut dene
          dfOutput = execSync('df -h').toString();
        } catch (err2) {
          console.error('Alternatif df komutu da çalıştırılamadı:', err2);
          return {
            disks: [],
            total: {
              size: 'N/A',
              used: 'N/A',
              avail: 'N/A',
              pcent: 0
            }
          };
        }
      }
    }

    console.log('Disk bilgileri çıktısı:', dfOutput); // Debug için

    // Çıktıyı satırlara böl
    const lines = dfOutput.trim().split('\n');

    // İlk satır başlık satırı, onu atla
    const disks = [];

    // Windows veya Linux/Mac için farklı işlemler
    if (process.platform === 'win32') {
      // Windows için disk bilgilerini işle
      for (let i = 2; i < lines.length; i++) { // İlk 2 satır başlık
        const line = lines[i].trim();
        if (!line) continue;

        const parts = line.split(/\s+/);
        if (parts.length >= 4) {
          const driveLetter = parts[0];
          const label = parts[1] || '';
          const size = parts[2];
          const sizeRemaining = parts[3];

          // Kullanılan alanı hesapla
          const sizeInBytes = parseFloat(size);
          const remainingInBytes = parseFloat(sizeRemaining);
          const usedInBytes = sizeInBytes - remainingInBytes;

          // Kullanım yüzdesini hesapla
          const pcent = Math.round((usedInBytes / sizeInBytes) * 100);

          disks.push({
            source: driveLetter,
            size: formatBytes(sizeInBytes),
            used: formatBytes(usedInBytes),
            avail: formatBytes(remainingInBytes),
            pcent: pcent,
            target: `${driveLetter}${label ? ' (' + label + ')' : ''}`
          });
        }
      }
    } else {
      // Linux/Mac için disk bilgilerini işle
      // Oracle Linux için farklı format olabilir
      for (let i = 1; i < lines.length; i++) { // İlk satır başlık
        const line = lines[i].trim();
        if (!line) continue;

        const parts = line.split(/\s+/);
        console.log('Disk satırı:', line, 'Parçalar:', parts); // Debug için

        // Standart Linux df çıktısı formatı: Filesystem, Size, Used, Avail, Use%, Mounted on
        if (parts.length >= 6) {
          const source = parts[0];
          const size = parts[1];
          const used = parts[2];
          const avail = parts[3];
          const pcent = parts[4].replace('%', ''); // Yüzde işaretini kaldır
          const target = parts.slice(5).join(' '); // Hedef dizin boşluk içerebilir

          // Gerçek dosya sistemlerini filtrele (devfs, tmpfs gibi özel sistemleri atla)
          if (!source.includes('tmpfs') && !source.includes('devfs') && !source.includes('none')) {
            disks.push({
              source,
              size,
              used,
              avail,
              pcent: parseInt(pcent, 10) || 0,
              target
            });
          }
        }
      }
    }

    // Toplam disk kullanımını hesapla
    let totalSize = 0;
    let totalUsed = 0;

    disks.forEach(disk => {
      // Size ve used değerlerini byte'a çevir
      const sizeInBytes = convertToBytes(disk.size);
      const usedInBytes = convertToBytes(disk.used);

      if (!isNaN(sizeInBytes) && !isNaN(usedInBytes)) {
        totalSize += sizeInBytes;
        totalUsed += usedInBytes;
      }
    });

    // Toplam kullanım yüzdesini hesapla
    const totalUsagePercent = totalSize > 0 ? Math.round((totalUsed / totalSize) * 100) : 0;

    return {
      disks,
      total: {
        size: formatBytes(totalSize),
        used: formatBytes(totalUsed),
        avail: formatBytes(totalSize - totalUsed),
        pcent: totalUsagePercent
      }
    };
  } catch (error) {
    console.error('Disk bilgileri alınırken hata oluştu:', error);
    return {
      disks: [],
      total: {
        size: 'N/A',
        used: 'N/A',
        avail: 'N/A',
        pcent: 0
      }
    };
  }
}

/**
 * Boyut bilgisini (1K, 2M, 3G gibi) byte'a çevirir
 * @param {string} sizeStr - Boyut bilgisi
 * @returns {number} - Byte cinsinden boyut
 */
function convertToBytes(sizeStr) {
  if (!sizeStr) return 0;

  const units = {
    'B': 1,
    'K': 1024,
    'M': 1024 * 1024,
    'G': 1024 * 1024 * 1024,
    'T': 1024 * 1024 * 1024 * 1024
  };

  // Sayı ve birim kısmını ayır
  const match = sizeStr.match(/^(\d+(?:\.\d+)?)([BKMGT])?$/i);
  if (!match) return 0;

  const size = parseFloat(match[1]);
  const unit = match[2] ? match[2].toUpperCase() : 'B';

  return size * (units[unit] || 1);
}

/**
 * Byte cinsinden boyutu insan tarafından okunabilir formata çevirir
 * @param {number} bytes - Byte cinsinden boyut
 * @returns {string} - İnsan tarafından okunabilir boyut
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + units[i];
}

/**
 * İşletim sistemi bilgilerini alır
 * @returns {Object} - İşletim sistemi bilgileri
 */
function getOsInfo() {
  try {
    // Temel bilgileri Node.js os modülünden al
    const osInfo = {
      platform: process.platform,
      release: os.release(),
      type: os.type(),
      arch: os.arch(),
      hostname: os.hostname(),
      version: os.version ? os.version() : null
    };

    // Linux için ek bilgileri al
    if (process.platform === 'linux') {
      try {
        // /etc/os-release dosyasını oku (çoğu Linux dağıtımında bulunur)
        if (fs.existsSync('/etc/os-release')) {
          const osReleaseContent = fs.readFileSync('/etc/os-release', 'utf8');
          const osReleaseLines = osReleaseContent.split('\n');

          // Değerleri parse et
          const osReleaseData = {};
          osReleaseLines.forEach(line => {
            const parts = line.split('=');
            if (parts.length === 2) {
              let key = parts[0];
              let value = parts[1].replace(/"/g, '');
              osReleaseData[key] = value;
            }
          });

          // Dağıtım adını ve sürümünü ekle
          if (osReleaseData.NAME) {
            osInfo.distro = osReleaseData.NAME;
          }
          if (osReleaseData.VERSION) {
            osInfo.distroVersion = osReleaseData.VERSION;
          }
          if (osReleaseData.PRETTY_NAME) {
            osInfo.prettyName = osReleaseData.PRETTY_NAME;
          }
        }

        // Oracle Linux için özel kontrol
        if (fs.existsSync('/etc/oracle-release')) {
          const oracleReleaseContent = fs.readFileSync('/etc/oracle-release', 'utf8');
          osInfo.distro = 'Oracle Linux';
          osInfo.prettyName = oracleReleaseContent.trim();

          // Sürüm numarasını çıkar
          const versionMatch = oracleReleaseContent.match(/release (\d+(\.\d+)*)/);
          if (versionMatch) {
            osInfo.distroVersion = versionMatch[1];
          }
        }

        // Kernel sürümünü al
        try {
          const kernelVersion = execSync('uname -r').toString().trim();
          osInfo.kernelVersion = kernelVersion;
        } catch (err) {
          console.error('Kernel sürümü alınamadı:', err);
        }
      } catch (err) {
        console.error('Linux dağıtım bilgileri alınamadı:', err);
      }
    }

    // Windows için ek bilgileri al
    else if (process.platform === 'win32') {
      try {
        const winVerOutput = execSync('ver').toString().trim();
        const versionMatch = winVerOutput.match(/\[(Version\s+(.+))\]/);
        if (versionMatch) {
          osInfo.prettyName = `Windows ${versionMatch[2]}`;
        }
      } catch (err) {
        console.error('Windows sürüm bilgileri alınamadı:', err);
      }
    }

    // macOS için ek bilgileri al
    else if (process.platform === 'darwin') {
      try {
        const macVerOutput = execSync('sw_vers').toString().trim();
        const productNameMatch = macVerOutput.match(/ProductName:\s+(.+)/);
        const productVersionMatch = macVerOutput.match(/ProductVersion:\s+(.+)/);

        if (productNameMatch) {
          osInfo.distro = productNameMatch[1];
        }
        if (productVersionMatch) {
          osInfo.distroVersion = productVersionMatch[1];
        }
        if (productNameMatch && productVersionMatch) {
          osInfo.prettyName = `${productNameMatch[1]} ${productVersionMatch[1]}`;
        }
      } catch (err) {
        console.error('macOS sürüm bilgileri alınamadı:', err);
      }
    }

    return osInfo;
  } catch (error) {
    console.error('İşletim sistemi bilgileri alınırken hata oluştu:', error);
    return {
      platform: process.platform,
      release: os.release(),
      type: os.type(),
      arch: os.arch()
    };
  }
}

/**
 * Sistem servislerinin durumunu getirir
 * GET /api/system/status
 */
router.get('/status', async (req, res) => {
  try {
    // Redis durumunu kontrol et
    let redisStatus = 'up';
    try {
      await redisClient.ping();
    } catch (error) {
      redisStatus = 'down';
    }

    // Disk bilgilerini al
    const diskInfo = getDiskInfo();
    console.log('Disk bilgileri:', JSON.stringify(diskInfo));

    // İşletim sistemi bilgilerini al
    const osInfo = getOsInfo();
    console.log('İşletim sistemi bilgileri:', JSON.stringify(osInfo));

    // Sistem bilgilerini al
    const systemInfo = {
      uptime: Math.floor(os.uptime()),
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem()
      },
      cpu: os.cpus(),
      loadAvg: os.loadavg(),
      disk: diskInfo,
      os: osInfo
    };

    // Redis uptime bilgisini almaya çalış
    let redisUptime = null;
    if (redisStatus === 'up') {
      try {
        // Redis INFO komutunu çalıştır
        const redisInfo = await redisClient.info();
        // uptime_in_seconds değerini bul
        const uptimeMatch = redisInfo.match(/uptime_in_seconds:(\d+)/);
        if (uptimeMatch && uptimeMatch[1]) {
          redisUptime = parseInt(uptimeMatch[1], 10);
        }
      } catch (error) {
        console.error('Redis uptime bilgisi alınamadı:', error);
      }
    }

    // Zamanlayıcı uptime bilgisini al
    let schedulerUptime = null;
    if (global.schedulerStartTime) {
      schedulerUptime = Math.floor((Date.now() - global.schedulerStartTime) / 1000);
    }

    // Servis durumlarını oluştur
    const services = {
      backend: {
        status: 'up',
        name: 'Backend API',
        description: 'Node.js Express API Sunucusu',
        uptime: process.uptime(),
        version: process.version
      },
      redis: {
        status: redisStatus,
        name: 'Redis',
        description: 'Veritabanı ve Önbellek Servisi',
        uptime: redisUptime
      },
      scheduler: {
        status: global.schedulerRunning ? 'up' : 'down',
        name: 'Zamanlayıcı',
        description: 'Cihaz İzleme Zamanlayıcısı',
        uptime: schedulerUptime,
        lastRun: global.lastSchedulerRun || null
      }
    };

    res.json({
      services,
      systemInfo
    });
  } catch (error) {
    console.error('Error getting system status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Sistem istatistiklerini getirir
 * GET /api/system/stats
 */
router.get('/stats', async (req, res) => {
  try {
    // Redis'ten istatistikleri al
    const deviceCount = await redisClient.scard('devices');
    const alertCount = await redisClient.zcard('alerts');

    // Son 24 saatteki izleme sayısını al
    const monitorCount = await redisClient.get('stats:monitors:count') || 0;

    // Aktif kullanıcı sayısını al
    const activeUserCount = await redisClient.scard('active_users') || 0;

    res.json({
      devices: parseInt(deviceCount),
      alerts: parseInt(alertCount),
      monitors: parseInt(monitorCount),
      activeUsers: parseInt(activeUserCount),
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Error getting system stats:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
module.exports.getDiskInfo = getDiskInfo;
