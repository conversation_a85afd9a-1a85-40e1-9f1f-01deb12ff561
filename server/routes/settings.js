const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const emailService = require('../services/emailService');

// <PERSON>yarlar dosyası yolu
const settingsFilePath = path.join(__dirname, '../data/settings.json');

// Varsayılan ayarlar
const defaultSettings = {
  // Uygulama kimliği
  appTitle: 'NetWatch',
  companyName: 'NetWatch',
  // E-posta bildirimleri
  emailNotifications: false,
  emailServer: '',
  emailPort: '',
  emailUser: '',
  emailPassword: '',
  emailFrom: '',
  emailTo: '',
  emailSecure: true,

  // Veri saklama süreleri
  notificationRetentionDays: '30', // Bildirim saklama süresi
  // İzleme türleri için saklama süreleri
  icmpRetentionDays: '30',
  httpRetentionDays: '30',
  tcpRetentionDays: '30',
  snmpRetentionDays: '30',
  dnsRetentionDays: '30',
  sslRetentionDays: '30',
  databaseRetentionDays: '30',
  apiRetentionDays: '30',
  smtpRetentionDays: '30',
  windowsRetentionDays: '30',
  linuxRetentionDays: '30',
  ipmiRetentionDays: '30',
  dockerRetentionDays: '30',
  // Sistem sağlığı kontrol aralığı (dakika)
  systemHealthCheckInterval: '1',
  // Güvenlik ayarları
  sessionTimeout: '60', // Dakika cinsinden oturum zaman aşımı
  passwordPolicy: 'medium', // low, medium, high
  passwordExpiryDays: '90', // Şifre geçerlilik süresi (gün)
  maxLoginAttempts: '5', // Maksimum başarısız giriş denemesi

  bruteForceProtection: true, // Brute force koruması
  securityLogRetention: '30', // Güvenlik logları saklama süresi (gün)
  autoLogout: true, // Otomatik çıkış
  // Görsel ayarlar
  darkMode: false, // Karanlık tema
  language: 'tr', // Dil
  timezone: 'Europe/Istanbul', // Zaman dilimi
  dateFormat: 'DD.MM.YYYY', // Tarih formatı
  timeFormat: '24h', // Saat formatı
  // DNS ayarları
  defaultDnsServer: '*******' // Varsayılan DNS sunucusu
};

// Ayarlar dosyasını oluştur veya oku
const getSettings = async () => {
  try {
    // data klasörünün varlığını kontrol et
    try {
      await fs.access(path.join(__dirname, '../data'));
    } catch (err) {
      // data klasörü yoksa oluştur
      await fs.mkdir(path.join(__dirname, '../data'), { recursive: true });
    }

    // Ayarlar dosyasını oku
    const data = await fs.readFile(settingsFilePath, 'utf8');
    return JSON.parse(data);
  } catch (err) {
    // Dosya yoksa veya okunamazsa varsayılan ayarları kullan
    await fs.writeFile(settingsFilePath, JSON.stringify(defaultSettings, null, 2));
    return defaultSettings;
  }
};

// Ayarları kaydet
const saveSettings = async (settings) => {
  await fs.writeFile(settingsFilePath, JSON.stringify(settings, null, 2));
  return settings;
};

// Tüm ayarları getir
router.get('/', async (req, res) => {
  try {
    const settings = await getSettings();
    res.json(settings);
  } catch (err) {
    console.error('Error getting settings:', err);
    res.status(500).json({ error: 'Ayarlar alınırken bir hata oluştu' });
  }
});

// Ayarları güncelle
router.put('/', async (req, res) => {
  try {
    const newSettings = req.body;
    const settings = await saveSettings(newSettings);
    res.json(settings);
  } catch (err) {
    console.error('Error updating settings:', err);
    res.status(500).json({ error: 'Ayarlar güncellenirken bir hata oluştu' });
  }
});

/**
 * E-posta ayarlarını test eder
 * POST /api/settings/test-email
 */
router.post('/test-email', async (req, res) => {
  try {
    console.log('📧 Test e-postası gönderme isteği alındı');

    const result = await emailService.testConfiguration();

    if (result.success) {
      console.log('✅ Test e-postası başarıyla gönderildi');
      res.json({
        success: true,
        message: result.message
      });
    } else {
      console.log('❌ Test e-postası gönderilemedi:', result.error);
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('❌ Test e-postası endpoint hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Test e-postası gönderilirken bir hata oluştu'
    });
  }
});

module.exports = router;
