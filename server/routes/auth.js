/**
 * <PERSON><PERSON> doğrulama rotaları
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const userModel = require('../models/user');
const notificationService = require('../services/notificationService');
const emailService = require('../services/emailService');
const {
  generateAccessToken,
  generateRefreshToken,
  verifyRefreshToken,
  invalidateRefreshToken
} = require('../utils/jwt');
const { authenticate } = require('../middleware/auth');
const { validatePasswordWithSettings } = require('../utils/passwordPolicy');
const { handleFailedLogin, resetFailedAttempts, isAccountLocked } = require('../utils/loginAttempts');
const { updateLastActivity } = require('../middleware/sessionTimeout');
const { isPasswordExpired } = require('../utils/passwordExpiry');
const {
  generatePasswordResetToken,
  verifyPasswordResetToken,
  markTokenAsUsed,
  clearUserPasswordResetTokens,
  canRequestPasswordReset
} = require('../utils/passwordReset');

const router = express.Router();

/**
 * Kullanıcı kaydı
 * POST /api/auth/register
 */
router.post('/register', [
  // Validasyon kuralları
  body('username')
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Kullanıcı adı 3-30 karakter arasında olmalıdır')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Kullanıcı adı sadece harf, rakam ve alt çizgi içerebilir'),

  body('email')
    .trim()
    .isEmail()
    .withMessage('Geçerli bir e-posta adresi giriniz')
    .normalizeEmail(),

  body('password')
    .notEmpty()
    .withMessage('Şifre gereklidir'),

  body('role')
    .optional()
    .isIn(['admin', 'user'])
    .withMessage('Geçersiz rol')
], async (req, res) => {
  try {
    // Validasyon hatalarını kontrol et
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Şifre politikasını kontrol et
    const { password } = req.body;
    const passwordValidation = await validatePasswordWithSettings(password);

    if (!passwordValidation.valid) {
      return res.status(400).json({ error: passwordValidation.message });
    }

    // Kullanıcıyı oluştur
    const user = await userModel.createUser(req.body);

    // Kullanıcı oluşturuldu bildirimi oluştur
    await notificationService.createUserCreatedNotification(user);

    // Token'ları oluştur
    const accessToken = generateAccessToken({ id: user.id, role: user.role });
    const refreshToken = generateRefreshToken({ id: user.id, role: user.role });

    res.status(201).json({
      user,
      accessToken,
      refreshToken
    });
  } catch (error) {
    console.error('Kullanıcı kaydı hatası:', error);
    res.status(400).json({ error: error.message });
  }
});

/**
 * Kullanıcı girişi
 * POST /api/auth/login
 */
router.post('/login', [
  // Validasyon kuralları
  body('username')
    .trim()
    .notEmpty()
    .withMessage('Kullanıcı adı gerekli'),

  body('password')
    .notEmpty()
    .withMessage('Şifre gerekli')
], async (req, res) => {
  try {
    // Validasyon hatalarını kontrol et
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { username, password } = req.body;

    // Kullanıcıyı bul
    const user = await userModel.getUserByUsername(username, true);

    // Kullanıcı bulunamadıysa
    if (!user) {
      // Başarısız giriş denemesini işle
      const result = await handleFailedLogin(username);
      return res.status(401).json({ error: result.message });
    }

    // Kullanıcı durumu kontrolü
    const userStatus = await userModel.getUserStatus(user.id);

    // Devre dışı kontrolü (en yüksek öncelik)
    if (userStatus.status === 'disabled') {
      return res.status(401).json({
        error: 'Hesabınız yönetici tarafından devre dışı bırakıldı. Lütfen sistem yöneticisi ile iletişime geçin.',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // Geçici kilitleme kontrolü
    if (userStatus.locked && userStatus.lockedUntil) {
      const remainingTime = Math.ceil((userStatus.lockedUntil - Date.now()) / (60 * 1000));
      if (remainingTime > 0) {
        return res.status(401).json({
          error: `Hesabınız kilitlendi. ${remainingTime} dakika sonra tekrar deneyin.`,
          code: 'ACCOUNT_LOCKED'
        });
      }
    }

    // Şifreyi doğrula
    const isValid = await userModel.verifyPassword(user.id, password);

    if (!isValid) {
      // Başarısız giriş denemesini işle
      const result = await handleFailedLogin(username, user);
      return res.status(401).json({ error: result.message });
    }

    // Başarılı giriş, başarısız denemeleri sıfırla
    await resetFailedAttempts(username);

    // 🔐 Şifre geçerlilik kontrolü
    const passwordExpired = await isPasswordExpired(user);
    if (passwordExpired) {
      return res.status(401).json({
        error: 'Şifrenizin süresi dolmuş. Lütfen şifrenizi değiştirin.',
        code: 'PASSWORD_EXPIRED',
        requirePasswordChange: true
      });
    }

    // Son aktivite zamanını güncelle
    await updateLastActivity(user.id);

    // Token'ları oluştur
    const accessToken = generateAccessToken({ id: user.id, role: user.role });
    const refreshToken = generateRefreshToken({ id: user.id, role: user.role });

    // Şifre olmadan kullanıcıyı döndür
    const { password: _, ...userWithoutPassword } = user;

    // Başarılı giriş bildirimi oluştur
    const ipAddress = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';
    await notificationService.createUserLoginNotification(userWithoutPassword, ipAddress);

    res.json({
      user: userWithoutPassword,
      accessToken,
      refreshToken
    });
  } catch (error) {
    console.error('Kullanıcı girişi hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Token yenileme
 * POST /api/auth/refresh-token
 */
router.post('/refresh-token', async (req, res) => {
  try {
    const { refreshToken, userId } = req.body;

    if (!refreshToken || !userId) {
      return res.status(400).json({ error: 'Refresh token ve kullanıcı ID\'si gerekli' });
    }

    // Refresh token'ı doğrula
    const isValid = await verifyRefreshToken(refreshToken, userId);

    if (!isValid) {
      return res.status(401).json({ error: 'Geçersiz veya süresi dolmuş refresh token' });
    }

    // Kullanıcıyı bul
    const user = await userModel.getUserById(userId);

    if (!user) {
      return res.status(404).json({ error: 'Kullanıcı bulunamadı' });
    }

    // Yeni token'ları oluştur
    const accessToken = generateAccessToken({ id: user.id, role: user.role });
    const newRefreshToken = generateRefreshToken({ id: user.id, role: user.role });

    res.json({
      accessToken,
      refreshToken: newRefreshToken
    });
  } catch (error) {
    console.error('Token yenileme hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Kullanıcı çıkışı
 * POST /api/auth/logout
 */
router.post('/logout', authenticate, async (req, res) => {
  try {
    // Kullanıcı bilgilerini al
    const user = await userModel.getUserById(req.user.id);

    if (!user) {
      return res.status(404).json({ error: 'Kullanıcı bulunamadı' });
    }

    // Refresh token'ı geçersiz kıl
    await invalidateRefreshToken(req.user.id);

    // Çıkış bildirimi oluştur
    await notificationService.createUserLogoutNotification(user);

    res.json({ message: 'Başarıyla çıkış yapıldı' });
  } catch (error) {
    console.error('Kullanıcı çıkışı hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Mevcut kullanıcı bilgilerini getir
 * GET /api/auth/me
 */
router.get('/me', authenticate, async (req, res) => {
  try {
    const user = await userModel.getUserById(req.user.id);

    if (!user) {
      return res.status(404).json({ error: 'Kullanıcı bulunamadı' });
    }

    res.json(user);
  } catch (error) {
    console.error('Kullanıcı bilgileri hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Şifre sıfırlama talebi
 * POST /api/auth/forgot-password
 */
router.post('/forgot-password', [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Geçerli bir e-posta adresi giriniz')
    .normalizeEmail()
], async (req, res) => {
  try {
    // Validasyon hatalarını kontrol et
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email } = req.body;

    // Rate limiting kontrolü
    const canRequest = await canRequestPasswordReset(email);
    if (!canRequest) {
      return res.status(429).json({
        error: 'Çok sık şifre sıfırlama talebi yapıyorsunuz. Lütfen 5 dakika bekleyin.'
      });
    }

    // Kullanıcıyı e-posta ile bul
    const user = await userModel.getUserByEmail(email);

    if (!user) {
      // Güvenlik için kullanıcı bulunamasa bile başarılı mesaj döndür
      return res.json({
        message: 'Eğer bu e-posta adresi sistemde kayıtlıysa, şifre sıfırlama bağlantısı gönderilecektir.'
      });
    }

    // Kullanıcının mevcut token'larını temizle
    await clearUserPasswordResetTokens(user.id);

    // Yeni token oluştur
    const resetToken = await generatePasswordResetToken(user.id, email);

    // Base URL'i oluştur
    const protocol = req.headers['x-forwarded-proto'] || req.protocol;
    const host = req.headers['x-forwarded-host'] || req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // E-posta gönder
    const emailSent = await emailService.sendPasswordResetEmail(email, resetToken, baseUrl);

    if (!emailSent) {
      console.error('Şifre sıfırlama e-postası gönderilemedi:', email);
      return res.status(500).json({
        error: 'E-posta gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.'
      });
    }

    // Güvenlik bildirimi oluştur
    await notificationService.createSecurityNotification(
      'Şifre Sıfırlama Talebi',
      `${user.username} kullanıcısı için şifre sıfırlama talebi oluşturuldu.`,
      'info',
      user.id,
      {
        username: user.username,
        email: email,
        action: 'password_reset_request',
        timestamp: Date.now()
      }
    );

    res.json({
      message: 'Şifre sıfırlama bağlantısı e-posta adresinize gönderildi.'
    });

  } catch (error) {
    console.error('Şifre sıfırlama talebi hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Token doğrulama
 * GET /api/auth/verify-reset-token/:token
 */
router.get('/verify-reset-token/:token', async (req, res) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).json({ error: 'Token gerekli' });
    }

    // Token'ı doğrula
    const tokenData = await verifyPasswordResetToken(token);

    if (!tokenData) {
      return res.status(400).json({
        error: 'Geçersiz veya süresi dolmuş token',
        code: 'INVALID_TOKEN'
      });
    }

    // Kullanıcı bilgilerini al
    const user = await userModel.getUserById(tokenData.userId);

    if (!user) {
      return res.status(404).json({
        error: 'Kullanıcı bulunamadı',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      valid: true,
      email: tokenData.email,
      username: user.username
    });

  } catch (error) {
    console.error('Token doğrulama hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Şifre sıfırlama
 * POST /api/auth/reset-password
 */
router.post('/reset-password', [
  body('token')
    .notEmpty()
    .withMessage('Token gerekli'),

  body('password')
    .notEmpty()
    .withMessage('Şifre gerekli')
], async (req, res) => {
  try {
    // Validasyon hatalarını kontrol et
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { token, password } = req.body;

    // Token'ı doğrula
    const tokenData = await verifyPasswordResetToken(token);

    if (!tokenData) {
      return res.status(400).json({
        error: 'Geçersiz veya süresi dolmuş token',
        code: 'INVALID_TOKEN'
      });
    }

    // Şifre politikasını kontrol et
    const passwordValidation = await validatePasswordWithSettings(password);

    if (!passwordValidation.valid) {
      return res.status(400).json({ error: passwordValidation.message });
    }

    // Kullanıcı bilgilerini al
    const user = await userModel.getUserById(tokenData.userId);

    if (!user) {
      return res.status(404).json({
        error: 'Kullanıcı bulunamadı',
        code: 'USER_NOT_FOUND'
      });
    }

    // Şifreyi sıfırla
    await userModel.resetPassword(tokenData.userId, password);

    // Token'ı kullanılmış olarak işaretle
    await markTokenAsUsed(token);

    // Kullanıcının diğer token'larını temizle
    await clearUserPasswordResetTokens(tokenData.userId);

    // Güvenlik bildirimi oluştur
    await notificationService.createSecurityNotification(
      'Şifre Başarıyla Sıfırlandı',
      `${user.username} kullanıcısının şifresi başarıyla sıfırlandı.`,
      'info',
      user.id,
      {
        username: user.username,
        email: tokenData.email,
        action: 'password_reset_completed',
        timestamp: Date.now()
      }
    );

    res.json({
      message: 'Şifreniz başarıyla sıfırlandı. Artık yeni şifrenizle giriş yapabilirsiniz.'
    });

  } catch (error) {
    console.error('Şifre sıfırlama hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

module.exports = router;
