/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> yö<PERSON>imi rotaları
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const userModel = require('../models/user');
const notificationService = require('../services/notificationService');
const { authenticate, isAdmin } = require('../middleware/auth');
const { validatePasswordWithSettings } = require('../utils/passwordPolicy');
const { getDaysUntilPasswordExpiry, isPasswordExpired, isPasswordExpiringSoon } = require('../utils/passwordExpiry');
const settingsService = require('../services/settingsService');

const router = express.Router();

/**
 * Tüm kullanıcıları getir (sadece admin)
 * GET /api/users
 */
router.get('/', authenticate, isAdmin, async (req, res) => {
  try {
    const users = await userModel.getAllUsers();

    // Ayarları bir kez al (performans için)
    const settings = await settingsService.getSettings();

    // Her kullanıcı için şifre geçerlilik bilgilerini ekle
    const usersWithPasswordInfo = await Promise.all(
      users.map(async (user) => {
        try {
          const daysUntilExpiry = await getDaysUntilPasswordExpiry(user, settings);
          const isExpired = await isPasswordExpired(user, settings);
          const isExpiringSoon = await isPasswordExpiringSoon(user, 7, settings);

          const passwordExpiry = {
            daysUntilExpiry: daysUntilExpiry === Infinity ? null : daysUntilExpiry,
            isExpired,
            isExpiringSoon,
            passwordChangedAt: user.passwordChangedAt,
            expiryDate: daysUntilExpiry === Infinity ? null :
                       new Date(parseInt(user.passwordChangedAt || Date.now()) +
                       (parseInt(settings.passwordExpiryDays) * 24 * 60 * 60 * 1000)).toISOString()
          };

          return {
            ...user,
            passwordExpiry
          };
        } catch (error) {
          console.error(`Kullanıcı ${user.id} için şifre bilgisi hesaplanırken hata:`, error);
          return {
            ...user,
            passwordExpiry: {
              daysUntilExpiry: Infinity,
              isExpired: false,
              isExpiringSoon: false,
              passwordChangedAt: user.passwordChangedAt,
              expiryDate: null
            }
          };
        }
      })
    );

    res.json(usersWithPasswordInfo);
  } catch (error) {
    console.error('Kullanıcıları getirme hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Kullanıcı detaylarını getir
 * GET /api/users/:id
 */
router.get('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // Sadece kendi bilgilerini veya admin ise herhangi bir kullanıcının bilgilerini görebilir
    if (req.user.id !== id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Bu işlem için yetkiniz yok' });
    }

    const user = await userModel.getUserById(id);

    if (!user) {
      return res.status(404).json({ error: 'Kullanıcı bulunamadı' });
    }

    res.json(user);
  } catch (error) {
    console.error('Kullanıcı detayları hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Kullanıcı güncelle
 * PUT /api/users/:id
 */
router.put('/:id', [
  authenticate,
  // Validasyon kuralları
  body('username')
    .optional()
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Kullanıcı adı 3-30 karakter arasında olmalıdır')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Kullanıcı adı sadece harf, rakam ve alt çizgi içerebilir'),

  body('email')
    .optional()
    .trim()
    .isEmail()
    .withMessage('Geçerli bir e-posta adresi giriniz')
    .normalizeEmail(),

  body('role')
    .optional()
    .isIn(['admin', 'user'])
    .withMessage('Geçersiz rol')
], async (req, res) => {
  try {
    const { id } = req.params;

    // Sadece kendi bilgilerini veya admin ise herhangi bir kullanıcının bilgilerini güncelleyebilir
    if (req.user.id !== id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Bu işlem için yetkiniz yok' });
    }

    // Validasyon hatalarını kontrol et
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Admin olmayan kullanıcılar rol değiştiremez
    if (req.body.role && req.user.role !== 'admin') {
      delete req.body.role;
    }

    // Şifre güncelleme bu endpoint'ten yapılmaz
    if (req.body.password) {
      delete req.body.password;
    }

    const updatedUser = await userModel.updateUser(id, req.body);

    // Kullanıcı güncellendi bildirimi oluştur
    await notificationService.createUserUpdatedNotification(updatedUser, req.body);

    res.json(updatedUser);
  } catch (error) {
    console.error('Kullanıcı güncelleme hatası:', error);

    if (error.message.includes('zaten kullanılıyor')) {
      return res.status(400).json({ error: error.message });
    }

    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Kullanıcı sil
 * DELETE /api/users/:id
 */
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const { id } = req.params;

    // Sadece kendi hesabını veya admin ise herhangi bir kullanıcıyı silebilir
    if (req.user.id !== id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Bu işlem için yetkiniz yok' });
    }

    // Kullanıcı bilgilerini al (silmeden önce)
    const user = await userModel.getUserById(id);

    // Kullanıcıyı sil
    await userModel.deleteUser(id);

    // Kullanıcı silindi bildirimi oluştur
    await notificationService.createUserDeletedNotification(user);

    res.json({ message: 'Kullanıcı başarıyla silindi' });
  } catch (error) {
    console.error('Kullanıcı silme hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Kullanıcı kilidini kaldır
 * POST /api/users/:id/unlock
 */
router.post('/:id/unlock', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Kullanıcı var mı kontrol et
    const user = await userModel.getUserById(id);
    if (!user) {
      return res.status(404).json({ error: 'Kullanıcı bulunamadı' });
    }

    // Kullanıcının kilidini kaldır
    await userModel.unlockUser(id);

    // Bildirim oluştur
    await notificationService.createSecurityNotification(
      'Kullanıcı Kilidi Kaldırıldı',
      `${user.username} kullanıcısının hesap kilidi yönetici tarafından kaldırıldı.`,
      'info',
      user.id,
      {
        username: user.username,
        adminId: req.user.id,
        adminUsername: req.user.username,
        action: 'unlock_user',
        timestamp: Date.now()
      }
    );

    res.json({ message: 'Kullanıcı kilidi başarıyla kaldırıldı' });
  } catch (error) {
    console.error('Kullanıcı kilit kaldırma hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Kullanıcıyı devre dışı bırak
 * POST /api/users/:id/disable
 */
router.post('/:id/disable', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Kullanıcı var mı kontrol et
    const user = await userModel.getUserById(id);
    if (!user) {
      return res.status(404).json({ error: 'Kullanıcı bulunamadı' });
    }

    // Kullanıcıyı devre dışı bırak
    await userModel.disableUser(id);

    // Kullanıcının aktif oturumlarını sonlandır
    const socketService = require('../services/socketService');
    socketService.forceUserLogout(id, 'Hesabınız yönetici tarafından devre dışı bırakıldı. Lütfen sistem yöneticisi ile iletişime geçin.');

    // Kullanıcının refresh token'ını geçersiz kıl
    const { invalidateRefreshToken } = require('../utils/jwt');
    await invalidateRefreshToken(id);

    // Bildirim oluştur
    await notificationService.createSecurityNotification(
      'Kullanıcı Devre Dışı Bırakıldı',
      `${user.username} kullanıcısı yönetici tarafından devre dışı bırakıldı.`,
      'warning',
      user.id,
      {
        username: user.username,
        adminId: req.user.id,
        adminUsername: req.user.username,
        action: 'disable_user',
        timestamp: Date.now()
      }
    );

    res.json({ message: 'Kullanıcı başarıyla devre dışı bırakıldı ve aktif oturumları sonlandırıldı' });
  } catch (error) {
    console.error('Kullanıcı devre dışı bırakma hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Kullanıcıyı aktif et
 * POST /api/users/:id/enable
 */
router.post('/:id/enable', authenticate, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Kullanıcı var mı kontrol et
    const user = await userModel.getUserById(id);
    if (!user) {
      return res.status(404).json({ error: 'Kullanıcı bulunamadı' });
    }

    // Kullanıcıyı aktif et
    await userModel.enableUser(id);

    // Kullanıcının eski refresh token'larını temizle (güvenlik için)
    const { invalidateRefreshToken } = require('../utils/jwt');
    await invalidateRefreshToken(id);

    // Bildirim oluştur
    await notificationService.createSecurityNotification(
      'Kullanıcı Aktif Edildi',
      `${user.username} kullanıcısı yönetici tarafından aktif edildi.`,
      'info',
      user.id,
      {
        username: user.username,
        adminId: req.user.id,
        adminUsername: req.user.username,
        action: 'enable_user',
        timestamp: Date.now()
      }
    );

    res.json({ message: 'Kullanıcı başarıyla aktif edildi' });
  } catch (error) {
    console.error('Kullanıcı aktif etme hatası:', error);
    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

/**
 * Şifre değiştir
 * POST /api/users/change-password
 */
router.post('/change-password', [
  authenticate,
  // Validasyon kuralları
  body('currentPassword')
    .notEmpty()
    .withMessage('Mevcut şifre gerekli'),

  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('Yeni şifre en az 6 karakter olmalıdır')
], async (req, res) => {
  try {
    // Validasyon hatalarını kontrol et
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { currentPassword, newPassword } = req.body;

    // Şifre politikasını kontrol et
    const passwordValidation = await validatePasswordWithSettings(newPassword);

    if (!passwordValidation.valid) {
      return res.status(400).json({ error: passwordValidation.message });
    }

    await userModel.changePassword(req.user.id, currentPassword, newPassword);

    // Kullanıcı bilgilerini al
    const user = await userModel.getUserById(req.user.id);

    // Şifre değiştirildi bildirimi oluştur
    await notificationService.createPasswordChangedNotification(user);

    res.json({ message: 'Şifre başarıyla değiştirildi' });
  } catch (error) {
    console.error('Şifre değiştirme hatası:', error);

    if (error.message === 'Mevcut şifre yanlış') {
      return res.status(400).json({ error: error.message });
    }

    res.status(500).json({ error: 'Sunucu hatası' });
  }
});

module.exports = router;
