const express = require('express');
const router = express.Router();
const redisClient = require('../services/redis');
const notificationService = require('../services/notificationService');
const { authenticateToken } = require('../middleware/auth');

/**
 * Tüm uyarıları getirir (yeni bildirim sistemine yönlendirir)
 * GET /api/alerts
 */
router.get('/', async (req, res) => {
  try {
    // Yeni bildirim sistemine yönlendir
    const notifications = await notificationService.getNotifications({
      type: 'device',
      category: 'status',
      limit: 50,
      offset: 0
    });
    res.json(notifications);
  } catch (error) {
    console.error('Error getting alerts:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın uyarılarını getirir (yeni bildirim sistemine yönlendirir)
 * GET /api/alerts/device/:id
 */
router.get('/device/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Yeni bildirim sistemine yönlendir
    const notifications = await notificationService.getNotifications({
      type: 'device',
      category: 'status',
      source: { type: 'device', id },
      limit: 50,
      offset: 0
    });
    res.json(notifications);
  } catch (error) {
    console.error('Error getting device alerts:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir uyarıyı onaylar (yeni bildirim sistemine yönlendirir)
 * POST /api/alerts/acknowledge/:deviceId/:alertId
 */
router.post('/acknowledge/:deviceId/:alertId', async (req, res) => {
  try {
    const { deviceId, alertId } = req.params;
    const { userId, username } = req.body;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', deviceId);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Yeni bildirim sistemine yönlendir
    const result = await notificationService.acknowledgeNotification(alertId, { userId, username });

    if (result) {
      // Socket.io ile gerçek zamanlı güncelleme gönder
      const io = req.app.get('io');
      if (io) {
        io.emit('notification:update', { id: alertId, status: 'acknowledged' });
      }

      res.json({ success: true });
    } else {
      res.status(404).json({ error: 'Notification not found' });
    }
  } catch (error) {
    console.error('Error acknowledging notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bildirim yapılandırmasını getirir
 * GET /api/alerts/config
 */
router.get('/config', async (req, res) => {
  try {
    const config = await redisClient.hgetall('notification:config');

    if (!config) {
      return res.json({
        email_enabled: false,
        email_recipients: '',
        webhook_enabled: false,
        webhook_url: ''
      });
    }

    res.json({
      email_enabled: config.email_enabled === 'true',
      email_recipients: config.email_recipients || '',
      webhook_enabled: config.webhook_enabled === 'true',
      webhook_url: config.webhook_url || ''
    });
  } catch (error) {
    console.error('Error getting notification config:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bildirim yapılandırmasını günceller
 * PUT /api/alerts/config
 */
router.put('/config', async (req, res) => {
  try {
    const { email_enabled, email_recipients, webhook_enabled, webhook_url } = req.body;

    // Yapılandırmayı güncelle
    await redisClient.hmset('notification:config', {
      email_enabled: email_enabled ? 'true' : 'false',
      email_recipients: email_recipients || '',
      webhook_enabled: webhook_enabled ? 'true' : 'false',
      webhook_url: webhook_url || ''
    });

    res.json({
      email_enabled: email_enabled || false,
      email_recipients: email_recipients || '',
      webhook_enabled: webhook_enabled || false,
      webhook_url: webhook_url || ''
    });
  } catch (error) {
    console.error('Error updating notification config:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Test cihaz bildirimi gönderir
 * POST /api/alerts/test
 */
router.post('/test', async (req, res) => {
  try {
    const { type } = req.body;

    // Tüm cihazları al
    const deviceService = require('../services/deviceService');
    const devices = await deviceService.getAllDevices();

    // Rastgele bir cihaz seç
    const randomDevice = devices[Math.floor(Math.random() * devices.length)];

    if (!randomDevice) {
      return res.status(404).json({ error: 'No devices found' });
    }

    // Cihaz için izleme durumunu al
    const monitorTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl', 'database', 'api', 'snmp'];
    const enabledMonitors = [];

    for (const monitorType of monitorTypes) {
      if (randomDevice.monitors && randomDevice.monitors[monitorType] && randomDevice.monitors[monitorType].enabled) {
        enabledMonitors.push(monitorType);
      }
    }

    if (enabledMonitors.length === 0) {
      return res.status(404).json({ error: 'No enabled monitors found for selected device' });
    }

    // Rastgele bir izleme türü seç
    const randomMonitorType = enabledMonitors[Math.floor(Math.random() * enabledMonitors.length)];

    // Bildirim oluşturmak için durum oluştur
    const status = {
      status: 'down', // Bildirim oluşturmak için 'down' durumu kullan
      responseTime: Math.floor(Math.random() * 2000),
      error: 'Connection timeout'
    };

    // Yeni bildirim sistemini kullanarak bildirim oluştur
    const notification = await notificationService.createDeviceStatusNotification(
      randomDevice.id,
      randomDevice.name,
      randomMonitorType,
      status
    );

    res.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('Error sending test notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Onaylanmamış bildirim sayısını getir
 * GET /api/alerts/unacknowledged/count
 */
router.get('/unacknowledged/count', async (req, res) => {
  try {
    // Yeni bildirim sistemini kullanarak onaylanmamış bildirim sayısını getir
    const count = await notificationService.getNotificationCount({
      type: 'device',
      category: 'status',
      status: 'new'
    });
    res.json({ count });
  } catch (error) {
    console.error('Onaylanmamış bildirim sayısı getirilirken hata:', error);
    res.status(500).json({ error: 'Onaylanmamış bildirim sayısı getirilirken bir hata oluştu' });
  }
});

/**
 * Test sistem bildirimi gönderir
 * POST /api/alerts/test-system-notification
 */
router.post('/test-system-notification', async (req, res) => {
  try {
    const { type } = req.body;

    // Yeni bildirim sistemini kullanarak sistem bildirimi oluştur
    const notification = await notificationService.createSystemNotification({
      title: 'Test Sistem Bildirimi',
      message: 'Bu bir test sistem bildirimidir.',
      severity: type || 'info',
      category: 'system'
    });

    res.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('Test sistem bildirimi gönderilirken hata:', error);
    res.status(500).json({ error: 'Test sistem bildirimi gönderilirken bir hata oluştu' });
  }
});

/**
 * Çeşitli test bildirimleri gönderir
 * POST /api/alerts/test-all-notifications
 */
router.post('/test-all-notifications', async (req, res) => {
  try {
    // Sistem bildirimleri
    const systemNotifications = [
      {
        title: 'Sistem Güncellendi',
        message: 'Sistem başarıyla en son sürüme güncellendi.',
        severity: 'info',
        category: 'system'
      },
      {
        title: 'Veritabanı Yedeği',
        message: 'Günlük veritabanı yedeği başarıyla oluşturuldu.',
        severity: 'info',
        category: 'maintenance'
      },
      {
        title: 'Disk Alanı Uyarısı',
        message: 'Sunucu disk alanı %85 doluluk oranına ulaştı.',
        severity: 'warning',
        category: 'performance'
      }
    ];

    // Tüm cihazları al
    const deviceService = require('../services/deviceService');
    const devices = await deviceService.getAllDevices();

    // Oluşturulan bildirimler için boş dizi
    const createdNotifications = [];

    // Sistem bildirimlerini oluştur
    for (const notification of systemNotifications) {
      const createdNotification = await notificationService.createSystemNotification(notification);
      createdNotifications.push(createdNotification);

      // Bildirimlerin sırayla görünmesi için kısa bir bekleme
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // En fazla 4 cihaz için bildirim oluştur
    const maxDevices = Math.min(4, devices.length);

    for (let i = 0; i < maxDevices; i++) {
      const device = devices[i];

      // Farklı izleme türleri için bildirimler oluştur
      const monitorTypes = ['icmp', 'http', 'tcp', 'dns'];
      const monitorType = monitorTypes[i % monitorTypes.length];

      // Bildirim oluşturmak için durum oluştur
      const status = {
        status: 'down',
        responseTime: Math.floor(Math.random() * 2000),
        error: 'Connection timeout'
      };

      // Cihaz durumu bildirimi oluştur
      const deviceNotification = await notificationService.createDeviceStatusNotification(
        device.id,
        device.name,
        monitorType,
        status
      );

      createdNotifications.push(deviceNotification);

      // Bildirimlerin sırayla görünmesi için kısa bir bekleme
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    res.json({
      success: true,
      message: 'Tüm test bildirimleri gönderildi',
      count: createdNotifications.length,
      notifications: createdNotifications
    });
  } catch (error) {
    console.error('Test bildirimleri gönderilirken hata:', error);
    res.status(500).json({ error: 'Test bildirimleri gönderilirken bir hata oluştu' });
  }
});

module.exports = router;
