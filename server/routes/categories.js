const express = require('express');
const router = express.Router();

/**
 * <PERSON><PERSON>i yapısı - Lean monitoring yaklaşımına uygun
 * 4 ana kategori + 18 alt kategori
 */
const DEVICE_CATEGORIES = {
  "Ağ Cihazları": {
    icon: "Network",
    color: "blue",
    subcategories: {
      "Router": {
        icon: "Router",
        label: "Router'lar",
        description: "Ağ yönlendirici cihazları"
      },
      "Switch": {
        icon: "Share2", 
        label: "Switch'ler",
        description: "Ağ anahtarlama cihazları"
      },
      "Firewall": {
        icon: "Shield",
        label: "Firewall'lar", 
        description: "Güvenlik duvarı cihazları"
      },
      "AccessPoint": {
        icon: "Wifi",
        label: "Access Point'ler",
        description: "Kablosuz erişim noktaları"
      },
      "Modem": {
        icon: "Radio",
        label: "Modem'ler",
        description: "İnternet bağlantı cihazları"
      }
    }
  },
  "Sunucular": {
    icon: "Server",
    color: "green", 
    subcategories: {
      "Fiziksel": {
        icon: "ServerCrash",
        label: "Fiziksel Sunucular",
        description: "Donanım tabanlı sunucular",
        needsPlatform: true
      },
      "Sanal": {
        icon: "Cloud",
        label: "Sanal Sunucular", 
        description: "Sanallaştırılmış sunucular",
        needsPlatform: true
      },
      "Container": {
        icon: "Package",
        label: "Container'lar",
        description: "Konteyner tabanlı uygulamalar"
      },
      "Veritabanı": {
        icon: "Database",
        label: "Veri Tabanı Sunucuları",
        description: "Veritabanı sunucu sistemleri"
      },
      "Depolama": {
        icon: "HardDrive", 
        label: "Depolama Cihazları",
        description: "Veri depolama sistemleri"
      }
    }
  },
  "Web": {
    icon: "Globe",
    color: "purple",
    subcategories: {
      "WebServer": {
        icon: "Globe2",
        label: "Web Sunucuları",
        description: "HTTP/HTTPS web sunucuları",
        needsPlatform: true
      },
      "API": {
        icon: "Webhook", 
        label: "API'ler",
        description: "REST/GraphQL API servisleri",
        needsPlatform: true
      },
      "Mail": {
        icon: "Mail",
        label: "Mail Sunucuları", 
        description: "E-posta sunucu sistemleri",
        needsPlatform: true
      },
      "CDN": {
        icon: "Network",
        label: "CDN'ler",
        description: "İçerik dağıtım ağları"
      },
      "DNS": {
        icon: "Search",
        label: "DNS Sunucuları",
        description: "Domain name sunucuları"
      }
    }
  },
  "IoT": {
    icon: "Cpu",
    color: "orange",
    subcategories: {
      "Sensör": {
        icon: "Activity",
        label: "Akıllı Sensörler", 
        description: "IoT sensör cihazları"
      },
      "PLC": {
        icon: "Cog",
        label: "Endüstriyel PLC'ler",
        description: "Programlanabilir mantık kontrolörleri"
      },
      "BinaSistemi": {
        icon: "Building2",
        label: "Akıllı Bina Sistemleri",
        description: "Bina otomasyon sistemleri"
      },
      "UPS": {
        icon: "Battery",
        label: "UPS & Güç Yönetim Cihazları", 
        description: "Kesintisiz güç kaynakları"
      }
    }
  }
};

/**
 * Platform seçenekleri
 */
const PLATFORM_OPTIONS = [
  {
    value: 'windows',
    label: 'Windows Server',
    icon: 'Monitor'
  },
  {
    value: 'linux', 
    label: 'Linux Server',
    icon: 'Terminal'
  }
];

/**
 * Tüm cihaz kategorilerini getirir
 * GET /api/categories
 */
router.get('/', async (req, res) => {
  try {
    res.json({
      categories: DEVICE_CATEGORIES,
      platforms: PLATFORM_OPTIONS,
      metadata: {
        totalMainCategories: Object.keys(DEVICE_CATEGORIES).length,
        totalSubcategories: Object.values(DEVICE_CATEGORIES).reduce(
          (total, category) => total + Object.keys(category.subcategories).length, 
          0
        ),
        version: '1.0.0',
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error getting categories:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Belirli bir ana kategorinin alt kategorilerini getirir
 * GET /api/categories/:mainCategory
 */
router.get('/:mainCategory', async (req, res) => {
  try {
    const { mainCategory } = req.params;
    
    if (!DEVICE_CATEGORIES[mainCategory]) {
      return res.status(404).json({ error: 'Main category not found' });
    }
    
    res.json({
      mainCategory,
      ...DEVICE_CATEGORIES[mainCategory],
      metadata: {
        subcategoryCount: Object.keys(DEVICE_CATEGORIES[mainCategory].subcategories).length
      }
    });
  } catch (error) {
    console.error('Error getting category details:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Platform seçimi gereken kategorileri getirir
 * GET /api/categories/platform/required
 */
router.get('/platform/required', async (req, res) => {
  try {
    const platformRequiredCategories = [];
    
    Object.entries(DEVICE_CATEGORIES).forEach(([mainCategory, categoryData]) => {
      Object.entries(categoryData.subcategories).forEach(([subCategory, subData]) => {
        if (subData.needsPlatform) {
          platformRequiredCategories.push(`${mainCategory}/${subCategory}`);
        }
      });
    });
    
    res.json({
      categories: platformRequiredCategories,
      platforms: PLATFORM_OPTIONS
    });
  } catch (error) {
    console.error('Error getting platform required categories:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
