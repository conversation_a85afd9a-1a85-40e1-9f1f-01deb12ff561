const express = require('express');
const router = express.Router();
const redisClient = require('../config/redis');
const { v4: uuidv4 } = require('uuid');
const { validateAndSetDefaultMonitorSettings } = require('../utils/monitorUtils');
const deviceService = require('../services/deviceService');

/**
 * Tüm cihazları getirir
 * GET /api/devices
 */
router.get('/', async (req, res) => {
  try {
    // Tüm cihaz ID'lerini al
    const deviceIds = await redisClient.smembers('devices');

    // Her cihazın detaylarını al
    const devices = [];
    for (const id of deviceIds) {
      const deviceData = await redisClient.hgetall(`device:${id}`);
      if (deviceData) {
        // Monitors alanını JSON'dan parse et
        if (deviceData.monitors) {
          try {
            deviceData.monitors = JSON.parse(deviceData.monitors);
          } catch (e) {
            deviceData.monitors = {};
          }
        } else {
          deviceData.monitors = {};
        }

        // Alerts alanını JSON'dan parse et
        if (deviceData.alerts) {
          try {
            deviceData.alerts = JSON.parse(deviceData.alerts);
          } catch (e) {
            deviceData.alerts = [];
          }
        } else {
          deviceData.alerts = [];
        }

        devices.push({
          id,
          ...deviceData
        });
      }
    }

    res.json(devices);
  } catch (error) {
    console.error('Error getting devices:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Belirli bir cihazı getirir
 * GET /api/devices/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Cihaz detaylarını al
    const deviceData = await redisClient.hgetall(`device:${id}`);
    if (!deviceData) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Monitors alanını JSON'dan parse et
    if (deviceData.monitors) {
      try {
        deviceData.monitors = JSON.parse(deviceData.monitors);
      } catch (e) {
        deviceData.monitors = {};
      }
    } else {
      deviceData.monitors = {};
    }

    // Alerts alanını JSON'dan parse et
    if (deviceData.alerts) {
      try {
        deviceData.alerts = JSON.parse(deviceData.alerts);
      } catch (e) {
        deviceData.alerts = [];
      }
    } else {
      deviceData.alerts = [];
    }

    res.json({
      id,
      ...deviceData
    });
  } catch (error) {
    console.error('Error getting device:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Yeni bir cihaz ekler
 * POST /api/devices
 */
router.post('/', async (req, res) => {
  try {
    const { name, host, description, group, location, platform, monitors, alerts } = req.body;

    // Gerekli alanları kontrol et
    if (!name || !host) {
      return res.status(400).json({ error: 'Name and host are required' });
    }

    // Yeni cihaz ID'si oluştur
    const id = uuidv4();

    // Kategori zorunlu kontrolü
    if (!group || group.trim() === '') {
      return res.status(400).json({ error: 'Category selection is required. Please select a category.' });
    }

    // Cihaz verilerini hazırla
    const deviceData = {
      name,
      host,
      description: description || '',
      group: group,
      location: location || '',
      platform: platform || null,
      monitors: monitors || {},
      alerts: alerts || []
    };

    // İzleyici ayarlarını doğrula ve varsayılan değerleri ayarla
    validateAndSetDefaultMonitorSettings(deviceData);

    // Cihazı Redis'e kaydet
    await redisClient.hmset(`device:${id}`, {
      name: deviceData.name,
      host: deviceData.host,
      description: deviceData.description,
      group: deviceData.group,
      location: deviceData.location,
      platform: deviceData.platform,
      monitors: JSON.stringify(deviceData.monitors),
      alerts: JSON.stringify(deviceData.alerts),
      createdAt: Date.now()
    });

    // Konum bilgisini ayrıca kaydet
    if (deviceData.location) {
      await redisClient.hset(`device:${id}`, 'location', deviceData.location);
    }

    // Cihaz ID'sini cihazlar kümesine ekle
    await redisClient.sadd('devices', id);

    // Cihaz grubunu gruplar kümesine ekle
    if (deviceData.group) {
      await redisClient.sadd('device:groups', deviceData.group);
    }

    // Socket.io ile gerçek zamanlı güncelleme gönder
    const io = req.app.get('io');
    if (io) {
      io.emit('device:new', {
        id,
        ...deviceData,
        createdAt: Date.now()
      });
    }

    res.status(201).json({
      id,
      ...deviceData,
      createdAt: Date.now()
    });
  } catch (error) {
    console.error('Error creating device:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazı günceller
 * PUT /api/devices/:id
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, host, description, group, location, platform, monitors, alerts } = req.body;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Gerekli alanları kontrol et
    if (!name || !host) {
      return res.status(400).json({ error: 'Name and host are required' });
    }

    // Kategori zorunlu kontrolü
    if (!group || group.trim() === '') {
      return res.status(400).json({ error: 'Category selection is required. Please select a category.' });
    }

    // Mevcut cihaz bilgilerini al
    const existingDevice = await redisClient.hgetall(`device:${id}`);

    // Cihaz verilerini hazırla
    const deviceData = {
      name,
      host,
      description: description || '',
      group: group,
      location: location || '',
      platform: platform || null,
      monitors: monitors || {},
      alerts: alerts || []
    };

    // İzleyici ayarlarını doğrula ve varsayılan değerleri ayarla
    validateAndSetDefaultMonitorSettings(deviceData);

    // Cihazı güncelle
    await redisClient.hmset(`device:${id}`, {
      name: deviceData.name,
      host: deviceData.host,
      description: deviceData.description,
      group: deviceData.group,
      location: deviceData.location,
      platform: deviceData.platform,
      monitors: JSON.stringify(deviceData.monitors),
      alerts: JSON.stringify(deviceData.alerts),
      updatedAt: Date.now()
    });

    // Konum bilgisini ayrıca kaydet
    if (deviceData.location) {
      await redisClient.hset(`device:${id}`, 'location', deviceData.location);
    }

    // Cihaz grubunu gruplar kümesine ekle
    if (deviceData.group) {
      await redisClient.sadd('device:groups', deviceData.group);
    }

    // Eski grup silinebilir mi kontrol et
    if (existingDevice.group && existingDevice.group !== group) {
      // Grupta başka cihaz var mı kontrol et
      const groupDevices = await redisClient.smembers('devices');
      let groupHasOtherDevices = false;

      for (const deviceId of groupDevices) {
        if (deviceId !== id) {
          const device = await redisClient.hgetall(`device:${deviceId}`);
          if (device && device.group === existingDevice.group) {
            groupHasOtherDevices = true;
            break;
          }
        }
      }

      // Grupta başka cihaz yoksa grubu sil
      if (!groupHasOtherDevices) {
        await redisClient.srem('device:groups', existingDevice.group);
      }
    }

    // Socket.io ile gerçek zamanlı güncelleme gönder
    const io = req.app.get('io');
    if (io) {
      io.emit('device:update', {
        device: {
          id,
          ...deviceData,
          updatedAt: Date.now()
        }
      });
    }

    res.json({
      id,
      ...deviceData,
      updatedAt: Date.now()
    });
  } catch (error) {
    console.error('Error updating device:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazı siler
 * DELETE /api/devices/:id
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // deviceService kullanarak cihazı sil (bildirimleri de siler)
    await deviceService.deleteDevice(id);

    // Socket.io ile gerçek zamanlı güncelleme gönder
    const io = req.app.get('io');
    if (io) {
      io.emit('device:delete', { id });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting device:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın tüm durumlarını getirir
 * GET /api/devices/:id/status
 */
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Cihaz detaylarını al
    const deviceData = await redisClient.hgetall(`device:${id}`);
    if (!deviceData) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Monitors alanını JSON'dan parse et
    let monitors = {};
    if (deviceData.monitors) {
      try {
        monitors = JSON.parse(deviceData.monitors);
      } catch (e) {
        monitors = {};
      }
    }

    // Tüm izleme verilerini al
    const statuses = {};

    // ICMP durumunu al
    const icmpData = await redisClient.hgetall(`monitor:icmp:${id}`);
    if (icmpData) {
      if (icmpData.details) {
        try {
          icmpData.details = JSON.parse(icmpData.details);
        } catch (e) {
          icmpData.details = {};
        }
      }
      statuses.icmp = icmpData;
    }

    // HTTP durumunu al
    if (monitors.http && monitors.http.enabled) {
      const httpData = await redisClient.hgetall(`monitor:http:${id}`);
      if (httpData) {
        if (httpData.details) {
          try {
            httpData.details = JSON.parse(httpData.details);
          } catch (e) {
            httpData.details = {};
          }
        }
        statuses.http = httpData;
      }
    }

    // TCP durumunu al
    if (monitors.tcp && monitors.tcp.enabled) {
      const tcpPort = monitors.tcp.port || '80';
      const tcpData = await redisClient.hgetall(`monitor:tcp:${id}:${tcpPort}`);
      if (tcpData) {
        if (tcpData.details) {
          try {
            tcpData.details = JSON.parse(tcpData.details);
          } catch (e) {
            tcpData.details = {};
          }
        }
        statuses.tcp = { [tcpPort]: tcpData };
      }
    }

    // SNMP durumunu al
    if (monitors.snmp && monitors.snmp.enabled) {
      const snmpData = await redisClient.hgetall(`monitor:snmp:${id}`);
      if (snmpData) {
        if (snmpData.details) {
          try {
            snmpData.details = JSON.parse(snmpData.details);
          } catch (e) {
            snmpData.details = {};
          }
        }
        statuses.snmp = snmpData;
      }
    }

    // DNS durumunu al
    if (monitors.dns && monitors.dns.enabled) {
      const dnsData = await redisClient.hgetall(`monitor:dns:${id}`);
      if (dnsData) {
        if (dnsData.details) {
          try {
            dnsData.details = JSON.parse(dnsData.details);
          } catch (e) {
            dnsData.details = {};
          }
        }
        statuses.dns = dnsData;
      }
    }

    // SSL durumunu al
    if (monitors.ssl && monitors.ssl.enabled) {
      const sslData = await redisClient.hgetall(`monitor:ssl:${id}`);
      if (sslData) {
        if (sslData.details) {
          try {
            sslData.details = JSON.parse(sslData.details);
          } catch (e) {
            sslData.details = {};
          }
        }
        statuses.ssl = sslData;
      }
    }

    // Veritabanı durumunu al
    if (monitors.database && monitors.database.enabled) {
      const dbData = await redisClient.hgetall(`monitor:database:${id}`);
      if (dbData) {
        if (dbData.details) {
          try {
            dbData.details = JSON.parse(dbData.details);
          } catch (e) {
            dbData.details = {};
          }
        }
        statuses.database = dbData;
      }
    }

    // API durumunu al
    if (monitors.api && monitors.api.enabled) {
      const apiData = await redisClient.hgetall(`monitor:api:${id}`);
      if (apiData) {
        if (apiData.details) {
          try {
            apiData.details = JSON.parse(apiData.details);
          } catch (e) {
            apiData.details = {};
          }
        }
        statuses.api = apiData;
      }
    }

    // SMTP durumunu al
    if (monitors.smtp && monitors.smtp.enabled) {
      const smtpData = await redisClient.hgetall(`monitor:smtp:${id}`);
      if (smtpData) {
        if (smtpData.details) {
          try {
            smtpData.details = JSON.parse(smtpData.details);
          } catch (e) {
            smtpData.details = {};
          }
        }
        statuses.smtp = smtpData;
      }
    }

    // Windows sistem durumunu al
    if (monitors.windows && monitors.windows.enabled) {
      const windowsData = await redisClient.hgetall(`monitor:windows:${id}`);
      if (windowsData) {
        if (windowsData.details) {
          try {
            windowsData.details = JSON.parse(windowsData.details);
          } catch (e) {
            windowsData.details = {};
          }
        }
        statuses.windows = windowsData;
      }
    }

    // Linux sistem durumunu al
    if (monitors.linux && monitors.linux.enabled) {
      const linuxData = await redisClient.hgetall(`monitor:linux:${id}`);
      if (linuxData) {
        if (linuxData.details) {
          try {
            linuxData.details = JSON.parse(linuxData.details);
          } catch (e) {
            linuxData.details = {};
          }
        }
        statuses.linux = linuxData;
      }
    }

    // IPMI donanım durumunu al
    if (monitors.ipmi && monitors.ipmi.enabled) {
      const ipmiData = await redisClient.hgetall(`monitor:ipmi:${id}`);
      if (ipmiData) {
        if (ipmiData.details) {
          try {
            ipmiData.details = JSON.parse(ipmiData.details);
          } catch (e) {
            ipmiData.details = {};
          }
        }
        statuses.ipmi = ipmiData;
      }
    }

    // System monitoring durumunu al
    if (monitors.system && monitors.system.enabled) {
      const systemData = await redisClient.hgetall(`monitor:system:${id}`);
      if (systemData) {
        if (systemData.details) {
          try {
            systemData.details = JSON.parse(systemData.details);
          } catch (e) {
            systemData.details = {};
          }
        }
        statuses.system = systemData;
      }
    }

    // Docker konteyner durumunu al
    if (monitors.docker && monitors.docker.enabled) {
      const dockerData = await redisClient.hgetall(`monitor:docker:${id}`);
      if (dockerData) {
        if (dockerData.details) {
          try {
            dockerData.details = JSON.parse(dockerData.details);
          } catch (e) {
            dockerData.details = {};
          }
        }
        statuses.docker = dockerData;
      }
    }

    // IPMI donanım durumunu al
    if (monitors.ipmi && monitors.ipmi.enabled) {
      const ipmiData = await redisClient.hgetall(`monitor:ipmi:${id}`);
      if (ipmiData) {
        if (ipmiData.details) {
          try {
            ipmiData.details = JSON.parse(ipmiData.details);
          } catch (e) {
            ipmiData.details = {};
          }
        }
        statuses.ipmi = ipmiData;
      }
    }

    res.json(statuses);
  } catch (error) {
    console.error('Error getting device status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Tüm cihaz gruplarını getirir
 * GET /api/devices/groups/all
 */
router.get('/groups/all', async (req, res) => {
  try {
    const groups = await redisClient.smembers('device:groups');
    res.json(groups);
  } catch (error) {
    console.error('Error getting device groups:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Birden fazla cihazın grubunu toplu olarak değiştirir
 * PUT /api/devices/bulk/group
 */
router.put('/bulk/group', async (req, res) => {
  try {
    const { deviceIds, group } = req.body;

    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
      return res.status(400).json({ error: 'Device IDs are required' });
    }

    if (!group || group.trim() === '') {
      return res.status(400).json({ error: 'Category selection is required. Please select a category.' });
    }

    const results = {};
    const io = req.app.get('io');

    // Her cihazın grubunu değiştir
    for (const id of deviceIds) {
      // Cihazın var olup olmadığını kontrol et
      const exists = await redisClient.sismember('devices', id);
      if (!exists) {
        results[id] = { error: 'Device not found' };
        continue;
      }

      // Mevcut cihaz bilgilerini al
      const existingDevice = await redisClient.hgetall(`device:${id}`);
      if (!existingDevice) {
        results[id] = { error: 'Device not found' };
        continue;
      }

      // Cihazın grubunu güncelle
      await redisClient.hset(`device:${id}`, 'group', group);

      // Konum bilgisini koru
      const location = existingDevice.location;
      if (location) {
        await redisClient.hset(`device:${id}`, 'location', location);
      }

      // Cihaz grubunu gruplar kümesine ekle
      await redisClient.sadd('device:groups', group);

      // Eski grup silinebilir mi kontrol et
      if (existingDevice.group && existingDevice.group !== group) {
        // Grupta başka cihaz var mı kontrol et
        const groupDevices = await redisClient.smembers('devices');
        let groupHasOtherDevices = false;

        for (const deviceId of groupDevices) {
          if (deviceId !== id) {
            const device = await redisClient.hgetall(`device:${deviceId}`);
            if (device && device.group === existingDevice.group) {
              groupHasOtherDevices = true;
              break;
            }
          }
        }

        // Grupta başka cihaz yoksa grubu sil
        if (!groupHasOtherDevices) {
          await redisClient.srem('device:groups', existingDevice.group);
        }
      }

      // Socket.io ile gerçek zamanlı güncelleme gönder
      if (io) {
        io.emit('device:update', {
          id,
          ...existingDevice,
          group,
          updatedAt: Date.now()
        });
      }

      results[id] = { success: true };
    }

    res.json(results);
  } catch (error) {
    console.error('Error updating device groups in bulk:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Birden fazla cihazı toplu olarak siler
 * DELETE /api/devices/bulk (eski yöntem)
 */
router.delete('/bulk', async (req, res) => {
  try {
    const { deviceIds } = req.body;

    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
      return res.status(400).json({ error: 'Device IDs are required' });
    }

    const results = {};
    const io = req.app.get('io');

    // Her cihazı sil
    for (const id of deviceIds) {
      try {
        // Cihazın var olup olmadığını kontrol et
        const exists = await redisClient.sismember('devices', id);
        if (!exists) {
          results[id] = { error: 'Device not found' };
          continue;
        }

        // deviceService kullanarak cihazı sil (bildirimleri de siler)
        await deviceService.deleteDevice(id);

        // Socket.io ile gerçek zamanlı güncelleme gönder
        if (io) {
          io.emit('device:delete', { id });
        }

        results[id] = { success: true };
      } catch (error) {
        console.error(`Error deleting device ${id}:`, error);
        results[id] = { error: 'Failed to delete device' };
      }
    }

    res.json(results);
  } catch (error) {
    console.error('Error deleting devices in bulk:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Birden fazla cihazı toplu olarak siler (POST yöntemi)
 * POST /api/devices/bulk/delete
 */
router.post('/bulk/delete', async (req, res) => {
  try {
    const { deviceIds } = req.body;

    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
      return res.status(400).json({ error: 'Device IDs are required' });
    }

    const results = {};
    const io = req.app.get('io');

    // Her cihazı sil
    for (const id of deviceIds) {
      try {
        // Cihazın var olup olmadığını kontrol et
        const exists = await redisClient.sismember('devices', id);
        if (!exists) {
          results[id] = { error: 'Device not found' };
          continue;
        }

        // deviceService kullanarak cihazı sil (bildirimleri de siler)
        await deviceService.deleteDevice(id);

        // Socket.io ile gerçek zamanlı güncelleme gönder
        if (io) {
          io.emit('device:delete', { id });
        }

        results[id] = { success: true };
      } catch (error) {
        console.error(`Error deleting device ${id}:`, error);
        results[id] = { error: 'Failed to delete device' };
      }
    }

    res.json(results);
  } catch (error) {
    console.error('Error deleting devices in bulk:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});



module.exports = router;
