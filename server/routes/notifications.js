/**
 * <PERSON><PERSON><PERSON>im sistemi API endpoint'leri
 */
const express = require('express');
const router = express.Router();
const notificationService = require('../services/notificationService');
const notificationFilterService = require('../services/notification-filter-service');
const settingsService = require('../services/settingsService');
const { authenticateToken } = require('../middleware/auth');

/**
 * Tüm bildirimleri getirir (kullanıcı ayarlarına göre filtrelenmiş)
 * GET /api/notifications
 *
 * Query parametreleri:
 * - includeCounts: true/false - <PERSON><PERSON><PERSON><PERSON> da döndür (performans optimizasyonu)
 * - count: true - <PERSON><PERSON><PERSON> sayıları döndür
 */
// Test endpoint
router.get('/test', (req, res) => {
  console.log('🧪 TEST ROUTE HIT!');
  res.json({ message: 'Test endpoint working!', timestamp: Date.now() });
});

router.get('/', async (req, res) => {
  console.log('🚀 NOTIFICATIONS ROUTE HIT!');
  console.log('🔍 Query params:', req.query);

  try {
    const {
      severity,
      status,
      source,
      limit,
      offset,
      count,
      search,
      filtered,
      includeCounts
    } = req.query;

    // ✅ Basit filtreler objesi oluştur
    const filters = {};
    if (severity && severity !== 'all') filters.severity = severity;
    if (status && status !== 'all') filters.status = status;
    if (source && source !== 'all') filters.source = { type: source };
    if (search) filters.search = search;

    // Sayfalama parametreleri
    const limitNum = parseInt(limit) || 10;
    const offsetNum = parseInt(offset) || 0;

    // Eğer sadece sayı isteniyorsa
    if (count === 'true') {
      const counts = await notificationService.getCounts(filters);
      return res.json(counts);
    }

    console.log('🔧 Backend - About to call getNotifications with:', {
      ...filters,
      limit: limitNum,
      offset: offsetNum
    });

    // Bildirimleri getir
    let notifications = await notificationService.getNotifications({
      ...filters,
      limit: limitNum,
      offset: offsetNum
    });

    console.log('📦 Backend - getNotifications returned:', notifications.length, 'items');

    // Eğer filtreleme isteniyorsa, kullanıcı ayarlarına göre filtrele
    if (filtered === 'true') {
      try {
        const userSettings = await settingsService.getAll();
        notifications = notificationFilterService.filterNotifications(notifications, userSettings);
      } catch (error) {
        console.error('Error filtering notifications:', error);
        // Filtreleme hatası durumunda tüm bildirimleri döndür
      }
    }

    console.log('🔍 Backend - includeCounts:', includeCounts);
    console.log('🔍 Backend - filters:', filters);
    console.log('🔍 Backend - notifications count:', notifications.length);

    // Eğer sayılar da isteniyorsa (performans optimizasyonu için)
    if (includeCounts === 'true') {
      console.log('✅ Backend - Getting counts with filters');
      const counts = await notificationService.getCounts(filters);
      console.log('📊 Backend - Counts result:', counts);

      const response = {
        data: notifications,
        meta: counts,
        pagination: {
          limit: limitNum,
          offset: offsetNum,
          total: counts.total,
          totalPages: Math.ceil(counts.total / limitNum)
        }
      };
      console.log('📤 Backend - Sending response:', response);
      return res.json(response);
    }

    console.log('📤 Backend - Sending notifications only');
    // Sadece bildirimleri döndür
    res.json(notifications);
  } catch (error) {
    console.error('Error getting notifications:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bildirim sayıları endpoint'i
 * GET /api/notifications/counts
 */
router.get('/counts', async (req, res) => {
  try {
    const { search, status, severity, source } = req.query;

    // ✅ Basit filtreler
    const filters = {};
    if (search) filters.search = search;
    if (status && status !== 'all') filters.status = status;
    if (severity && severity !== 'all') filters.severity = severity;
    if (source && source !== 'all') {
      filters.source = { type: source };
    }

    // Sayıları al
    const counts = await notificationService.getCounts(filters);

    res.json(counts);
  } catch (error) {
    console.error('Bildirim sayıları alınırken hata:', error);
    res.status(500).json({ error: 'Sayılar alınamadı' });
  }
});

/**
 * Belirli bir bildirimi getirir
 * GET /api/notifications/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const notification = await notificationService.getNotification(req.params.id);

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json(notification);
  } catch (error) {
    console.error(`Error getting notification ${req.params.id}:`, error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir bildirimi okundu olarak işaretler
 * POST /api/notifications/:id/read
 */
router.post('/:id/read', async (req, res) => {
  try {
    const notification = await notificationService.markNotificationAsRead(req.params.id);

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json(notification);
  } catch (error) {
    console.error(`Error marking notification ${req.params.id} as read:`, error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Tüm bildirimleri okundu olarak işaretler
 * POST /api/notifications/read-all
 */
router.post('/read-all', async (req, res) => {
  try {
    const result = await notificationService.markAllNotificationsAsRead();
    res.json({ success: true, count: result.count });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ✅ Acknowledge endpoint kaldırıldı - Artık sadece resolved kullanılıyor

/**
 * Bir bildirimi çözülmüş olarak işaretler
 * POST /api/notifications/:id/resolve
 */
router.post('/:id/resolve', async (req, res) => {
  try {
    const { userId, username, resolution } = req.body;

    if (!userId || !username) {
      return res.status(400).json({ error: 'User ID and username are required' });
    }

    const notification = await notificationService.resolveNotification(
      req.params.id,
      userId,
      username,
      resolution || ''
    );

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json(notification);
  } catch (error) {
    console.error(`Error resolving notification ${req.params.id}:`, error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Cihaz bildirimi oluşturur
 * POST /api/notifications/device
 */
router.post('/device', async (req, res) => {
  try {
    const { deviceId, deviceName, monitorType, status } = req.body;

    if (!deviceId || !deviceName || !monitorType || !status) {
      return res.status(400).json({ error: 'Device ID, name, monitor type and status are required' });
    }

    const notification = await notificationService.createDeviceStatusNotification(
      deviceId,
      deviceName,
      monitorType,
      status
    );

    if (!notification) {
      return res.status(400).json({ error: 'No notification created based on the provided status' });
    }

    res.status(201).json(notification);
  } catch (error) {
    console.error('Error creating device notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Sistem bildirimi oluşturur
 * POST /api/notifications/system
 */
router.post('/system', async (req, res) => {
  try {
    const { title, message, category, severity, sourceName, metadata, actions, link } = req.body;

    if (!title || !message) {
      return res.status(400).json({ error: 'Title and message are required' });
    }

    const notification = await notificationService.createSystemNotification({
      title,
      message,
      category,
      severity,
      sourceName,
      metadata,
      actions,
      link
    });

    res.status(201).json(notification);
  } catch (error) {
    console.error('Error creating system notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Kullanıcı bildirimi oluşturur
 * POST /api/notifications/user
 */
router.post('/user', async (req, res) => {
  try {
    const { title, message, userId, username, action, severity, metadata } = req.body;

    if (!title || !message) {
      return res.status(400).json({ error: 'Title and message are required' });
    }

    // Kullanıcı bildirimi oluştur
    const notification = await notificationService.createUserNotification({
      title,
      message,
      userId,
      username,
      action,
      severity: severity || 'info',
      metadata: metadata || {}
    });

    res.status(201).json(notification);
  } catch (error) {
    console.error('Error creating user notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Test bildirimi oluşturur
 * POST /api/notifications/test
 */
router.post('/test', async (req, res) => {
  try {
    const notification = await notificationService.createTestNotification();
    res.status(201).json(notification);
  } catch (error) {
    console.error('Error creating test notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Kategori bazlı test bildirimi oluşturur
 * POST /api/notifications/test/:category
 */
router.post('/test/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const { title, message } = req.body;

    if (!['critical', 'warning', 'info', 'success'].includes(category)) {
      return res.status(400).json({ error: 'Invalid category. Must be: critical, warning, info, success' });
    }

    const notification = notificationFilterService.createTestNotification(
      category,
      title || `Test ${category} bildirimi`,
      message || `Bu bir test ${category} bildirimidir`
    );

    // Test bildirimi oluştur
    const createdNotification = await notificationService.createNotification(notification);
    res.status(201).json(createdNotification);
  } catch (error) {
    console.error('Error creating test notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Login bildirimi test eder
 * POST /api/notifications/test/login
 */
router.post('/test/login', async (req, res) => {
  try {
    // Login bildirimi simülasyonu
    const loginNotification = {
      title: 'Kullanıcı Girişi',
      message: 'Test kullanıcısı sisteme giriş yaptı.',
      category: 'security',
      severity: 'info',
      sourceName: 'Authentication'
    };

    const createdNotification = await notificationService.createSystemNotification(loginNotification);

    // Filtreleme testi
    const userSettings = await settingsService.getAll();
    const userCategory = notificationFilterService.getNotificationUserCategory(createdNotification);
    const shouldShow = notificationFilterService.shouldShowNotification(createdNotification, userSettings);

    res.status(201).json({
      notification: createdNotification,
      debug: {
        userCategory,
        shouldShow,
        userSettings: {
          notifyOnCritical: userSettings.notifyOnCritical,
          notifyOnWarning: userSettings.notifyOnWarning,
          notifyOnInfo: userSettings.notifyOnInfo
        }
      }
    });
  } catch (error) {
    console.error('Error creating login test notification:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Tüm bildirimleri siler
 * DELETE /api/notifications/all
 */
router.delete('/all', async (req, res) => {
  try {
    const result = await notificationService.cleanupOldNotifications(0); // 0 gün = tüm bildirimler
    res.json({
      success: true,
      message: `${result.count} bildirim başarıyla silindi`,
      count: result.count
    });
  } catch (error) {
    console.error('Error deleting all notifications:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
