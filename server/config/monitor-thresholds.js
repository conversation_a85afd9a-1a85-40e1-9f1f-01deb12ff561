/**
 * Network Monitoring Threshold Configuration
 * 
 * Standardized response time thresholds for consistent status determination
 * across all monitor types in the 4-status system (up, warning, critical, down)
 */

const MONITOR_THRESHOLDS = {
  // Network connectivity monitors (should be fast and responsive)
  icmp: {
    warning: 500,   // 500ms - Network latency threshold
    critical: 1000  // 1000ms - High latency threshold
  },
  
  tcp: {
    warning: 500,   // 500ms - TCP connection establishment threshold
    critical: 1000  // 1000ms - Slow TCP connection threshold
  },
  
  // Application layer monitors (more tolerant for web applications)
  http: {
    warning: 1000,  // 1000ms - Web page load time threshold
    critical: 2500  // 2500ms - Reduced from 3000ms for better consistency
  },
  
  // DNS resolution monitors (should be fast but realistic)
  dns: {
    warning: 300,   // 300ms - Increased from 200ms for more realistic threshold
    critical: 800   // 800ms - Reduced from 1000ms for better consistency
  },
  
  // SSL certificate monitors (time-based, different metric)
  ssl: {
    warning: 30,    // 30 days - Certificate expiration warning
    critical: 7     // 7 days - Certificate expiration critical
  }
};

/**
 * Get threshold configuration for a specific monitor type
 * @param {string} monitorType - Monitor type (icmp, http, tcp, dns, ssl)
 * @returns {Object} - Threshold configuration with warning and critical values
 */
const getThresholds = (monitorType) => {
  const thresholds = MONITOR_THRESHOLDS[monitorType];
  if (!thresholds) {
    console.warn(`Unknown monitor type: ${monitorType}, using default thresholds`);
    return { warning: 500, critical: 1000 };
  }
  return thresholds;
};

/**
 * Check if response time exceeds warning threshold
 * @param {string} monitorType - Monitor type
 * @param {number} responseTime - Response time in milliseconds
 * @returns {boolean} - True if exceeds warning threshold
 */
const isWarning = (monitorType, responseTime) => {
  const thresholds = getThresholds(monitorType);
  return responseTime >= thresholds.warning;
};

/**
 * Check if response time exceeds critical threshold
 * @param {string} monitorType - Monitor type
 * @param {number} responseTime - Response time in milliseconds
 * @returns {boolean} - True if exceeds critical threshold
 */
const isCritical = (monitorType, responseTime) => {
  const thresholds = getThresholds(monitorType);
  return responseTime > thresholds.critical;
};

module.exports = {
  MONITOR_THRESHOLDS,
  getThresholds,
  isWarning,
  isCritical
};
