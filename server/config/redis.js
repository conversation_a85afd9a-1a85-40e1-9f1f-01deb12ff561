const Redis = require('ioredis');
const dotenv = require('dotenv');

dotenv.config();

// Redis bağlantı bilgileri
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || '',
  retryStrategy: (times) => {
    // Bağlantı hatası durumunda yeniden deneme stratejisi
    const delay = Math.min(times * 50, 2000);
    return delay;
  }
};

// Redis istemcisini oluştur
const redisClient = new Redis(redisConfig);

// Bağlantı olaylarını dinle
redisClient.on('connect', () => {
  console.log('Redis server connected');
});

redisClient.on('error', (err) => {
  console.error('Redis connection error:', err);
});

module.exports = redisClient;
