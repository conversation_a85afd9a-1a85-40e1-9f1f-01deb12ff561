const cron = require('node-cron');
const redisClient = require('../config/redis');
// Connection pool geçici olarak devre dışı - performans sorunu
// const { withRedisConnection, withRedisTransaction } = require('../config/redisPool');

// Temizleme servisini yükle
const cleanupService = require('./cleanupService');

// Not: İzleme modülleri artık monitorTypes içinde tanımlanıyor

// Bildirim servisini yükle
const notificationService = require('./notificationService');

// deviceAlertService kaldırıldı - notificationService direkt kullanılıyor

// Cihaz durumu hesaplama servisini yükle
const deviceStatusService = require('./deviceStatusService');

// Aktif zamanlayıcıları sakla
const activeSchedulers = {};

// Zamanlayıcı başlangıç zamanını kaydet
global.schedulerStartTime = Date.now();

// İzleme türlerini ve özelliklerini tanımla
const monitorTypes = {
  icmp: {
    name: 'ICMP',
    module: require('./monitors/icmp'),
    monitorFunction: 'monitorDevice',
    statusField: 'alive',
    responseTimeField: 'time',
    defaultEnabled: true // ICMP varsayılan olarak etkin
  },
  http: {
    name: 'HTTP',
    module: require('./monitors/http'),
    monitorFunction: 'monitorDevice',
    statusField: 'success',
    responseTimeField: 'responseTime',
    defaultEnabled: false
  },
  tcp: {
    name: 'TCP',
    module: require('./monitors/tcp'),
    monitorFunction: 'monitorDevice',
    statusField: 'open',
    responseTimeField: 'responseTime',
    defaultEnabled: false,
    requiresPort: true
  },

  dns: {
    name: 'DNS',
    module: require('./monitors/dns-monitor'),
    monitorFunction: 'monitorDevice',
    statusField: 'status',
    responseTimeField: 'responseTime',
    defaultEnabled: false,
    customFields: ['domain', 'recordType', 'server']
  },
  ssl: {
    name: 'SSL',
    module: require('./monitors/ssl-monitor'),
    monitorFunction: 'monitorDevice',
    statusField: 'status',
    responseTimeField: 'responseTime',
    defaultEnabled: false,
    customFields: ['port', 'host', 'domain']
  }
};

// Not: Varsayılan izleme aralıkları artık monitorTypes içinde tanımlanıyor

// Batch processing konfigürasyonu - 500 cihaz için optimize edilmiş
const BATCH_CONFIG = {
  SIZE: 50,           // 50'şer cihaz işle (2.5x artış)
  DELAY: 1000,        // 1 saniye bekle (2x hızlı)
  MAX_CONCURRENT: 10  // Maksimum 10 eş zamanlı batch (2x artış)
};

/**
 * Cihazları batch'ler halinde işler
 * @param {Array} devices - İşlenecek cihaz listesi
 * @param {Function} processingFunction - Her cihaz için çalıştırılacak fonksiyon
 * @param {Object} io - Socket.io nesnesi
 * @returns {Promise} - İşlem sonucu
 */
const processBatchedDevices = async (devices, processingFunction, io) => {
  const totalDevices = devices.length;
  const batchCount = Math.ceil(totalDevices / BATCH_CONFIG.SIZE);

  console.log(`Starting batch processing: ${totalDevices} devices in ${batchCount} batches`);

  for (let i = 0; i < totalDevices; i += BATCH_CONFIG.SIZE) {
    const batchNumber = Math.floor(i / BATCH_CONFIG.SIZE) + 1;
    const batch = devices.slice(i, i + BATCH_CONFIG.SIZE);

    console.log(`Processing batch ${batchNumber}/${batchCount}: ${batch.length} devices`);

    try {
      // Batch içindeki cihazları paralel olarak işle
      await Promise.all(batch.map(device => processingFunction(device, io)));

      console.log(`Batch ${batchNumber}/${batchCount} completed successfully`);

      // Son batch değilse bekle
      if (i + BATCH_CONFIG.SIZE < totalDevices) {
        console.log(`Waiting ${BATCH_CONFIG.DELAY}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, BATCH_CONFIG.DELAY));
      }
    } catch (error) {
      console.error(`Error in batch ${batchNumber}/${batchCount}:`, error);
      // Hata olsa bile devam et
    }
  }

  console.log(`Batch processing completed: ${totalDevices} devices processed`);
};

/**
 * Tek bir cihazı smart scheduler mantığıyla işler
 * @param {Object} device - Cihaz bilgileri
 * @param {Object} io - Socket.io nesnesi
 * @returns {Promise} - İşlem sonucu
 */
const processDeviceInScheduler = async (device, io) => {
  const deviceId = device.id;
  const monitors = device.monitors || {};
  const now = Date.now();

  // Her izleme türü için kontrol et
  const monitorTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl'];

  // Hangi izleme türlerinin kontrol edilmesi gerektiğini belirle
  const monitorsToCheck = [];

  // Tüm izleme türleri için Redis'ten son kontrol ve sonraki kontrol zamanlarını tek seferde al
  const monitorTypesToCheck = monitorTypes;
  console.log(`Device ${device.name} (${deviceId}): monitorTypesToCheck=${JSON.stringify(monitorTypesToCheck)}`);
  console.log(`Device ${device.name} (${deviceId}): monitors=${JSON.stringify(monitors)}`);
  const redisKeys = [];

  // Her izleme türü için Redis anahtarlarını oluştur
  for (const type of monitorTypesToCheck) {
    // İzleme etkin değilse atla
    if (type === 'icmp' || (monitors[type] && monitors[type].enabled)) {
      // Son kontrol zamanı ve sonraki kontrol zamanı için anahtarı ekle
      const statusKey = `monitor:${type}:${deviceId}`;

      // TCP için özel durum: port bilgisini içeren anahtarlar kullanılıyor
      if (type === 'tcp' && monitors.tcp && monitors.tcp.port) {
        const port = monitors.tcp.port;
        const tcpKey = `monitor:${type}:${deviceId}:${port}`;
        redisKeys.push(tcpKey);
        console.log(`Device ${device.name} (${deviceId}): Added TCP key ${tcpKey}`);
      } else {
        redisKeys.push(statusKey);
        console.log(`Device ${device.name} (${deviceId}): Added key ${statusKey}`);
      }
    }
  }
  console.log(`Device ${device.name} (${deviceId}): Added ${redisKeys.length} Redis keys`);

  // Tüm anahtarları tek seferde al
  let redisResults = [];
  if (redisKeys.length > 0) {
    // Her anahtar için hgetall kullanarak tüm hash değerlerini al
    redisResults = await Promise.all(redisKeys.map(key => redisClient.hgetall(key)));
  }

  // Redis değerlerini işle
  const monitorChecks = [];
  let keyIndex = 0;
  console.log(`Device ${device.name} (${deviceId}): Processing ${redisResults.length} Redis results`);

  for (const type of monitorTypesToCheck) {
    // İzleme etkin değilse atla
    if (type === 'icmp' || (monitors[type] && monitors[type].enabled)) {
      // İzleme türü için interval değerini al (dakika cinsinden, milisaniyeye çevir)
      // Eğer interval değeri yoksa, varsayılan olarak 5 dakika kullan
      const intervalValue = monitors[type] && monitors[type].interval ? monitors[type].interval : '5';
      const interval = parseInt(intervalValue) * 60 * 1000;

      // Redis'ten alınan hash değerlerini kullan
      let monitorData = redisResults[keyIndex++] || {};

      // TCP için özel durum: port bilgisini içeren anahtarlar kullanılıyor
      if (type === 'tcp' && monitors.tcp && monitors.tcp.port) {
        try {
          const port = monitors.tcp.port;
          const tcpKey = `monitor:${type}:${deviceId}:${port}`;
          const tcpData = await redisClient.hgetall(tcpKey);
          if (tcpData && Object.keys(tcpData).length > 0) {
            monitorData = tcpData;
            console.log(`Using TCP data with port ${port} for device ${deviceId}`);
          }
        } catch (e) {
          console.error(`Error getting TCP data with port for device ${deviceId}:`, e);
        }
      }

      const lastCheck = parseInt(monitorData.lastCheck || '0');
      const nextCheck = parseInt(monitorData.nextCheck || '0');
      console.log(`Device ${device.name} (${deviceId}), Monitor ${type}: lastCheck=${lastCheck}, nextCheck=${nextCheck}, now=${now}`);

      // Eğer sonraki kontrol zamanı henüz belirlenmemişse veya geçmişte kalmışsa, şimdi belirle
      if (nextCheck === 0) {
        // Eğer hiç kontrol edilmemişse veya son kontrol zamanı 0 ise
        if (lastCheck === 0) {
          // İlk kontrol için şimdiki zamanı kullan, ancak sonraki kontrol zamanını interval kadar sonraya ayarla
          monitorChecks.push({
            type,
            shouldCheck: true,
            needsUpdate: true,
            isInitial: true,
            lastCheck: 0,
            interval
          });
          console.log(`Initial check for ${type} monitor of device ${deviceId}`);
        } else {
          // Sonraki kontrol zamanını hesapla: son kontrol zamanı + interval
          let calculatedNextCheck = lastCheck + interval;

          // Eğer hesaplanan sonraki kontrol zamanı geçmişte kaldıysa, şimdiki zamandan başla
          if (calculatedNextCheck < now) {
            calculatedNextCheck = now;
          }

          monitorChecks.push({
            type,
            shouldCheck: true,
            needsUpdate: true,
            isInitial: false,
            lastCheck,
            interval
          });
          console.log(`Setting next check time for ${type} monitor of device ${deviceId}`);
        }
      } else if (now >= nextCheck) {
        // Kontrol zamanı geldiyse listeye ekle
        monitorChecks.push({
          type,
          shouldCheck: true,
          needsUpdate: true, // Değiştirildi: false -> true
          isInitial: false,
          lastCheck,
          interval
        });
        console.log(`Check time reached for ${type} monitor of device ${deviceId}: now=${now}, nextCheck=${nextCheck}`);
      }
    }
  }

  // Redis güncellemelerini yap
  const updatePromises = monitorChecks.filter(check => check.needsUpdate).map(check => {
    return updateQueryTimes(deviceId, check.type, check.lastCheck, check.interval, redisClient, check.isInitial);
  });

  // Tüm güncellemelerin tamamlanmasını bekle
  if (updatePromises.length > 0) {
    await Promise.all(updatePromises);
  }

  // Kontrol edilecek izleme türlerini belirle
  const results = monitorChecks.filter(check => check.shouldCheck).map(check => check.type);

  // null olmayan sonuçları monitorsToCheck listesine ekle
  results.forEach(type => {
    if (type) {
      monitorsToCheck.push(type);
    }
  });

  console.log(`Device ${device.name} (${deviceId}): monitorsToCheck=${monitorsToCheck.length}`);

  // Eğer kontrol edilecek izleme türleri varsa, cihazı kontrol et
  if (monitorsToCheck.length > 0) {
    console.log(`Checking device ${device.name} (${deviceId}) for monitors: ${monitorsToCheck.join(', ')}`);
    try {
      // Socket.io ile kontrol başladı bilgisi gönder
      if (io) {
        io.emit('device:monitoring:start', {
          deviceId: device.id,
          monitorTypes: monitorsToCheck,
          timestamp: Date.now()
        });
      }

      // Cihazı kontrol et
      await monitorDevice(device, io, monitorsToCheck);

      // Socket.io ile kontrol tamamlandı bilgisi gönder
      if (io) {
        io.emit('device:monitoring:complete', {
          deviceId: device.id,
          monitorTypes: monitorsToCheck,
          timestamp: Date.now()
        });
      }

      // Kontrol işlemi başarıyla tamamlandıktan sonra son kontrol zamanlarını ve sonraki sorgu zamanlarını güncelle
      const updateTime = Date.now();
      for (const type of monitorsToCheck) {
        // İzleme türü için interval değerini al (dakika cinsinden, milisaniyeye çevir)
        // Eğer interval değeri yoksa, varsayılan olarak 5 dakika kullan
        const intervalValue = monitors[type] && monitors[type].interval ? monitors[type].interval : '5';
        const interval = parseInt(intervalValue) * 60 * 1000;

        // Son kontrol zamanını ve sonraki sorgu zamanını güncelle
        // Not: DNS, SSL, veritabanı ve API izleme türleri için, monitorDevice fonksiyonu içinde zaten güncelleme yapılıyor olabilir
        // Bu nedenle, bu türler için güncelleme yapmadan önce kontrol et
        if (['dns', 'ssl', 'database', 'api'].includes(type)) {
          // Bu türler için, monitorDevice fonksiyonu içinde güncelleme yapılıp yapılmadığını kontrol et
          const statusKey = `monitor:${type}:${deviceId}`;
          const lastCheck = await redisClient.hget(statusKey, 'lastCheck');

          // Eğer son kontrol zamanı updateTime'a yakınsa, güncelleme yapılmış demektir
          if (lastCheck && Math.abs(parseInt(lastCheck) - updateTime) < 5000) {
            console.log(`Skipping update for ${type} monitor of device ${deviceId} as it was already updated in monitorDevice`);
            continue;
          }
        }

        // Son kontrol zamanını ve sonraki sorgu zamanını güncelle
        await updateQueryTimes(deviceId, type, updateTime, interval, redisClient);
      }
      console.log(`Updated check times and next query times for device ${device.name} (${deviceId})`);
    } catch (error) {
      console.error(`Error monitoring device ${device.name} (${deviceId}):`, error);

      // Hata durumunda da son kontrol zamanlarını güncelle, normal interval ile
      const updateTime = Date.now();
      for (const type of monitorsToCheck) {
        try {
          // İzleme türü için interval değerini al
          // Eğer interval değeri yoksa, varsayılan olarak 5 dakika kullan
          const intervalValue = monitors[type] && monitors[type].interval ? monitors[type].interval : '5';
          const interval = parseInt(intervalValue) * 60 * 1000;

          // Son kontrol zamanını güncelle, normal interval ile
          await redisClient.hmset(`monitor:${type}:${deviceId}`, {
            'lastCheck': updateTime.toString(),
            'nextCheck': (updateTime + interval).toString(),
            'lastError': error.message || 'Unknown error',
            'lastErrorTime': updateTime.toString()
          });

          console.log(`Updated query times for ${type} monitor of device ${deviceId} after error with normal interval: ${interval}ms`);
        } catch (updateError) {
          console.error(`Error updating query times for ${type} monitor of device ${deviceId} after error:`, updateError);
        }
      }
    }
  }
};

/**
 * Tüm cihazları Redis'ten alır
 * @returns {Promise<Array>} - Cihaz listesi
 */
const getAllDevices = async () => {
  try {
    // Tüm cihaz ID'lerini al
    const deviceIds = await redisClient.smembers('devices');

    // Her cihazın detaylarını al
    const devices = [];
    for (const id of deviceIds) {
      const deviceData = await redisClient.hgetall(`device:${id}`);
      if (deviceData) {
        // Monitors alanını JSON'dan parse et
        if (deviceData.monitors) {
          try {
            deviceData.monitors = JSON.parse(deviceData.monitors);
          } catch (e) {
            deviceData.monitors = {};
          }
        } else {
          deviceData.monitors = {};
        }

        devices.push({
          id,
          ...deviceData
        });
      }
    }

    return devices;
  } catch (error) {
    console.error('Error getting devices from Redis:', error);
    return [];
  }
};

/**
 * Belirli bir cihazı izler
 * @param {Object} device - Cihaz bilgileri
 * @param {Object} io - Socket.io nesnesi
 * @param {Array} monitorTypesToCheck - Kontrol edilecek izleme türleri (boş ise tümü kontrol edilir)
 * @param {boolean} isManualCheck - Manuel kontrol mu? (varsayılan: false)
 */
const monitorDevice = async (device, io, monitorTypesToCheck = [], isManualCheck = false) => {
  try {
    console.log(`🎯 MONITOR DEVICE CALLED: ${device.name} (${device.id}) - isManualCheck: ${isManualCheck} - monitorTypesToCheck:`, monitorTypesToCheck);
    const monitors = device.monitors || {};
    const results = {};
    const now = Date.now();

    // Her izleme türü için kontrol et
    for (const type in monitorTypes) {
      // Bu izleme türü kontrol edilecek mi?
      const shouldCheck = (monitorTypesToCheck.length === 0 || monitorTypesToCheck.includes(type)) &&
                         (type === 'icmp' ? (monitors.icmp && monitors.icmp.enabled !== false) :
                          (monitors[type] && monitors[type].enabled));

      if (!shouldCheck) continue;

      try {
        console.log(`Checking ${monitorTypes[type].name} for device ${device.name} (${device.id})`);

        // İzleme modülünü ve fonksiyonunu al
        const monitorModule = monitorTypes[type].module;
        const monitorFunction = monitorTypes[type].monitorFunction;

        // İzleme işlemini gerçekleştir (manuel kontrol parametresini de geçir)
        console.log(`🚀 CALLING MONITOR: ${type} for device ${device.name}`);
        results[type] = await monitorModule[monitorFunction](device, io, isManualCheck);
        console.log(`✅ MONITOR RESULT: ${type} for device ${device.name} - result:`, JSON.stringify(results[type], null, 2));

        // İzleme sonucunu kontrol et
        if (results[type]) {
          console.log(`📊 PROCESSING RESULT: ${type} for device ${device.name} - has result: true`);
          // Durum alanını belirle
          const statusField = monitorTypes[type].statusField;
          const responseTimeField = monitorTypes[type].responseTimeField;

          // Durum değerini belirle - Tüm monitor türleri artık 4-durumlu status döndürüyor
          let status = results[type].status || 'unknown';

          // Yanıt süresini belirle
          const responseTime = results[type][responseTimeField] || 0;

          // Hata mesajını belirle
          const error = results[type].error || '';

          // Açıklama mesajını belirle (yeni açıklama sistemi)
          const message = results[type].message || '';
          console.log(`🔍 DEBUG MESSAGE: ${type} monitor for ${device.name} - message: "${message}" - results[${type}]:`, JSON.stringify(results[type], null, 2));

          // Ek alanları belirle
          const additionalFields = {};

          // TCP için port bilgisini ekle
          if (type === 'tcp' && monitors.tcp && monitors.tcp.port) {
            additionalFields.port = monitors.tcp.port;
          }

          // SSL için daysRemaining bilgisini ekle
          if (type === 'ssl' && results[type].details && results[type].details.daysRemaining) {
            additionalFields.daysRemaining = results[type].details.daysRemaining;
          }

          // 🔍 Önceki durumu kontrol et (bildirim spam'ini önlemek için)
          const monitorKey = `monitor:${type}:${device.id}`;
          const previousData = await redisClient.hgetall(monitorKey);
          const previousStatus = previousData.status || null;

          // 📊 Bildirim oluşturma mantığı
          let shouldNotify = false;

          // İlk kontrol (previousStatus yok) durumunda sadece sorun varsa bildir
          if (!previousStatus) {
            shouldNotify = status === 'down' || status === 'critical' || status === 'warning';
          } else {
            // Durum değişikliği varsa ve anlamlı ise bildir
            shouldNotify = (previousStatus !== status) && (
              // DOWN durumu: Her zaman bildir
              status === 'down' || status === 'critical' || status === 'warning' ||
              // UP durumu: Sadece iyileşme anında bildir (önceki durum down/critical/warning ise)
              (status === 'up' && ['down', 'critical', 'warning'].includes(previousStatus))
            );
          }

          if (shouldNotify) {
            console.log(`📢 NOTIFICATION TRIGGERED: ${type} for ${device.name} - ${previousStatus} → ${status}`);
            await notificationService.createDeviceStatusNotification(
              device.id,
              device.name,
              type,
              {
                status,
                responseTime,
                lastCheck: now,
                error,
                host: device.host,
                ...additionalFields
              }
            );
          } else {
            console.log(`🔇 NOTIFICATION SKIPPED: ${type} for ${device.name} - ${previousStatus} → ${status} (no change or not significant)`);
          }

          // Tüm izleme türleri için sonuçları Redis'e kaydet
          // ICMP, HTTP, TCP, DNS, SSL için unified kayıt sistemi
          if (['icmp', 'http', 'tcp', 'dns', 'ssl', 'database', 'api'].includes(type)) {
            // Sonucu Redis'e kaydet
            const key = `monitor:${type}:${device.id}`;

            // İzleme türü için interval değerini al (dakika cinsinden, milisaniyeye çevir)
            // Eğer interval değeri yoksa, varsayılan değer kullan ve hata mesajı yazdır
            if (!monitors[type] || !monitors[type].interval) {
              console.error(`ERROR: Monitor ${type} for device ${device.id} (${device.name}) has no interval set! This is a configuration error.`);

              // SSL için varsayılan değer 60 dakika, diğerleri için 5 dakika
              const defaultInterval = type === 'ssl' ? '60' : '5';
              console.log(`Using default interval of ${defaultInterval} minutes for ${type} monitor of device ${device.id} (${device.name})`);
            }

            // SSL için varsayılan değer 60 dakika, diğerleri için 5 dakika
            const defaultInterval = type === 'ssl' ? '60' : '5';
            const intervalValue = monitors[type] && monitors[type].interval ? monitors[type].interval : defaultInterval;
            const interval = parseInt(intervalValue) * 60 * 1000;

            // nextCheck değerini hesapla
            const nextCheck = now + interval;

            console.log(`💾 SAVING TO REDIS: ${key} - message: "${results[type].message || ''}" - status: ${status}`);
            await redisClient.hmset(key, {
              'status': status,
              'responseTime': responseTime,
              'lastCheck': now.toString(),
              'nextCheck': nextCheck.toString(),
              'error': error,
              'message': results[type].message || '', // Yeni açıklama alanı
              'details': JSON.stringify(results[type].details || {})
            });
            console.log(`✅ SAVED TO REDIS: ${key}`);

            // TTL ayarla (24 saat)
            await redisClient.expire(key, 86400);

            // Geçmiş verileri kaydet (son 100 ölçüm)
            const historyKey = `history:${type}:${device.id}`;

            // Geçmiş veriler için özel alanları belirle
            let historyData = {
              timestamp: now,
              status,
              responseTime,
              message: results[type].message || '', // Yeni açıklama alanı
              error: error
            };

            // İzleme türüne göre özel alanları ekle
            if (type === 'dns' && results[type].details && results[type].details.records) {
              historyData.records = results[type].details.records;
            } else if (type === 'ssl' && results[type].details) {
              historyData.details = {
                expiryStatus: results[type].details.expiryStatus || 'unknown',
                daysRemaining: results[type].details.daysRemaining || 0
              };
            } else if (type === 'database' && monitors.database && monitors.database.type) {
              historyData.dbType = monitors.database.type;
            } else if (type === 'api' && results[type].details && results[type].details.statusCode) {
              historyData.statusCode = results[type].details.statusCode;
            }

            await redisClient.lpush(historyKey, JSON.stringify(historyData));
            await redisClient.ltrim(historyKey, 0, 99);

            // Individual monitor updates kaldırıldı - batch update için veri toplanıyor
            // Socket.io güncellemesi device status hesaplandıktan sonra toplu olarak gönderilecek
          }
        }
      } catch (error) {
        console.error(`Error monitoring ${type} for device ${device.host}:`, error);
        results[type] = { error: error.message };
      }
    }

    // Uyarıları kontrol et
    await checkAlerts(device, results, io);

    // Cihaz durumunu hesapla ve kaydet
    try {
      // Tüm izleme sonuçlarını bir araya getir
      const monitorResults = {};

      // Her izleme türü için sonuçları düzenle
      for (const type in monitorTypes) {
        if (results[type]) {
          const statusField = monitorTypes[type].statusField;
          const responseTimeField = monitorTypes[type].responseTimeField;

          // Tüm monitor türleri artık 4-durumlu status döndürüyor
          let status = results[type].status || 'unknown';

          monitorResults[type] = {
            status,
            responseTime: results[type][responseTimeField] || 0,
            lastCheck: now.toString(),
            error: results[type].error || ''
          };
        } else {
          monitorResults[type] = null;
        }
      }

      // Cihaz durumunu hesapla ve kaydet
      await deviceStatusService.calculateAndSaveDeviceStatus(device.id, device, monitorResults, redisClient);

      // Hesaplanan durumu al
      const statusData = await deviceStatusService.getDeviceStatus(device.id, redisClient);

      // Batch update: Tüm monitor durumları ve hesaplanmış device durumu birlikte gönder
      if (io) {
        // Monitor durumlarını topla
        const monitorUpdates = {};
        for (const [type, result] of Object.entries(results)) {
          if (result && !result.error) {
            monitorUpdates[type] = {
              status: result.status,
              responseTime: result.responseTime || 0,
              lastCheck: Date.now(),
              message: result.message || '',
              details: result.details || {}
            };
          }
        }

        // Tek seferde tüm updates'i gönder (yeni event)
        io.emit('device:complete:update', {
          deviceId: device.id,
          calculatedStatus: statusData.status,
          reason: statusData.reason,
          rawStatus: statusData.rawStatus,
          lastCalculated: statusData.lastCalculated,
          monitors: monitorUpdates,
          timestamp: Date.now()
        });

        // Backward compatibility için eski events'i de gönder
        io.emit('device:status:update', {
          deviceId: device.id,
          calculatedStatus: statusData.status,
          reason: statusData.reason,
          rawStatus: statusData.rawStatus,
          lastCalculated: statusData.lastCalculated
        });
      }
    } catch (error) {
      console.error(`Error calculating device status for ${device.id}:`, error);
    }

    return results;
  } catch (error) {
    console.error(`Error monitoring device ${device.id}:`, error);
    return { error: error.message };
  }
};

/**
 * Uyarıları kontrol eder ve gerekirse bildirim gönderir
 * @param {Object} device - Cihaz bilgileri
 * @param {Object} results - İzleme sonuçları
 * @param {Object} io - Socket.io nesnesi
 */
const checkAlerts = async (device, results, io) => {
  try {
    const alerts = device.alerts || [];

    for (const alert of alerts) {
      if (!alert.enabled) continue;

      const { type, condition, threshold, duration } = alert;

      // İlgili izleme sonucunu al
      const monitorResult = results[type];
      if (!monitorResult) continue;

      // Durumu kontrol et
      let triggered = false;

      switch (type) {
        case 'icmp':
          if (condition === 'down' && !monitorResult.alive) {
            triggered = true;
          } else if (condition === 'latency' && monitorResult.time > threshold) {
            triggered = true;
          }
          break;

        case 'http':
          if (condition === 'down' && !monitorResult.success) {
            triggered = true;
          } else if (condition === 'status' && monitorResult.status !== threshold) {
            triggered = true;
          } else if (condition === 'latency' && monitorResult.responseTime > threshold) {
            triggered = true;
          }
          break;

        case 'tcp':
          if (condition === 'down' && !monitorResult.open) {
            triggered = true;
          } else if (condition === 'latency' && monitorResult.responseTime > threshold) {
            triggered = true;
          }
          break;

        case 'snmp':
          if (condition === 'down' && !monitorResult.success) {
            triggered = true;
          }
          break;

        case 'dns':
          if (condition === 'down' && monitorResult.status === 'down') {
            triggered = true;
          } else if (condition === 'latency' && monitorResult.responseTime > threshold) {
            triggered = true;
          }
          break;

        case 'ssl':
          if (condition === 'down' && monitorResult.status === 'down') {
            triggered = true;
          } else if (condition === 'expiry' && monitorResult.details?.daysRemaining < threshold) {
            triggered = true;
          }
          break;

        case 'database':
          if (condition === 'down' && monitorResult.status === 'down') {
            triggered = true;
          } else if (condition === 'latency' && monitorResult.responseTime > threshold) {
            triggered = true;
          }
          break;

        case 'api':
          if (condition === 'down' && monitorResult.status === 'down') {
            triggered = true;
          } else if (condition === 'latency' && monitorResult.responseTime > threshold) {
            triggered = true;
          } else if (condition === 'status' && monitorResult.details?.statusCode !== parseInt(threshold)) {
            triggered = true;
          }
          break;
      }

      if (triggered) {
        // Uyarı durumunu kontrol et
        const alertKey = `alert:${device.id}:${type}:${condition}`;
        const alertStatus = await redisClient.get(alertKey);

        if (!alertStatus) {
          // Yeni uyarı, süresi başlat
          await redisClient.set(alertKey, Date.now());
          await redisClient.expire(alertKey, Math.ceil(duration / 1000) * 2);
        } else {
          // Mevcut uyarı, süreyi kontrol et
          const alertTime = parseInt(alertStatus);
          const currentTime = Date.now();

          if (currentTime - alertTime >= duration) {
            // Süre doldu, bildirim gönder
            const alertData = {
              deviceId: device.id,
              deviceName: device.name,
              type,
              condition,
              threshold,
              value: type === 'icmp' ? monitorResult.time :
                     type === 'http' ? monitorResult.responseTime :
                     type === 'tcp' ? monitorResult.responseTime : 0,
              timestamp: currentTime
            };

            // Bildirim gönder
            await notificationService.sendAlert(alertData);

            // Socket.io ile uyarı bilgisini gönder
            if (io) {
              io.emit('alert:new', alertData);
            }

            // Uyarı durumunu sıfırla
            await redisClient.del(alertKey);
          }
        }
      } else {
        // Uyarı durumunu temizle
        const alertKey = `alert:${device.id}:${type}:${condition}`;
        await redisClient.del(alertKey);
      }
    }
  } catch (error) {
    console.error(`Error checking alerts for device ${device.id}:`, error);
  }
};

/**
 * Tüm cihazları izler
 * @param {Object} io - Socket.io nesnesi
 * @param {Array} specificDeviceIds - Belirli cihaz ID'leri (boş ise tüm cihazlar kontrol edilir)
 * @param {Array} specificMonitorTypes - Belirli izleme türleri (boş ise tüm izleme türleri kontrol edilir)
 * @param {boolean} updateLastCheckTimes - Son kontrol zamanlarını güncelle (varsayılan: false)
 * @param {boolean} isManualCheck - Manuel kontrol mu? (varsayılan: false)
 */
const monitorAllDevices = async (io, specificDeviceIds = [], specificMonitorTypes = [], updateLastCheckTimes = false, isManualCheck = false) => {
  try {
    // Zamanlayıcı durumunu güncelle
    global.lastSchedulerRun = Date.now();

    const devices = await getAllDevices();

    // Belirli cihazları veya tüm cihazları kontrol et
    const devicesToCheck = specificDeviceIds.length > 0
      ? devices.filter(device => specificDeviceIds.includes(device.id))
      : devices;

    // Cihazları paralel olarak kontrol et
    const devicePromises = devicesToCheck.map(async (device) => {
      try {
        console.log(`Monitoring device ${device.name} (${device.id})...`);

        // Kontrol başlangıç zamanı
        const startTime = Date.now();

        // Artık cihaz özelinde son kontrol zamanları saklanmıyor
        // Her izleme türü kendi son kontrol ve sonraki kontrol zamanlarını Redis'te saklıyor

        // Manuel kontrol ise isManualCheck parametresini geçir
        await monitorDevice(device, io, specificMonitorTypes, isManualCheck);

        // Cihaz durumunu hesapla ve kaydet
        try {
          // Cihaz durumu hesaplama servisini yükle
          const deviceStatusService = require('./deviceStatusService');

          // Cihaz detaylarını al
          const deviceData = await redisClient.hgetall(`device:${device.id}`);
          if (deviceData) {
            // Monitors alanını JSON'dan parse et
            if (deviceData.monitors) {
              try {
                deviceData.monitors = JSON.parse(deviceData.monitors);
              } catch (e) {
                deviceData.monitors = {};
              }
            } else {
              deviceData.monitors = {};
            }

            // Tüm izleme sonuçlarını al
            const icmpData = await redisClient.hgetall(`monitor:icmp:${device.id}`);
            const httpData = await redisClient.hgetall(`monitor:http:${device.id}`);
            const snmpData = await redisClient.hgetall(`monitor:snmp:${device.id}`);
            const dnsData = await redisClient.hgetall(`monitor:dns:${device.id}`);
            const sslData = await redisClient.hgetall(`monitor:ssl:${device.id}`);
            const dbData = await redisClient.hgetall(`monitor:database:${device.id}`);
            const apiData = await redisClient.hgetall(`monitor:api:${device.id}`);

            // TCP durumunu al
            const tcpData = {};
            if (deviceData.monitors.tcp && deviceData.monitors.tcp.enabled) {
              const port = deviceData.monitors.tcp.port || 80;
              const tcpKey = `monitor:tcp:${device.id}:${port}`;
              const portData = await redisClient.hgetall(tcpKey);
              if (portData && Object.keys(portData).length > 0) {
                tcpData[port] = portData;
                console.log(`Retrieved TCP data with port ${port} for device ${device.id}`);
              } else {
                console.log(`No TCP data found with port ${port} for device ${device.id}`);
              }
            }

            // Tüm izleme sonuçlarını bir araya getir
            const monitorResults = {
              icmp: icmpData,
              http: httpData,
              tcp: tcpData,
              snmp: snmpData,
              dns: dnsData,
              ssl: sslData,
              database: dbData,
              api: apiData
            };

            // Mevcut hesaplanmış durumu al (manuel kontrol için yeniden hesaplama yapma)
            const statusData = await deviceStatusService.getDeviceStatus(device.id, redisClient);

            // Socket.io ile bildirim gönder
            if (io) {
              io.emit('device:checked', {
                deviceId: device.id,
                timestamp: Date.now(),
                monitorTypes: specificMonitorTypes.length > 0 ? specificMonitorTypes : 'all',
                calculatedStatus: statusData.status,
                reason: statusData.reason,
                rawStatus: statusData.rawStatus,
                lastCalculated: statusData.lastCalculated
              });
            }
          } else {
            // Socket.io ile bildirim gönder
            if (io) {
              io.emit('device:checked', {
                deviceId: device.id,
                timestamp: Date.now(),
                monitorTypes: specificMonitorTypes.length > 0 ? specificMonitorTypes : 'all'
              });
            }
          }
        } catch (error) {
          console.error(`Error calculating device status for ${device.id}:`, error);

          // Socket.io ile bildirim gönder
          if (io) {
            io.emit('device:checked', {
              deviceId: device.id,
              timestamp: Date.now(),
              monitorTypes: specificMonitorTypes.length > 0 ? specificMonitorTypes : 'all'
            });
          }
        }

        // Kontrol bitiş zamanı ve süre
        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`Device ${device.name} (${device.id}) monitored in ${duration}ms. Started at: ${new Date(startTime).toISOString()}`);
      } catch (error) {
        console.error(`Error monitoring device ${device.name} (${device.id}):`, error);
      }
    });

    // Tüm cihaz kontrol işlemlerinin tamamlanmasını bekle
    await Promise.all(devicePromises);

    return { success: true, count: devicesToCheck.length };
  } catch (error) {
    console.error('Error monitoring devices:', error);
    return { success: false, error: error.message };
  }
};

// Global değişkenler
global.schedulerRunning = false;
global.lastSchedulerRun = null;

// Redis'i tek kaynak olarak kullanacak şekilde yeniden düzenlendi
// Bellek içi değişkenler kaldırıldı

/**
 * İzleme türü için son kontrol zamanını ve sonraki kontrol zamanını günceller
 * @param {string} deviceId - Cihaz ID
 * @param {string} type - İzleme türü
 * @param {number} updateTime - Güncelleme zamanı
 * @param {number} interval - İzleme aralığı (ms)
 * @param {Object} redisClient - Redis istemcisi
 * @param {boolean} isInitialUpdate - İlk güncelleme mi? (varsayılan: false)
 */
const updateQueryTimes = async (deviceId, type, updateTime, interval, redisClient, isInitialUpdate = false) => {
  try {
    // Redis'e son kontrol zamanını ve sonraki kontrol zamanını kaydet
    const statusKey = `monitor:${type}:${deviceId}`;

    // TCP için özel durum: port bilgisini içeren anahtarlar kullanılıyor olabilir
    let key = statusKey;
    if (type === 'tcp') {
      // TCP için port bilgisini kontrol et
      const device = await redisClient.hgetall(`device:${deviceId}`);
      if (device && device.monitors) {
        try {
          const monitors = JSON.parse(device.monitors);
          if (monitors.tcp && monitors.tcp.port) {
            key = `${statusKey}:${monitors.tcp.port}`;
            console.log(`Using TCP key with port ${monitors.tcp.port} for device ${deviceId}: ${key}`);
          }
        } catch (e) {
          console.error(`Error parsing monitors for device ${deviceId}:`, e);
        }
      }
    }

    const now = Date.now();

    // Eğer ilk güncelleme ise veya updateTime 0 ise, sadece nextCheck'i güncelle
    if (isInitialUpdate || updateTime === 0) {
      const nextCheck = now + interval;
      await redisClient.hmset(key, {
        'nextCheck': nextCheck.toString()
      });
      console.log(`Initial nextCheck set for ${type} monitor of device ${deviceId}: ${new Date(nextCheck).toISOString()}`);
    } else {
      // Normal güncelleme: hem lastCheck hem de nextCheck'i güncelle
      await redisClient.hmset(key, {
        'lastCheck': updateTime.toString(),
        'nextCheck': (updateTime + interval).toString()
      });
      console.log(`Check times updated for ${type} monitor of device ${deviceId}: lastCheck=${new Date(updateTime).toISOString()}, nextCheck=${new Date(updateTime + interval).toISOString()}`);
    }
  } catch (err) {
    console.error(`Error updating check times for ${type} monitor of device ${deviceId}:`, err);
  }
};

/**
 * İzleme türü için son kontrol zamanını Redis'ten alır
 * @param {string} deviceId - Cihaz ID
 * @param {string} type - İzleme türü
 * @param {Object} redisClient - Redis istemcisi
 * @returns {Promise<number>} - Son kontrol zamanı
 */
const getLastCheckTime = async (deviceId, type, redisClient) => {
  try {
    let statusKey = `monitor:${type}:${deviceId}`;

    // TCP için özel durum: port bilgisini içeren anahtarlar kullanılıyor olabilir
    if (type === 'tcp') {
      // TCP için port bilgisini kontrol et
      const device = await redisClient.hgetall(`device:${deviceId}`);
      if (device && device.monitors) {
        try {
          const monitors = JSON.parse(device.monitors);
          if (monitors.tcp && monitors.tcp.port) {
            statusKey = `${statusKey}:${monitors.tcp.port}`;
          }
        } catch (e) {
          console.error(`Error parsing monitors for device ${deviceId}:`, e);
        }
      }
    }

    const lastCheck = await redisClient.hget(statusKey, 'lastCheck');
    return lastCheck ? parseInt(lastCheck) : 0;
  } catch (err) {
    console.error(`Error getting last check time for ${type} monitor of device ${deviceId}:`, err);
    return 0;
  }
};

/**
 * İzleme türü için sonraki kontrol zamanını Redis'ten alır
 * @param {string} deviceId - Cihaz ID
 * @param {string} type - İzleme türü
 * @param {Object} redisClient - Redis istemcisi
 * @returns {Promise<number>} - Sonraki kontrol zamanı
 */
const getNextCheckTime = async (deviceId, type, redisClient) => {
  try {
    let statusKey = `monitor:${type}:${deviceId}`;

    // TCP için özel durum: port bilgisini içeren anahtarlar kullanılıyor olabilir
    if (type === 'tcp') {
      // TCP için port bilgisini kontrol et
      const device = await redisClient.hgetall(`device:${deviceId}`);
      if (device && device.monitors) {
        try {
          const monitors = JSON.parse(device.monitors);
          if (monitors.tcp && monitors.tcp.port) {
            statusKey = `${statusKey}:${monitors.tcp.port}`;
          }
        } catch (e) {
          console.error(`Error parsing monitors for device ${deviceId}:`, e);
        }
      }
    }

    const nextCheck = await redisClient.hget(statusKey, 'nextCheck');
    return nextCheck ? parseInt(nextCheck) : 0;
  } catch (err) {
    console.error(`Error getting next check time for ${type} monitor of device ${deviceId}:`, err);
    return 0;
  }
};

/**
 * Zamanlayıcıyı başlatır
 * @param {Object} io - Socket.io nesnesi
 */
const initScheduler = (io) => {
  // Zamanlayıcı durumunu güncelle
  global.schedulerRunning = true;
  global.lastSchedulerRun = Date.now();

  // Artık global.lastCheckTimes değişkeni kullanılmıyor
  // Her izleme türü kendi son kontrol ve sonraki kontrol zamanlarını Redis'te saklıyor

  // Sistem ayarlarını yükle
  let systemHealthCheckInterval = '1'; // Varsayılan: 1 dakika
  try {
    const fs = require('fs');
    const path = require('path');
    const settingsPath = path.join(__dirname, '../data/settings.json');

    if (fs.existsSync(settingsPath)) {
      const settingsData = fs.readFileSync(settingsPath, 'utf8');
      const settings = JSON.parse(settingsData);

      // Sistem sağlığı kontrol aralığını al
      if (settings.systemHealthCheckInterval) {
        systemHealthCheckInterval = settings.systemHealthCheckInterval;
      }
    }
  } catch (error) {
    console.error('Sistem ayarları yüklenirken hata oluştu:', error);
  }

  // Sistem sağlığını kontrol et (ayarlarda belirtilen aralıkta)
  const systemHealthCronExpression = `*/${systemHealthCheckInterval} * * * *`;
  console.log(`Sistem sağlığı kontrolü için cron ifadesi: ${systemHealthCronExpression}`);

  activeSchedulers.systemHealth = cron.schedule(systemHealthCronExpression, async () => {
    try {
      // Bildirim servisi modülünü yükle
      const notificationService = require('./notificationService');

      // Sistem sağlığını kontrol et
      const os = require('os');

      // Redis durumunu kontrol et
      let redisStatus = 'up';
      try {
        await redisClient.ping();
      } catch (error) {
        redisStatus = 'down';
      }

      // Disk bilgilerini almak için sistem modülünü yükle
      const { getDiskInfo } = require('../routes/system');

      // Sistem bilgilerini al
      const systemInfo = {
        uptime: Math.floor(os.uptime()),
        memory: {
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem()
        },
        cpu: os.cpus(),
        loadAvg: os.loadavg(),
        disk: getDiskInfo ? getDiskInfo() : { total: { pcent: 0 } }
      };

      // Servis durumlarını oluştur
      const services = {
        backend: {
          status: 'up',
          name: 'Backend API',
          description: 'Node.js Express API Sunucusu',
          uptime: process.uptime(),
          version: process.version
        },
        redis: {
          status: redisStatus,
          name: 'Redis',
          description: 'Veritabanı ve Önbellek Servisi',
          uptime: null // Redis uptime bilgisi şu anda mevcut değil
        },
        scheduler: {
          status: global.schedulerRunning ? 'up' : 'down',
          name: 'Zamanlayıcı',
          description: 'Cihaz İzleme Zamanlayıcısı',
          lastRun: global.lastSchedulerRun || null
        }
      };

      // Önceki servis durumlarını Redis'ten al
      const previousServicesStr = await redisClient.get('system:services:status');
      const previousServices = previousServicesStr ? JSON.parse(previousServicesStr) : {};

      // Servis durumlarını karşılaştır ve değişiklik varsa bildirim oluştur
      for (const [serviceId, service] of Object.entries(services)) {
        const previousStatus = previousServices[serviceId]?.status;

        // Eğer önceki durum varsa ve değiştiyse bildirim oluştur
        if (previousStatus && previousStatus !== service.status) {
          // Bildirim önem seviyesini belirle
          const severity = service.status === 'down' ? 'critical' : 'info';

          // Bildirim başlığı ve mesajını oluştur
          const title = service.status === 'down'
            ? `${service.name} Servis Kesintisi`
            : `${service.name} Servis Geri Geldi`;

          const message = service.status === 'down'
            ? `${service.name} servisi yanıt vermiyor. Servis durumu: ${service.status}`
            : `${service.name} servisi tekrar çalışıyor. Servis durumu: ${service.status}`;

          // Sistem bildirimi oluştur
          await notificationService.createSystemNotification({
            title,
            message,
            category: 'status',
            severity,
            sourceName: 'System Monitor',
            metadata: {
              serviceId,
              previousStatus,
              currentStatus: service.status,
              timestamp: Date.now()
            }
          });

          console.log(`System notification created for service ${serviceId}: ${previousStatus} -> ${service.status}`);
        }
      }

      // Mevcut servis durumlarını Redis'e kaydet
      await redisClient.set('system:services:status', JSON.stringify(services));

      // Sistem kaynaklarını kontrol et ve kritik seviyelerde bildirim oluştur
      // Bellek kullanımı %90'ın üzerindeyse
      const memoryUsagePercent = (systemInfo.memory.used / systemInfo.memory.total) * 100;
      if (memoryUsagePercent > 90) {
        // Önceki bellek uyarısını kontrol et
        const lastMemoryAlertStr = await redisClient.get('system:memory:alert');
        const lastMemoryAlert = lastMemoryAlertStr ? JSON.parse(lastMemoryAlertStr) : null;

        // Son 30 dakika içinde bildirim gönderilmediyse
        const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
        if (!lastMemoryAlert || lastMemoryAlert.timestamp < thirtyMinutesAgo) {
          // Bellek kullanımı bildirimi oluştur
          await notificationService.createSystemNotification({
            title: 'Yüksek Bellek Kullanımı',
            message: `Sistem bellek kullanımı kritik seviyede: %${memoryUsagePercent.toFixed(2)}`,
            category: 'performance',
            severity: 'warning',
            sourceName: 'System Monitor',
            metadata: {
              memoryUsage: memoryUsagePercent,
              totalMemory: systemInfo.memory.total,
              usedMemory: systemInfo.memory.used,
              timestamp: Date.now()
            }
          });

          // Son bellek uyarısını kaydet
          await redisClient.set('system:memory:alert', JSON.stringify({
            timestamp: Date.now(),
            usage: memoryUsagePercent
          }));

          console.log(`System notification created for high memory usage: ${memoryUsagePercent.toFixed(2)}%`);
        }
      }

      // CPU yükü yüksekse (load average > CPU çekirdek sayısı * 0.8)
      const cpuCores = systemInfo.cpu.length;
      const loadAverage = systemInfo.loadAvg[0]; // 1 dakikalık load average
      const loadThreshold = cpuCores * 0.8;

      if (loadAverage > loadThreshold) {
        // Önceki CPU uyarısını kontrol et
        const lastCpuAlertStr = await redisClient.get('system:cpu:alert');
        const lastCpuAlert = lastCpuAlertStr ? JSON.parse(lastCpuAlertStr) : null;

        // Son 30 dakika içinde bildirim gönderilmediyse
        const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
        if (!lastCpuAlert || lastCpuAlert.timestamp < thirtyMinutesAgo) {
          // CPU yükü bildirimi oluştur
          await notificationService.createSystemNotification({
            title: 'Yüksek CPU Kullanımı',
            message: `Sistem CPU yükü kritik seviyede: ${loadAverage.toFixed(2)} (${cpuCores} çekirdek)`,
            category: 'performance',
            severity: 'warning',
            sourceName: 'System Monitor',
            metadata: {
              loadAverage,
              cpuCores,
              threshold: loadThreshold,
              timestamp: Date.now()
            }
          });

          // Son CPU uyarısını kaydet
          await redisClient.set('system:cpu:alert', JSON.stringify({
            timestamp: Date.now(),
            load: loadAverage
          }));

          console.log(`System notification created for high CPU load: ${loadAverage.toFixed(2)}`);
        }
      }

      const healthData = {
        services,
        systemInfo
      };

      // Socket.io ile sistem sağlığı güncellemesi gönder
      if (io) {
        io.emit('system:health:update', {
          timestamp: Date.now(),
          data: healthData
        });
      }
    } catch (error) {
      console.error('Error checking system health:', error);
    }
  });

  // Her 10 saniyede bir cihazları akıllı bir şekilde izle
  // Bu, kontrollerin daha az gecikmeyle yapılmasını sağlayacak
  activeSchedulers.allDevices = cron.schedule('*/10 * * * * *', async () => {
    console.log('Smart scheduler running at:', new Date().toISOString());
    try {
      // Tüm cihazları al
      const devices = await getAllDevices();
      console.log(`Smart scheduler found ${devices.length} devices`);

      // Cihazları batch processing ile işle
      await processBatchedDevices(devices, processDeviceInScheduler, io);
    } catch (error) {
      console.error('Error in smart scheduler:', error);
    }
  });

  console.log('Scheduler initialized');

  // Temizleme cron job'ını başlat - her saat başı çalışacak şekilde
  const cleanupJob = cron.schedule('0 * * * *', async () => {
    console.log('Running cleanup tasks (hourly)...');

    try {
      const result = await cleanupService.runCleanupTasks();

      if (result.success) {
        console.log('Cleanup tasks completed successfully');
        console.log(`Cleaned up ${result.notifications.count} old notifications`);

        // İzleme verilerini temizleme sonuçlarını logla
        if (result.monitoring) {
          console.log('Monitoring data cleanup results:');
          console.log(`ICMP: ${result.monitoring.icmp.count} records`);
          console.log(`HTTP: ${result.monitoring.http.count} records`);
          console.log(`TCP: ${result.monitoring.tcp.count} records`);
          console.log(`SNMP: ${result.monitoring.snmp.count} records`);
          console.log(`DNS: ${result.monitoring.dns.count} records`);
          console.log(`SSL: ${result.monitoring.ssl.count} records`);
          console.log(`Database: ${result.monitoring.database.count} records`);
          console.log(`API: ${result.monitoring.api.count} records`);
        }
      } else {
        console.error('Cleanup tasks failed:', result.error);
      }
    } catch (error) {
      console.error('Error running cleanup tasks:', error);
    }
  });

  // Aktif zamanlayıcılara ekle
  activeSchedulers.cleanup = cleanupJob;

  console.log('Hourly cleanup cron job initialized');

  // İlk çalıştırma - tüm cihazları kontrol et ve son kontrol zamanlarını güncelle
  (async () => {
    try {
      // Bildirim servisi modülünü yükle
      const notificationService = require('./notificationService');

      // İlk çalıştırma için servis durumlarını kaydet
      const os = require('os');

      // Redis durumunu kontrol et
      let redisStatus = 'up';
      try {
        await redisClient.ping();
      } catch (error) {
        redisStatus = 'down';
      }

      // Servis durumlarını oluştur
      const services = {
        backend: {
          status: 'up',
          name: 'Backend API',
          description: 'Node.js Express API Sunucusu',
          uptime: process.uptime(),
          version: process.version
        },
        redis: {
          status: redisStatus,
          name: 'Redis',
          description: 'Veritabanı ve Önbellek Servisi',
          uptime: null
        },
        scheduler: {
          status: global.schedulerRunning ? 'up' : 'down',
          name: 'Zamanlayıcı',
          description: 'Cihaz İzleme Zamanlayıcısı',
          lastRun: global.lastSchedulerRun || null
        }
      };

      // Servis durumlarını Redis'e kaydet
      await redisClient.set('system:services:status', JSON.stringify(services));

      // Sistem başlatıldı bildirimi oluştur
      await notificationService.createSystemNotification({
        title: 'Sistem Başlatıldı',
        message: 'Network Monitor sistemi başarıyla başlatıldı.',
        category: 'system',
        severity: 'info',
        sourceName: 'System Monitor',
        metadata: {
          timestamp: Date.now(),
          services
        }
      });

      console.log('System startup notification created');

      const devices = await getAllDevices();

      // Her cihaz için son kontrol zamanlarını ve sonraki sorgu zamanlarını güncelle
      for (const device of devices) {
        const deviceId = device.id;
        const monitors = device.monitors || {};

        // Her izleme türü için son kontrol zamanını ve sonraki sorgu zamanını güncelle
        const monitorTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl'];

        // Her izleme türü için paralel olarak güncelle
        const updatePromises = monitorTypes.map(async (type) => {
          if (type === 'icmp' || (monitors[type] && monitors[type].enabled)) {
            // İzleme türü için interval değerini al (dakika cinsinden, milisaniyeye çevir)
            // Eğer interval değeri yoksa, varsayılan olarak 5 dakika kullan
            const intervalValue = monitors[type] && monitors[type].interval ? monitors[type].interval : '5';
            const interval = parseInt(intervalValue) * 60 * 1000;

            // Redis'ten mevcut değerleri kontrol et
            const lastCheck = await getLastCheckTime(deviceId, type, redisClient);

            // Eğer değer yoksa veya sıfırsa, ilk güncelleme olarak işaretle
            if (!lastCheck) {
              // Son kontrol zamanını ve sonraki kontrol zamanını güncelle (ilk güncelleme olarak işaretle)
              await updateQueryTimes(deviceId, type, 0, interval, redisClient, true);
            }
          }
        });

        // Tüm güncellemelerin tamamlanmasını bekle
        await Promise.all(updatePromises);
      }

      console.log('Initial check times set for all devices');

      // Tüm cihazları kontrol et
      await monitorAllDevices(io, [], []);
    } catch (error) {
      console.error('Error during initial device monitoring:', error);
    }
  })();

  return activeSchedulers;
};

/**
 * Zamanlayıcıyı durdurur
 */
const stopScheduler = () => {
  Object.values(activeSchedulers).forEach(scheduler => {
    scheduler.stop();
  });

  // Zamanlayıcı durumunu güncelle
  global.schedulerRunning = false;

  console.log('Scheduler stopped');
};

module.exports = {
  initScheduler,
  stopScheduler,
  monitorDevice,
  monitorAllDevices,
  getAllDevices,
  getLastCheckTime, // Redis'ten son kontrol zamanını almak için fonksiyon
  getNextCheckTime, // Redis'ten sonraki kontrol zamanını almak için fonksiyon
  updateQueryTimes // Redis'te kontrol zamanlarını güncellemek için fonksiyon
  // getLastQueryTime ve getNextQueryTime fonksiyonları kaldırıldı
};
