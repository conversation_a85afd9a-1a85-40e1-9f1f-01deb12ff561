/**
 * <PERSON><PERSON>az durumu hesaplama servisi
 */

/**
 * Cihaz durumunu hesaplar
 * 6-durumlu sistem: 4 temel durum + flapping + unknown
 * @param {Object} deviceData - Cihaz bilgileri
 * @param {Object} monitorResults - İzleme sonuçları
 * @returns {Object} - Hesaplanan durum bilgileri (status, reason)
 */
const calculateDeviceStatus = (deviceData, monitorResults) => {
  // 4 temel durum için hesaplama: Öncelik sistemi kaldırıldı, basit hiyerarşi kullanılıyor

  // Etkinleştirilmiş izleme protokollerini bul
  const monitors = deviceData.monitors || {};
  const enabledMonitors = [];
  const monitorStatuses = [];
  const monitorLastChecks = [];

  // ICMP kontrol et - sadece etkinleştirilmişse
  if (monitors.icmp?.enabled && monitorResults.icmp) {
    enabledMonitors.push('icmp');
    monitorStatuses.push(monitorResults.icmp.status);
    monitorLastChecks.push(parseInt(monitorResults.icmp.lastCheck || 0));
  }

  // Diğer protokolleri kontrol et
  const monitorTypes = ['http', 'tcp', 'dns', 'ssl'];
  monitorTypes.forEach(type => {
    if (monitors[type]?.enabled && monitorResults[type]) {
      enabledMonitors.push(type);
      monitorStatuses.push(monitorResults[type].status);
      monitorLastChecks.push(parseInt(monitorResults[type].lastCheck || 0));
    }
  });

  // Etkinleştirilmiş izleme yoksa 'unknown' döndür
  if (enabledMonitors.length === 0) {
    return {
      status: 'unknown',
      reason: 'Etkinleştirilmiş izleme yok'
    };
  }

  // Zaman bazlı kontrol kaldırıldı - sadece izleme türlerinin durumlarına göre hesaplama yapılacak
  // İzleme türlerinin son kontrol ve sonraki kontrol bilgileri korunuyor, ancak cihaz durumu hesaplamasında kullanılmıyor

  // Durum analizi (4-durumlu sistem için basitleştirildi)

  // 4-durumlu sistem için servisleri kategorize et
  const upMonitors = [];
  const warningMonitors = [];
  const criticalMonitors = [];
  const downMonitors = [];

  enabledMonitors.forEach((type, index) => {
    const status = monitorStatuses[index];

    if (status === 'up') {
      upMonitors.push(type.toUpperCase());
    } else if (status === 'warning') {
      warningMonitors.push(type.toUpperCase());
    } else if (status === 'critical') {
      criticalMonitors.push(type.toUpperCase());
    } else if (status === 'down') {
      downMonitors.push(type.toUpperCase());
    } else {
      // Bilinmeyen durumlar (unknown, timeout, vb.) down olarak işle
      // 4-status sisteminde sadece: up, warning, critical, down
      console.warn(`Unknown monitor status '${status}' for ${type}, treating as down`);
      downMonitors.push(type.toUpperCase());
    }
  });

  // 4 temel durum için durum ve sebep belirleme
  let status = 'unknown';
  let reason = '';

  // DOWN: Herhangi bir servis down durumunda
  if (downMonitors.length > 0) {
    status = 'down';
    reason = `${downMonitors.join(', ')} ${downMonitors.length === 1 ? 'servisi' : 'servisleri'} yanıt vermiyor`;
  }
  // CRITICAL: Herhangi bir servis critical durumunda
  else if (criticalMonitors.length > 0) {
    status = 'critical';
    reason = `${criticalMonitors.join(', ')} ${criticalMonitors.length === 1 ? 'servisi' : 'servisleri'} kritik durumda (yavaş yanıt/yüksek gecikme)`;
  }
  // WARNING: Herhangi bir servis warning durumunda
  else if (warningMonitors.length > 0) {
    status = 'warning';
    reason = `${warningMonitors.join(', ')} ${warningMonitors.length === 1 ? 'servisi' : 'servisleri'} uyarı durumunda (orta seviye gecikme)`;
  }
  // UP: Tüm servisler up durumunda
  else if (upMonitors.length > 0) {
    status = 'up';
    const totalServices = upMonitors.length;
    reason = `Tüm servisler çalışıyor (${totalServices} ${totalServices === 1 ? 'servis' : 'servis'}: ${upMonitors.join(', ')})`;
  }
  // Hiçbir izleme etkin değilse 'unknown' döndür
  else {
    status = 'unknown';
    reason = 'Hiçbir izleme etkin değil - Cihaz yapılandırmasını kontrol edin';
  }

  return { status, reason };
};

/**
 * Ham izleme verilerinden flapping tespiti yapar
 * @param {string} deviceId - Cihaz ID'si
 * @param {Object} redisClient - Redis istemcisi
 * @returns {Object} - Flapping analizi sonucu
 */
const analyzeFlappingFromRawData = async (deviceId, redisClient) => {
  try {
    // ICMP ham verilerini al (en temel izleme)
    const icmpHistoryKey = `history:icmp:${deviceId}`;
    const icmpHistory = await redisClient.lrange(icmpHistoryKey, 0, 19); // Son 20 kayıt

    if (icmpHistory.length < 8) {
      return { isFlapping: false, reason: 'Yeterli veri yok' };
    }

    // Ham ICMP durumlarını parse et
    const rawStatuses = icmpHistory.map(item => {
      try {
        const parsed = JSON.parse(item);
        return parsed.status;
      } catch (e) {
        return 'unknown';
      }
    }).filter(status => status !== 'unknown');

    if (rawStatuses.length < 8) {
      return { isFlapping: false, reason: 'Geçerli veri yetersiz' };
    }

    // Flapping analizi (HAM VERİLER ÜZERİNDE)
    const uniqueStatuses = new Set(rawStatuses);
    const statusVariety = uniqueStatuses.size;

    // Son 10 kayıtta kaç kez durum değişmiş?
    let changeCount = 0;
    for (let i = 1; i < Math.min(rawStatuses.length, 10); i++) {
      if (rawStatuses[i] !== rawStatuses[i - 1]) {
        changeCount++;
      }
    }

    // Son 5 kayıtta sürekli değişim var mı?
    const last5Statuses = rawStatuses.slice(0, 5);
    const hasConstantChange = last5Statuses.length >= 5 && last5Statuses.every((status, index) =>
      index === 0 || status !== last5Statuses[index - 1]
    );

    // FLAPPING KOŞULLARI (HAM VERİLER İÇİN) - Daha katı kriterler:
    const tooManyDifferentStatuses = statusVariety >= 4 && changeCount >= 5; // 4 farklı durum VE 5+ değişiklik
    const tooManyChanges = changeCount >= 8; // 8+ değişiklik (10 kayıtta) - daha katı
    const constantFluctuation = hasConstantChange;

    const isFlapping = tooManyDifferentStatuses || tooManyChanges || constantFluctuation;

    return {
      isFlapping,
      reason: isFlapping ?
        `Ham verilerde kararsızlık: ${statusVariety} farklı durum, ${changeCount} değişiklik` :
        'Ham veriler stabil',
      details: {
        statusSequence: rawStatuses.slice(0, 10),
        uniqueStatuses: Array.from(uniqueStatuses),
        statusVariety,
        changeCount,
        constantFluctuation,
        dataSource: 'ICMP raw data'
      }
    };
  } catch (error) {
    console.error(`Error analyzing flapping for device ${deviceId}:`, error);
    return { isFlapping: false, reason: 'Analiz hatası' };
  }
};

/**
 * Cihaz durumunu hesaplar ve Redis'e kaydeder
 * @param {string} deviceId - Cihaz ID'si
 * @param {Object} deviceData - Cihaz bilgileri
 * @param {Object} monitorResults - İzleme sonuçları
 * @param {Object} redisClient - Redis istemcisi
 * @returns {Object} - Hesaplanan durum bilgileri
 */
const calculateAndSaveDeviceStatus = async (deviceId, deviceData, monitorResults, redisClient) => {
  try {
    // 1. Ham durumu hesapla
    const { status, reason } = calculateDeviceStatus(deviceData, monitorResults);

    // 2. HAM VERİLERDEN flapping analizi yap
    const flappingAnalysis = await analyzeFlappingFromRawData(deviceId, redisClient);

    // 3. Final durumu belirle
    let finalStatus = status;
    let finalReason = reason;

    if (flappingAnalysis.isFlapping) {
      finalStatus = 'flapping';
      finalReason = flappingAnalysis.reason;

      console.log(`🚨 FLAPPING DETECTED (SCHEDULER) for device ${deviceId}:`, flappingAnalysis.details);
    } else {
      console.log(`✅ STABLE STATUS (SCHEDULER) for device ${deviceId}:`, {
        currentStatus: status,
        rawDataAnalysis: flappingAnalysis.details
      });
    }

    // 4. Hesaplanmış durumu geçmişe kaydet (sadece kayıt tutma amaçlı)
    const historyKey = `device:status:history:${deviceId}`;
    await redisClient.lpush(historyKey, JSON.stringify({
      status: finalStatus,
      reason: finalReason,
      rawStatus: status, // Ham hesaplanmış durum
      timestamp: Date.now()
    }));
    await redisClient.ltrim(historyKey, 0, 9); // Son 10 durumu tut


    // Redis'e kaydet
    await redisClient.hset(`device:status:${deviceId}`, {
      status: finalStatus,
      reason: finalReason,
      rawStatus: status // Hesaplanan ham durum
    });

    return { status: finalStatus, reason: finalReason };
  } catch (error) {
    console.error(`Error calculating device status for ${deviceId}:`, error);
    return {
      status: 'unknown',
      reason: 'Hesaplama sırasında hata oluştu: ' + error.message
    };
  }
};

/**
 * Cihaz durumunu Redis'ten alır
 * @param {string} deviceId - Cihaz ID'si
 * @param {Object} redisClient - Redis istemcisi
 * @returns {Object} - Durum bilgileri
 */
const getDeviceStatus = async (deviceId, redisClient) => {
  try {
    const statusData = await redisClient.hgetall(`device:status:${deviceId}`);
    if (!statusData) {
      return {
        status: 'unknown',
        reason: 'Durum bilgisi bulunamadı'
      };
    }
    return statusData;
  } catch (error) {
    console.error(`Error getting device status for ${deviceId}:`, error);
    return {
      status: 'unknown',
      reason: 'Durum bilgisi alınırken hata oluştu'
    };
  }
};

/**
 * Cihazın durum geçmişini Redis'ten alır
 * @param {string} deviceId - Cihaz ID'si
 * @param {Object} redisClient - Redis istemcisi
 * @param {number} limit - Kaç kayıt alınacağı
 * @returns {Array} - Durum geçmişi
 */
const getDeviceStatusHistory = async (deviceId, redisClient, limit = 10) => {
  try {
    const historyKey = `device:status:history:${deviceId}`;
    const history = await redisClient.lrange(historyKey, 0, limit - 1);

    if (!history || history.length === 0) {
      return [];
    }

    return history.map(item => JSON.parse(item));
  } catch (error) {
    console.error(`Error getting device status history for ${deviceId}:`, error);
    return [];
  }
};

module.exports = {
  calculateDeviceStatus,
  calculateAndSaveDeviceStatus,
  getDeviceStatus,
  getDeviceStatusHistory,
  analyzeFlappingFromRawData
};
