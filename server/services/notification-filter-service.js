/**
 * Bildirim Filtreleme Servisi
 * Kullanıcı ayarlarına göre bildirimleri filtreler
 */

class NotificationFilterService {
  constructor() {
    // ✅ Basit Severity Bazlı Filtreleme
    // Artık sadece severity'e göre filtreleme yapıyoruz
    this.severityLevels = {
      critical: 'critical',
      warning: 'warning',
      info: 'info',
      success: 'success'
    };
  }

  /**
   * ✅ Basit severity bazlı kategori belirleme
   * @param {Object} notification - Bildirim nesnesi
   * @returns {string} - 'critical', 'warning', 'info', 'success'
   */
  getNotificationUserCategory(notification) {
    const { severity } = notification;

    // ✅ Sadece severity'e göre basit kategori belirleme
    switch (severity) {
      case 'critical':
        return 'critical';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      case 'info':
      default:
        return 'info';
    }
  }

  /**
   * ✅ Kullanıcı ayarlarına göre bildirimi filtrele
   * @param {Object} notification - Bildir<PERSON> nes<PERSON>i
   * @param {Object} userSettings - <PERSON>llanı<PERSON><PERSON> ayarları
   * @returns {boolean} - Bildirim gösterilmeli mi?
   */
  shouldShowNotification(notification, userSettings) {
    if (!notification || !userSettings) {
      return true; // Güvenli varsayılan
    }

    // ✅ Severity bazlı filtreleme
    const severity = notification.severity || 'info';
    let severityAllowed = true;

    switch (severity) {
      case 'critical':
        severityAllowed = userSettings.notifyOnCritical !== false;
        break;
      case 'warning':
        severityAllowed = userSettings.notifyOnWarning !== false;
        break;
      case 'success':
        severityAllowed = userSettings.notifyOnSuccess !== false;
        break;
      case 'info':
      default:
        severityAllowed = userSettings.notifyOnInfo !== false;
        break;
    }

    // ✅ Source bazlı filtreleme
    const sourceType = notification.source?.type || notification.category || 'system';
    let sourceAllowed = true;

    switch (sourceType) {
      case 'device':
        sourceAllowed = userSettings.notifyOnDevice !== false;
        break;
      case 'system':
      default:
        sourceAllowed = userSettings.notifyOnSystem !== false;
        break;
    }

    // Her iki filtreyi de geçmeli
    return severityAllowed && sourceAllowed;
  }

  /**
   * Bildirim listesini kullanıcı ayarlarına göre filtrele
   * @param {Array} notifications - Bildirim listesi
   * @param {Object} userSettings - Kullanıcı ayarları
   * @returns {Array} - Filtrelenmiş bildirim listesi
   */
  filterNotifications(notifications, userSettings) {
    if (!Array.isArray(notifications)) {
      return [];
    }

    if (!userSettings) {
      return notifications; // Ayarlar yoksa tümünü göster
    }

    return notifications.filter(notification =>
      this.shouldShowNotification(notification, userSettings)
    );
  }

  /**
   * ✅ Bildirim istatistiklerini severity'e göre hesapla
   * @param {Array} notifications - Bildirim listesi
   * @returns {Object} - Severity bazlı istatistikler
   */
  getNotificationStats(notifications) {
    const stats = {
      critical: 0,
      warning: 0,
      info: 0,
      success: 0,
      total: notifications.length
    };

    notifications.forEach(notification => {
      const severity = notification.severity || 'info';
      if (stats.hasOwnProperty(severity)) {
        stats[severity]++;
      } else {
        stats.info++; // Bilinmeyen severity'ler info olarak sayılır
      }
    });

    return stats;
  }

  /**
   * ✅ Test için severity seviyelerini döndür
   * @returns {Object} - Severity seviyeleri
   */
  getSeverityLevels() {
    return this.severityLevels;
  }

  /**
   * ✅ Test bildirimi oluştur - Basit severity sistemi
   * @param {string} severity - 'critical', 'warning', 'info', 'success'
   * @param {string} title - Bildirim başlığı
   * @param {string} message - Bildirim mesajı
   * @returns {Object} - Test bildirimi
   */
  createTestNotification(severity = 'info', title, message) {
    // ✅ Basit severity doğrulama
    const validSeverities = ['critical', 'warning', 'info', 'success'];
    const validatedSeverity = validSeverities.includes(severity) ? severity : 'info';

    // ✅ Basit source türü belirleme
    const sourceType = ['critical', 'warning'].includes(validatedSeverity) ? 'device' : 'system';

    // ✅ Gerçek cihaz ID'si kullan (Facebook cihazı)
    const realDeviceId = 'c9dc9d58-eba0-4dde-bdbd-2d75d2a14fa4';

    return {
      id: `test-${Date.now()}`,
      title: title || `Test ${validatedSeverity} bildirimi`,
      message: message || `Bu bir test ${validatedSeverity} bildirimidir`,
      severity: validatedSeverity,
      category: sourceType, // ✅ Basit kategori (device/system)
      timestamp: new Date().toISOString(),
      source: {
        id: sourceType === 'device' ? realDeviceId : 'test-system',
        name: sourceType === 'device' ? 'Facebook' : 'Test Sistem',
        type: sourceType
      }
    };
  }
}

// Singleton instance
const notificationFilterService = new NotificationFilterService();

module.exports = notificationFilterService;
