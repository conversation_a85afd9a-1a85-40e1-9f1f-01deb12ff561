let io;

/**
 * JSON parse işlemini güvenli bir şekilde yapar
 * @param {string} str - JSON string
 * @param {Object} defaultValue - Hata durumunda dönecek varsay<PERSON>lan değer
 * @returns {Object} - Parse edilmiş nesne veya varsayılan değer
 */
const safeJsonParse = (str, defaultValue = {}) => {
  if (!str || typeof str !== 'string') return defaultValue;
  try {
    return JSON.parse(str);
  } catch (e) {
    console.error('Error parsing JSON:', e);
    return defaultValue;
  }
};

/**
 * Cihaz durumunu ve sorgu zamanlarını alır
 * @param {string} deviceId - Cihaz ID
 * @param {string} type - İzle<PERSON> türü
 * @param {Object} redis - Redis istemcisi
 * @returns {Promise<Object>} - Durum ve sorgu zamanları
 */
const getMonitorStatus = async (deviceId, type, redis) => {
  try {
    // <PERSON>zleme türü durumunu al - doğru anahtarı kullan
    const statusData = await redis.hgetall(`monitor:${type}:${deviceId}`);

    if (statusData && Object.keys(statusData).length > 0) {
      // JSON parse işlemlerini yap
      if (statusData.details && typeof statusData.details === 'string') {
        statusData.details = safeJsonParse(statusData.details);
      }

      // Sayısal değerleri dönüştür
      if (statusData.lastCheck) statusData.lastCheck = parseInt(statusData.lastCheck);
      if (statusData.nextCheck) statusData.nextCheck = parseInt(statusData.nextCheck);
      if (statusData.responseTime) statusData.responseTime = parseInt(statusData.responseTime);

      // Geriye dönük uyumluluk için lastQueryTime ve nextQueryTime değerlerini kaldır
      delete statusData.lastQueryTime;
      delete statusData.nextQueryTime;
    }

    return statusData || {};
  } catch (error) {
    console.error(`Error getting monitor status for ${type} of device ${deviceId}:`, error);
    return {};
  }
};

/**
 * Socket.io'yu başlatır
 * @param {Object} server - HTTP server
 */
const init = (server) => {
  io = require('socket.io')(server, {
    cors: {
      origin: '*', // Tüm originlere izin ver
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      credentials: false,
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    },
    transports: ['websocket', 'polling'],
    pingTimeout: 60000, // Ping zaman aşımı süresi
    pingInterval: 25000, // Ping aralığı
    connectTimeout: 60000, // Bağlantı zaman aşımı süresi
    maxHttpBufferSize: 1e8 // Maksimum HTTP buffer boyutu (100MB)
  });

  io.on('connection', (socket) => {
    console.log(`Yeni bağlantı: ${socket.id}`);

    // Kullanıcı girişi olayını dinle
    socket.on('user:login', async (data) => {
      console.log(`🔐 KULLANICI GİRİŞİ: ${data.userId}, Socket ID: ${socket.id}`);

      try {
        // Kullanıcıyı socket'e kaydet
        socket.userId = data.userId;

        // Kullanıcı odalarına katıl
        socket.join(`user:${data.userId}`);

        console.log(`✅ Kullanıcı ${data.userId} başarıyla socket'e kaydedildi ve user:${data.userId} odasına katıldı`);
        console.log(`📊 Socket ${socket.id} için userId: ${socket.userId}`);

        // Kullanıcıya başarı mesajı gönder
        socket.emit('user:login:response', {
          success: true,
          message: `Kullanıcı girişi başarılı`,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('❌ Kullanıcı girişi sırasında hata:', error);
        socket.emit('user:login:response', {
          success: false,
          error: error.message,
          timestamp: Date.now()
        });
      }
    });

    // Belirli cihazları kontrol etme olayını dinle
    socket.on('device:check', async (data) => {
      try {
        const { deviceIds, monitorTypes } = data;
        console.log(`Cihaz kontrol isteği: ${deviceIds?.length || 0} cihaz, ${monitorTypes?.length || 0} izleme türü`);

        // Modülleri bir kez yükle
        const scheduler = require('./scheduler');
        const redis = require('../config/redis');

        // Cihazları kontrol et - manuel kontrol olduğunu belirten isManualCheck parametresini true olarak geçerek
        // bu, kontrol aralığını etkilemeden hemen kontrol yapılmasını sağlayacak
        const result = await scheduler.monitorAllDevices(io, deviceIds || [], monitorTypes || [], true, true);

        // Kullanıcıya başarı mesajı gönder
        socket.emit('device:check:response', {
          success: true,
          message: `${result.count} cihaz kontrol edildi`,
          deviceIds: deviceIds || [],
          monitorTypes: monitorTypes || [],
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Cihaz kontrolü sırasında hata:', error);
        socket.emit('device:check:response', {
          success: false,
          error: error.message,
          timestamp: Date.now()
        });
      }
    });

    // Mevcut durumları almak için yeni bir olay dinle
    socket.on('device:status:get', async () => {
      try {
        console.log(`Mevcut durumlar istendi: ${socket.id}`);

        // Redis bağlantısını bir kez al
        const redis = require('../config/redis');

        // Tüm cihazları al
        const devices = await redis.smembers('devices');
        const monitorTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl'];

        // Her cihaz için mevcut durumları al
        for (const deviceId of devices) {
          try {
            // Cihaz durumunu al
            const statusKey = `device:${deviceId}:status`;
            const statusExists = await redis.exists(statusKey);

            if (statusExists) {
              // Tüm izleme türleri için durumları paralel olarak al
              const monitorPromises = monitorTypes.map(type =>
                getMonitorStatus(deviceId, type, redis)
                  .then(statusData => {
                    if (statusData && Object.keys(statusData).length > 0) {
                      // Durumu istemciye gönder
                      socket.emit('monitor:update', { deviceId, type, data: statusData });
                    }
                    return statusData;
                  })
              );

              // Hesaplanmış durumu al (doğru key)
              const calculatedStatusPromise = redis.hgetall(`device:status:${deviceId}`);

              // Tüm promise'ları bekle
              const [calculatedStatus] = await Promise.all([
                calculatedStatusPromise,
                ...monitorPromises
              ]);

              // Hesaplanmış durumu gönder
              if (calculatedStatus && Object.keys(calculatedStatus).length > 0) {
                socket.emit('device:status:update', {
                  deviceId,
                  calculatedStatus: calculatedStatus.status,
                  reason: calculatedStatus.reason,
                  rawStatus: calculatedStatus.rawStatus,
                  lastCalculated: calculatedStatus.timestamp
                });
              }
            }
          } catch (error) {
            console.error(`Error getting status for device ${deviceId}:`, error);
          }
        }

        console.log(`Mevcut durumlar gönderildi: ${socket.id}`);
      } catch (error) {
        console.error('Error getting device statuses:', error);
      }
    });

    socket.on('disconnect', () => {
      console.log(`Bağlantı kesildi: ${socket.id}${socket.userId ? `, Kullanıcı: ${socket.userId}` : ''}`);

      // Kullanıcı odalarından çık
      if (socket.userId) {
        socket.leave(`user:${socket.userId}`);
      }
    });
  });

  return io;
};

/**
 * Socket.io nesnesini döndürür
 * @returns {Object} - Socket.io nesnesi
 */
const getIO = () => {
  if (!io) {
    throw new Error('Socket.io henüz başlatılmadı');
  }
  return io;
};

/**
 * Tüm bağlı istemcilere olay gönderir
 * @param {string} event - Olay adı
 * @param {*} data - Gönderilecek veri
 */
const emitToAll = (event, data) => {
  if (!io) {
    console.warn('Socket.io henüz başlatılmadı, olay gönderilemedi');
    return;
  }
  io.emit(event, data);
};

/**
 * Belirli bir istemciye olay gönderir
 * @param {string} socketId - Socket ID
 * @param {string} event - Olay adı
 * @param {*} data - Gönderilecek veri
 */
const emitToClient = (socketId, event, data) => {
  if (!io) {
    console.warn('Socket.io henüz başlatılmadı, olay gönderilemedi');
    return;
  }
  io.to(socketId).emit(event, data);
};

/**
 * Belirli bir odaya olay gönderir
 * @param {string} room - Oda adı
 * @param {string} event - Olay adı
 * @param {*} data - Gönderilecek veri
 */
const emitToRoom = (room, event, data) => {
  if (!io) {
    console.warn('Socket.io henüz başlatılmadı, olay gönderilemedi');
    return;
  }
  io.to(room).emit(event, data);
};

/**
 * Belirli bir kullanıcıya olay gönderir
 * @param {string} userId - Kullanıcı ID'si
 * @param {string} event - Olay adı
 * @param {*} data - Gönderilecek veri
 */
const emitToUser = (userId, event, data) => {
  if (!io) {
    console.warn('Socket.io henüz başlatılmadı, olay gönderilemedi');
    return;
  }
  io.to(`user:${userId}`).emit(event, data);
  console.log(`Socket event gönderildi kullanıcıya ${userId}: ${event}`);
};

/**
 * Kullanıcının aktif oturumlarını sonlandırır
 * @param {string} userId - Kullanıcı ID'si
 * @param {string} reason - Çıkış nedeni
 */
const forceUserLogout = (userId, reason = 'Hesabınız yönetici tarafından devre dışı bırakıldı') => {
  if (!io) {
    console.warn('Socket.io henüz başlatılmadı, kullanıcı çıkışı yapılamadı');
    return;
  }

  console.log(`🚨 ZORLA ÇIKIŞ: Kullanıcı ${userId} zorla çıkış yaptırılıyor: ${reason}`);

  // Kullanıcının tüm socket bağlantılarını kontrol et
  const userRoom = `user:${userId}`;
  const sockets = io.sockets.adapter.rooms.get(userRoom);

  console.log(`🔍 Kullanıcı ${userId} için aktif socket'ler:`, sockets ? Array.from(sockets) : 'Hiç aktif socket yok');

  // Kullanıcıya çıkış olayı gönder
  console.log(`📡 Kullanıcı ${userId}'ye force-logout olayı gönderiliyor...`);
  emitToUser(userId, 'user:force-logout', {
    reason,
    timestamp: Date.now()
  });

  // Kullanıcının tüm socket bağlantılarını kes
  if (sockets && sockets.size > 0) {
    console.log(`🔌 ${sockets.size} adet socket bağlantısı kesiliyor...`);
    sockets.forEach(socketId => {
      const socket = io.sockets.sockets.get(socketId);
      if (socket) {
        console.log(`❌ Socket bağlantısı kesiliyor: ${socketId} (Kullanıcı: ${userId})`);
        socket.disconnect(true);
      }
    });
  } else {
    console.log(`⚠️ Kullanıcı ${userId} için aktif socket bağlantısı bulunamadı`);
  }

  // Tüm aktif socket'leri listele (debug için)
  console.log(`📊 Toplam aktif socket sayısı: ${io.sockets.sockets.size}`);
  const allSockets = Array.from(io.sockets.sockets.values());
  allSockets.forEach(socket => {
    console.log(`🔗 Aktif socket: ${socket.id}, Kullanıcı: ${socket.userId || 'Bilinmiyor'}`);
  });
};

module.exports = {
  init,
  getIO,
  emitToAll,
  emitToClient,
  emitToRoom,
  emitToUser,
  forceUserLogout
};
