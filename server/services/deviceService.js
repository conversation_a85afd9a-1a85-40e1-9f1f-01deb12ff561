/**
 * <PERSON><PERSON>az yönetimi i<PERSON>in servis
 */
const redisClient = require('./redis');
const notificationService = require('./notificationService');

/**
 * JSON parse işlemini güvenli bir şekilde yapar
 * @param {string} str - JSON string
 * @param {Object|Array} defaultValue - Hata durumunda dönecek varsayılan değer
 * @returns {Object|Array} - Parse edilmiş nesne veya varsayılan değer
 */
const safeJsonParse = (str, defaultValue) => {
  if (!str || typeof str !== 'string') return defaultValue;
  try {
    return JSON.parse(str);
  } catch (e) {
    console.error('Error parsing JSON:', e);
    return defaultValue;
  }
};

/**
 * Tüm cihazları getirir
 * @returns {Promise<Array>} - Cihazlar dizisi
 */
const getAllDevices = async () => {
  try {
    // Tüm cihaz ID'lerini al
    const deviceIds = await redisClient.smembers('devices');

    // Her cihazın detaylarını paralel olarak al
    const devicePromises = deviceIds.map(async (id) => {
      const deviceData = await redisClient.hgetall(`device:${id}`);
      if (deviceData) {
        // JSON alanlarını parse et
        deviceData.monitors = safeJsonParse(deviceData.monitors, {});
        deviceData.alerts = safeJsonParse(deviceData.alerts, []);

        return {
          id,
          ...deviceData
        };
      }
      return null;
    });

    // Tüm promise'ları bekle ve null olanları filtrele
    const devices = (await Promise.all(devicePromises)).filter(device => device !== null);

    return devices;
  } catch (error) {
    console.error('Error getting all devices:', error);
    throw error;
  }
};

/**
 * Belirli bir cihazı getirir
 * @param {string} id - Cihaz ID
 * @returns {Promise<Object>} - Cihaz nesnesi
 */
const getDeviceById = async (id) => {
  try {
    // Cihazın var olup olmadığını ve detaylarını paralel olarak al
    const [exists, deviceData] = await Promise.all([
      redisClient.sismember('devices', id),
      redisClient.hgetall(`device:${id}`)
    ]);

    if (!exists || !deviceData) {
      return null;
    }

    // JSON alanlarını parse et
    deviceData.monitors = safeJsonParse(deviceData.monitors, {});
    deviceData.alerts = safeJsonParse(deviceData.alerts, []);

    return {
      id,
      ...deviceData
    };
  } catch (error) {
    console.error(`Error getting device by ID ${id}:`, error);
    throw error;
  }
};

/**
 * Tüm cihaz gruplarını getirir
 * @returns {Promise<Array>} - Gruplar dizisi
 */
const getAllGroups = async () => {
  try {
    return await redisClient.smembers('device:groups');
  } catch (error) {
    console.error('Error getting all device groups:', error);
    throw error;
  }
};

/**
 * Yeni bir cihaz oluşturur
 * @param {Object} deviceData - Cihaz verileri
 * @returns {Promise<Object>} - Oluşturulan cihaz
 */
const createDevice = async (deviceData) => {
  try {
    const { id, name, host, description, group, monitors, alerts } = deviceData;

    // Cihazı Redis'e kaydet
    await redisClient.hmset(`device:${id}`, {
      name,
      host,
      description: description || '',
      group: group || 'Default',
      monitors: JSON.stringify(monitors || {}),
      alerts: JSON.stringify(alerts || []),
      createdAt: Date.now()
    });

    // Cihaz ID'sini cihazlar kümesine ekle
    await redisClient.sadd('devices', id);

    // Cihaz grubunu gruplar kümesine ekle
    if (group) {
      await redisClient.sadd('device:groups', group);
    }

    return {
      id,
      name,
      host,
      description: description || '',
      group: group || 'Default',
      monitors: monitors || {},
      alerts: alerts || [],
      createdAt: Date.now()
    };
  } catch (error) {
    console.error('Error creating device:', error);
    throw error;
  }
};

/**
 * Bir cihazı günceller
 * @param {string} id - Cihaz ID
 * @param {Object} deviceData - Güncellenecek cihaz verileri
 * @returns {Promise<Object>} - Güncellenen cihaz
 */
const updateDevice = async (id, deviceData) => {
  try {
    const { name, host, description, group, monitors, alerts } = deviceData;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      throw new Error('Device not found');
    }

    // Cihazı güncelle
    await redisClient.hmset(`device:${id}`, {
      name,
      host,
      description: description || '',
      group: group || 'Default',
      monitors: JSON.stringify(monitors || {}),
      alerts: JSON.stringify(alerts || []),
      updatedAt: Date.now()
    });

    // Cihaz grubunu gruplar kümesine ekle
    if (group) {
      await redisClient.sadd('device:groups', group);
    }

    return {
      id,
      name,
      host,
      description: description || '',
      group: group || 'Default',
      monitors: monitors || {},
      alerts: alerts || [],
      updatedAt: Date.now()
    };
  } catch (error) {
    console.error(`Error updating device ${id}:`, error);
    throw error;
  }
};

/**
 * Bir cihazı siler
 * @param {string} id - Cihaz ID
 * @returns {Promise<boolean>} - Başarılı ise true
 */
const deleteDevice = async (id) => {
  try {
    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      throw new Error('Device not found');
    }

    // Cihazı sil
    await redisClient.del(`device:${id}`);
    await redisClient.srem('devices', id);

    // İzleme verilerini sil
    const monitorTypes = ['icmp', 'http', 'tcp', 'dns', 'ssl'];
    for (const type of monitorTypes) {
      await redisClient.del(`monitor:${type}:${id}`);
      await redisClient.del(`monitor:history:${type}:${id}`);
      await redisClient.del(`history:${type}:${id}`);
    }

    // Cihazla ilgili bildirimleri sil
    try {
      const deleteResult = await notificationService.deleteDeviceNotifications(id);
      console.log(`Cihaz ${id} için ${deleteResult.count} bildirim silindi`);
    } catch (error) {
      console.error(`Cihaz ${id} bildirimleri silinirken hata:`, error);
      // Bildirim silme hatası cihaz silme işlemini durdurmasın
    }

    return true;
  } catch (error) {
    console.error(`Error deleting device ${id}:`, error);
    throw error;
  }
};

module.exports = {
  getAllDevices,
  getDeviceById,
  getAllGroups,
  createDevice,
  updateDevice,
  deleteDevice
};
