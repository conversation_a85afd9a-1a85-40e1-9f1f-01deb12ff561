/**
 * HTTP izleme servisi
 */
const axios = require('axios');
const BaseMonitor = require('./base-monitor');
const { getThresholds } = require('../../config/monitor-thresholds');

class HttpMonitor extends BaseMonitor {
  constructor() {
    super('http');
  }

  /**
   * HTTP status kodunu kategoriye göre değerlendirir
   * 4-durumlu sistem: up, warning, critical, down
   * @param {number} statusCode - HTTP status kodu
   * @param {number} responseTime - Yanıt süresi (ms)
   * @param {string} error - Hata mesajı (varsa)
   * @returns {Object} - Kategori ve mesaj bilgisi
   */
  categorizeHttpStatus(statusCode, responseTime = 0, error = null) {
    // Bağlantı hatası
    if (statusCode === 0 || error) {
      if (error && error.includes('timeout')) {
        return { category: 'down', message: '<PERSON>ğlantı zaman aşımı' };
      }
      if (error && error.includes('ENOTFOUND')) {
        return { category: 'critical', message: 'DNS çözümlenemiyor' };
      }
      if (error && error.includes('ECONNREFUSED')) {
        return { category: 'critical', message: 'Bağlantı reddedildi' };
      }
      return { category: 'down', message: 'Bağlantı kurulamadı' };
    }

    // Yanıt süresi bazlı durum belirleme (başarılı durum kodları için)
    if (statusCode >= 200 && statusCode < 400) {
      const thresholds = getThresholds('http');

      if (responseTime > thresholds.critical) {
        return { category: 'critical', message: `Çok yavaş yanıt (>${thresholds.critical}ms)` };
      }
      if (responseTime > thresholds.warning) {
        return { category: 'warning', message: `Yavaş yanıt (>${thresholds.warning}ms)` };
      }
      if (statusCode === 301 || statusCode === 302) {
        return { category: 'up', message: 'Yönlendiriliyor (Normal)' };
      }
      return { category: 'up', message: 'Çevrimiçi' };
    }

    // 1xx Bilgi yanıtları - Uyarı olarak değerlendir
    if (statusCode >= 100 && statusCode < 200) {
      return { category: 'warning', message: `Bilgi yanıtı: HTTP ${statusCode}` };
    }

    // Kritik - Önemli istemci hataları
    if (statusCode === 401 || statusCode === 403) {
      return {
        category: 'critical',
        message: statusCode === 401 ? 'Kimlik doğrulama gerekli' : 'Erişim yasak'
      };
    }

    // Uyarı - Diğer istemci hataları (400-499)
    if (statusCode >= 400 && statusCode < 500) {
      const messages = {
        400: 'Hatalı istek - URL\'yi kontrol edin',
        404: 'Sayfa bulunamadı - URL\'yi kontrol edin',
        405: 'HTTP metodunu kontrol edin',
        429: 'Çok fazla istek - Aralığı artırın'
      };
      return {
        category: 'warning',
        message: messages[statusCode] || `HTTP ${statusCode} - İstemci hatası`
      };
    }

    // Çevrimdışı - Sunucu hataları (500-599)
    if (statusCode >= 500) {
      const messages = {
        500: 'Sunucu hatası',
        502: 'Proxy hatası',
        503: 'Servis kullanılamıyor',
        504: 'Sunucu zaman aşımı'
      };
      return {
        category: 'down',
        message: messages[statusCode] || `HTTP ${statusCode} - Sunucu hatası`
      };
    }

    // Diğer tüm bilinmeyen durum kodları - Kritik olarak değerlendir
    // Bu durumda HTTP protokolü dışında bir şey olmuş demektir
    return { category: 'critical', message: `Beklenmeyen HTTP durum kodu: ${statusCode}` };
  }

  /**
   * HTTP sonuçlarına göre 4-durumlu sistem durumu belirler
   * @param {Object} httpResult - HTTP sonuçları
   * @returns {string} - up, warning, critical, down
   */
  determineHttpStatus(httpResult) {
    // Bağlantı kurulamadıysa DOWN
    if (!httpResult.success || httpResult.category === 'down') {
      return 'down';
    }

    // Kategori bazlı durum belirleme
    switch (httpResult.category) {
      case 'critical':
        return 'critical';
      case 'warning':
        return 'warning';
      case 'up':
        return 'up';
      default:
        // Bu duruma hiç gelmemeli ama güvenlik için
        return 'critical';
    }
  }

  /**
   * HTTP durumu için açıklayıcı mesaj oluşturur
   * @param {string} status - Durum (up, warning, critical, down)
   * @param {Object} httpResult - HTTP sonuçları
   * @param {string} url - Kontrol edilen URL
   * @returns {string} - Açıklayıcı mesaj
   */
  getHttpStatusMessage(status, httpResult, url) {
    const responseTime = httpResult.responseTime || 0;
    const statusCode = httpResult.status || 0;

    switch (status) {
      case 'down':
        if (httpResult.error) {
          return `HTTP isteği başarısız: ${httpResult.message} (${url})`;
        }
        return `HTTP ${statusCode} - Servis yanıt vermiyor (${url})`;

      case 'critical':
        const criticalThreshold = getThresholds('http').critical;
        if (responseTime > criticalThreshold) {
          return `HTTP çok yavaş: ${responseTime}ms yanıt süresi (>${criticalThreshold}ms) - ${url}`;
        }
        return `HTTP ${statusCode} - ${httpResult.message} (${url})`;

      case 'warning':
        const warningThreshold = getThresholds('http').warning;
        if (responseTime > warningThreshold) {
          return `HTTP yavaş: ${responseTime}ms yanıt süresi (>${warningThreshold}ms) - ${url}`;
        }
        return `HTTP ${statusCode} - ${httpResult.message} (${url})`;

      case 'up':
        return `HTTP ${statusCode} başarılı: ${responseTime}ms yanıt süresi (${url})`;

      default:
        return 'HTTP durumu bilinmiyor';
    }
  }

  /**
   * HTTP isteği gönderir ve yanıtı kontrol eder
   * @param {Object} options - HTTP isteği seçenekleri
   * @returns {Promise<Object>} - HTTP isteği sonuçları
   */
  async checkHttp(options) {
    const startTime = Date.now();

    try {
      const response = await axios({
        method: options.method || 'GET',
        url: options.url,
        timeout: options.timeout || 5000,
        headers: options.headers || {},
        validateStatus: () => true // Tüm durum kodlarını kabul et
      });

      const responseTime = Date.now() - startTime;

      // Status kategorisini belirle
      const statusInfo = this.categorizeHttpStatus(response.status, responseTime);

      return {
        url: options.url,
        status: response.status,
        statusText: response.statusText,
        responseTime,
        success: statusInfo.category === 'up',
        category: statusInfo.category,
        message: statusInfo.message,
        contentLength: response.headers['content-length'] || 0,
        contentType: response.headers['content-type'] || '',
        error: null
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const statusCode = error.response ? error.response.status : 0;
      const statusInfo = this.categorizeHttpStatus(statusCode, responseTime, error.message);

      return {
        url: options.url,
        status: statusCode,
        statusText: error.response ? error.response.statusText : error.message,
        responseTime,
        success: false,
        category: statusInfo.category,
        message: statusInfo.message,
        contentLength: 0,
        contentType: '',
        error: error.message
      };
    }
  }

  /**
   * Bir cihazın HTTP durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // HTTP izleme yapılandırmasını al
      const config = device.monitors?.http || {};

      const options = {
        url: config.url || `http://${device.host}`,
        method: config.method || 'GET',
        timeout: config.timeout || 5000,
        headers: config.headers || {}
      };

      console.log(`HTTP check for ${device.name}: Using URL ${options.url} with timeout ${options.timeout}ms`);

      // HTTP isteğini yap
      const httpResult = await this.checkHttp(options);

      // 4-durumlu sistem için durum belirleme
      const status = this.determineHttpStatus(httpResult);

      // Durum açıklaması oluştur
      const message = this.getHttpStatusMessage(status, httpResult, options.url);

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: status, // 4-durumlu sistem: 'up', 'warning', 'critical', 'down'
        responseTime: httpResult.responseTime,
        error: httpResult.error || '',
        message: message, // Kullanıcı dostu mesaj
        details: {
          url: httpResult.url,
          statusCode: httpResult.status,
          statusText: httpResult.statusText,
          contentLength: httpResult.contentLength,
          contentType: httpResult.contentType,
          category: httpResult.category,
          success: httpResult.success
        }
      };

      // ✅ Artık sadece sonuç döndürüyoruz, kaydetmiyoruz
      // Scheduler bu sonucu alıp Redis'e kaydedecek
      return result; // result objesi message alanını içeriyor
    } catch (error) {
      console.error(`HTTP monitoring error for ${device.host}:`, error);

      // ✅ Hata durumunda da sadece sonuç döndürüyoruz
      // Scheduler hata durumunu da işleyecek

      return {
        status: 'down', // 4-durumlu sistem: Hata durumunda down
        responseTime: 0,
        error: error.message,
        message: `HTTP izleme hatası: ${error.message}`,
        details: {
          url: device.host,
          statusCode: 0,
          statusText: error.message,
          contentLength: 0,
          contentType: '',
          category: 'down',
          success: false
        }
      };
    }
  }
}

// Singleton instance oluştur
const httpMonitor = new HttpMonitor();

module.exports = {
  checkHttp: (options) => httpMonitor.checkHttp(options),
  monitorDevice: (device, io) => httpMonitor.monitorDevice(device, io),
  getResults: (deviceId) => httpMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => httpMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => httpMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => httpMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => httpMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => httpMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
