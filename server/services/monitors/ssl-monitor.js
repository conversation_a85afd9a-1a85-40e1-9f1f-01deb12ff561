/**
 * SSL izleme servisi
 * ssl-checker küt<PERSON><PERSON><PERSON><PERSON> kullanılarak modernize edildi
 */
const sslChecker = require('ssl-checker');
const { exec } = require('child_process');
const BaseMonitor = require('./base-monitor');

class SslMonitor extends BaseMonitor {
  constructor() {
    super('ssl');
  }

  /**
   * SSL sertifikasının başlangıç ve bitiş tarihlerini kontrol eder
   * @param {string} host - Kontrol edilecek host
   * @param {Object} options - SSL kontrol seçenekleri
   * @returns {Promise<Object>} - SSL kontrol sonuçları
   */
  async checkSslCertificate(host, options = {}) {
    const startTime = Date.now();
    const result = {
      status: 'unknown',
      responseTime: 0,
      error: null,
      details: {}
    };

    try {
      // Host adresinden www. ve https:// gibi önekleri temizle
      const cleanHost = host.replace(/^https?:\/\//i, '').replace(/^www\./i, '');
      const port = options.port || 443;

      console.log(`SSL check for host: ${host}, cleaned host: ${cleanHost}:${port}`);

      // ssl-checker kütüphanesini kullan (daha güvenilir ve hızlı)
      try {
        const certInfo = await sslChecker(cleanHost, {
          method: 'GET',
          port: port,
          protocol: 'https:',
          timeout: options.timeout || 10000
        });

        result.responseTime = Date.now() - startTime;
        result.status = this.determineSslStatus(certInfo.daysRemaining);
        result.details = {
          validFrom: certInfo.validFrom,
          validTo: certInfo.validTo,
          daysRemaining: certInfo.daysRemaining,
          expiryStatus: this.getExpiryStatus(certInfo.daysRemaining),
          issuer: certInfo.issuer,
          subject: certInfo.subject,
          serialNumber: certInfo.serialNumber,
          fingerprint: certInfo.fingerprint,
          keySize: certInfo.keySize,
          algorithm: certInfo.algorithm
        };

        return result;
      } catch (sslCheckerError) {
        console.log(`ssl-checker failed for ${cleanHost}, trying fallback TLS: ${sslCheckerError.message}`);

        // ssl-checker başarısız olursa fallback olarak TLS kullan
        const certInfo = await this.checkWithTLS(cleanHost, port);

        result.responseTime = Date.now() - startTime;
        result.status = this.determineSslStatus(certInfo.daysRemaining);
        result.details = {
          validFrom: certInfo.validFrom,
          validTo: certInfo.validTo,
          daysRemaining: certInfo.daysRemaining,
          expiryStatus: this.getExpiryStatus(certInfo.daysRemaining)
        };
      }

    } catch (err) {
      result.responseTime = Date.now() - startTime;
      result.status = 'down';
      result.error = err.message;

      // Hata durumunda boş detaylar ekle
      result.details = {
        validFrom: null,
        validTo: null,
        daysRemaining: 0,
        expiryStatus: 'expired'
      };
    }

    return result;
  }

  /**
   * TLS kullanarak SSL sertifika bilgilerini al
   * @param {string} host - Kontrol edilecek host
   * @param {number} port - Kontrol edilecek port
   * @returns {Promise<Object>} - Sertifika bilgileri
   */
  async checkWithTLS(host, port = 443) {
    return new Promise((resolve, reject) => {
      const tls = require('tls');

      // TLS bağlantısı oluştur
      const socket = tls.connect({
        host: host,
        port: port,
        rejectUnauthorized: false,
        timeout: 10000
      });

      // Hata durumunu dinle
      socket.on('error', (err) => {
        console.error(`TLS connection error for ${host}:`, err.message);
        reject(err);
      });

      // Zaman aşımı durumunu dinle
      socket.setTimeout(10000, () => {
        socket.destroy();
        reject(new Error('TLS connection timeout'));
      });

      // Bağlantı kurulduğunda
      socket.on('secureConnect', () => {
        try {
          const cert = socket.getPeerCertificate();
          console.log(`TLS certificate for ${host}:`, Object.keys(cert));

          if (Object.keys(cert).length === 0) {
            reject(new Error('No certificate found'));
            return;
          }

          // Sertifika tarihlerini al
          const validFrom = cert.valid_from;
          const validTo = cert.valid_to;

          // Kalan gün sayısını hesapla
          const daysRemaining = this.getDaysRemaining(validTo);

          // Bağlantıyı kapat
          socket.end();

          resolve({
            validFrom,
            validTo,
            daysRemaining
          });
        } catch (err) {
          reject(err);
        }
      });
    });
  }

  /**
   * OpenSSL kullanarak SSL sertifika bilgilerini al
   * @param {string} host - Kontrol edilecek host
   * @param {number} port - Kontrol edilecek port
   * @returns {Promise<Object>} - Sertifika bilgileri
   */
  async checkWithOpenSSL(host, port = 443) {
    return new Promise((resolve, reject) => {
      console.log(`Checking SSL certificate for ${host}:${port} using OpenSSL`);

      // OpenSSL komutunu çalıştır
      const command = `echo | openssl s_client -servername ${host} -connect ${host}:${port} 2>/dev/null | openssl x509 -noout -dates`;

      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.error(`OpenSSL error: ${error.message}`);
          reject(error);
          return;
        }

        if (stderr) {
          console.error(`OpenSSL stderr: ${stderr}`);
        }

        console.log(`OpenSSL stdout: ${stdout}`);

        // Çıktıyı işle
        const notBeforeMatch = stdout.match(/notBefore=(.+)/);
        const notAfterMatch = stdout.match(/notAfter=(.+)/);

        if (!notBeforeMatch || !notAfterMatch) {
          reject(new Error('Could not parse certificate dates'));
          return;
        }

        const validFrom = notBeforeMatch[1];
        const validTo = notAfterMatch[1];

        // Kalan gün sayısını hesapla
        const daysRemaining = this.getDaysRemaining(validTo);

        resolve({
          validFrom,
          validTo,
          daysRemaining
        });
      });
    });
  }

  /**
   * Sertifikanın geçerlilik süresinin bitimine kalan gün sayısını hesaplar
   * @param {string} validTo - Sertifikanın geçerlilik sonu tarihi
   * @returns {number} - Kalan gün sayısı
   */
  getDaysRemaining(validTo) {
    const expiryDate = new Date(validTo);
    const currentDate = new Date();
    const timeDiff = expiryDate.getTime() - currentDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  /**
   * SSL sonuçlarına göre 4-durumlu sistem durumu belirler
   * @param {number} daysRemaining - Kalan gün sayısı
   * @returns {string} - up, warning, critical, down
   */
  determineSslStatus(daysRemaining) {
    if (daysRemaining <= 0) {
      return 'down';        // Sertifika süresi dolmuş
    } else if (daysRemaining <= 7) {
      return 'critical';    // 7 gün veya daha az kaldı
    } else if (daysRemaining <= 30) {
      return 'warning';     // 30 gün veya daha az kaldı
    } else {
      return 'up';          // Sertifika geçerli
    }
  }

  /**
   * Sertifikanın geçerlilik durumunu belirler
   * @param {number} daysRemaining - Kalan gün sayısı
   * @returns {string} - Geçerlilik durumu
   */
  getExpiryStatus(daysRemaining) {
    if (daysRemaining <= 0) {
      return 'expired';
    } else if (daysRemaining <= 7) {
      return 'critical';
    } else if (daysRemaining <= 30) {
      return 'warning';
    } else {
      return 'valid';
    }
  }

  /**
   * SSL durumu için açıklama mesajı oluşturur
   * @param {string} status - Durum (up, warning, critical, down)
   * @param {Object} details - SSL detayları
   * @param {string} host - Host adresi
   * @returns {string} - Açıklama mesajı
   */
  getSslStatusMessage(status, details, host) {
    const daysRemaining = details?.daysRemaining || 0;
    const validTo = details?.validTo ? new Date(details.validTo).toLocaleDateString('tr-TR') : 'Bilinmiyor';

    switch (status) {
      case 'down':
        return `SSL sertifikası alınamadı: ${host} - Bağlantı hatası`;

      case 'critical':
        if (daysRemaining <= 0) {
          return `SSL sertifikası süresi dolmuş: ${host} (${validTo} tarihinde doldu)`;
        }
        return `SSL sertifikası yakında dolacak: ${host} - ${daysRemaining} gün kaldı (${validTo})`;

      case 'warning':
        return `SSL sertifikası uyarı: ${host} - ${daysRemaining} gün kaldı (${validTo})`;

      case 'up':
        return `SSL sertifikası geçerli: ${host} - ${daysRemaining} gün kaldı (${validTo})`;

      default:
        return 'SSL durumu bilinmiyor';
    }
  }

  /**
   * Bir cihazın SSL durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // SSL izleme yapılandırmasını al
      const config = device.monitors?.ssl || {};

      // Host ve port bilgilerini belirle
      const host = config.host || device.host;
      const port = config.port || 443;

      console.log(`SSL check for ${device.name}: Using host ${host}:${port}`);

      // SSL sertifikasını kontrol et
      const sslResult = await this.checkSslCertificate(host, { port });

      // Durum açıklaması oluştur
      const message = this.getSslStatusMessage(sslResult.status, sslResult.details, host);

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: sslResult.status,
        responseTime: sslResult.responseTime,
        error: sslResult.error || '',
        message: message, // Açıklama eklendi
        details: {
          ...sslResult.details,
          message: message // Detaylarda da açıklama
        }
      };

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      // Eğer interval değeri yoksa, varsayılan olarak 60 dakika kullan
      const intervalValue = device.monitors.ssl && device.monitors.ssl.interval ? device.monitors.ssl.interval : '60';
      const interval = parseInt(intervalValue) * 60 * 1000;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`SSL check for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // ✅ Artık sadece sonuç döndürüyoruz, kaydetmiyoruz
      // Scheduler bu sonucu alıp Redis'e kaydedecek
      return result; // result objesi message alanını içeriyor
    } catch (error) {
      console.error(`SSL monitoring error for ${device.host}:`, error);

      // ✅ Hata durumunda da sadece sonuç döndürüyoruz
      // Scheduler hata durumunu da işleyecek

      return {
        status: 'error',
        responseTime: 0,
        error: error.message,
        details: {
          validFrom: null,
          validTo: null,
          daysRemaining: 0,
          expiryStatus: 'error'
        }
      };
    }
  }
}

// Singleton instance oluştur
const sslMonitor = new SslMonitor();

module.exports = {
  checkSslCertificate: (host, options) => sslMonitor.checkSslCertificate(host, options),
  monitorDevice: (device, io) => sslMonitor.monitorDevice(device, io),
  getResults: (deviceId) => sslMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => sslMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => sslMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => sslMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => sslMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => sslMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
