/**
 * ICMP (ping) izleme servisi
 * Node.js v20 LTS uyumlu sürüm - child_process kullanarak sistem ping komutunu çalıştırır
 */
const { exec } = require('child_process');
const dns = require('dns');
const { promisify } = require('util');
const fs = require('fs').promises;
const path = require('path');
const BaseMonitor = require('./base-monitor');
const { isValidIpAddress } = require('../../utils/helpers');
const { getThresholds } = require('../../config/monitor-thresholds');

// DNS çözümleme fonksiyonlarını promisify et
const dnsLookup = promisify(dns.lookup);
const dnsResolve4 = promisify(dns.resolve4);

class IcmpMonitor extends BaseMonitor {
  constructor() {
    super('icmp');
    console.log('ICMP Monitor initialized using system ping command');
  }

  /**
   * <PERSON><PERSON><PERSON><PERSON> varsayılan DNS sunucusunu alır
   * @returns {Promise<string>} - Varsayılan DNS sunucusu
   */
  async getDefaultDnsServer() {
    try {
      // Ayarlar dosyasını oku
      const settingsFilePath = path.join(__dirname, '../../data/settings.json');
      const data = await fs.readFile(settingsFilePath, 'utf8');
      const settings = JSON.parse(data);

      // Varsayılan DNS sunucusunu döndür
      return settings.defaultDnsServer === 'system' ? null : settings.defaultDnsServer || '*******';
    } catch (err) {
      console.error('Error reading settings file:', err);
      return '*******'; // Hata durumunda Google DNS'i kullan
    }
  }

  /**
   * Alan adını IP adresine çözümler
   * @param {string} hostname - Çözümlenecek alan adı
   * @param {string} dnsServer - Kullanılacak DNS sunucusu (opsiyonel)
   * @returns {Promise<string>} - Çözümlenen IP adresi
   */
  async resolveHostname(hostname, dnsServer = null) {
    try {
      // Eğer özel bir DNS sunucusu belirtilmişse, onu kullan
      if (dnsServer) {
        console.log(`Using custom DNS server ${dnsServer} to resolve ${hostname}`);

        // Özel DNS sunucusu için resolver oluştur
        const resolver = new dns.Resolver();
        resolver.setServers([dnsServer]);

        // Promisify the resolver's resolve4 method
        const resolverResolve4 = promisify(resolver.resolve4.bind(resolver));

        // Alan adını çözümle
        const addresses = await resolverResolve4(hostname);
        return addresses[0]; // İlk IP adresini döndür
      } else {
        // Sistem DNS sunucusunu kullan
        console.log(`Using system DNS to resolve ${hostname}`);
        const result = await dnsLookup(hostname);
        return result.address;
      }
    } catch (error) {
      console.error(`Error resolving hostname ${hostname}:`, error);
      throw error;
    }
  }

  /**
   * Belirtilen IP adresine ping gönderir (sistem ping komutunu kullanır)
   * @param {string} host - Ping gönderilecek IP adresi veya hostname
   * @param {Object} options - Ping seçenekleri
   * @returns {Promise<Object>} - Ping sonuçları
   */
  async pingHost(host, options = {}) {
    try {
      // Ping seçeneklerini al
      const timeout = options.timeout || 2000;
      const retries = options.retries || 2;
      const packetSize = options.packetSize || 64;
      const ttl = options.ttl || 128;

      // IP adresi kontrolü
      const isIpAddress = isValidIpAddress(host);

      // Eğer host bir IP adresi değilse, DNS çözümlemesi yap
      let ipAddress = host;
      if (!isIpAddress) {
        try {
          console.log(`Resolving hostname: ${host}`);
          // Eğer özel bir DNS sunucusu belirtilmişse, onu kullan
          if (options.dnsServer) {
            console.log(`Using custom DNS server ${options.dnsServer} to resolve ${host}`);
            ipAddress = await this.resolveHostname(host, options.dnsServer);
          } else {
            ipAddress = await this.resolveHostname(host);
          }
          console.log(`Resolved ${host} to ${ipAddress}`);
        } catch (dnsError) {
          console.error(`DNS resolution error for ${host}:`, dnsError);
          return {
            host,
            ipAddress: null,
            alive: false,
            error: `DNS resolution error: ${dnsError.message}`,
            time: null
          };
        }
      }

      return new Promise((resolve) => {
        const startTime = process.hrtime();

        // Ping komutunu oluştur
        // Linux/Unix için ping komutu
        const pingCmd = `ping -c ${retries + 1} -W ${Math.ceil(timeout / 1000)} -s ${packetSize} -t ${ttl} ${ipAddress}`;

        console.log(`Executing ping command: ${pingCmd}`);

        // Ping komutunu çalıştır
        exec(pingCmd, (error, stdout, stderr) => {
          if (stderr) {
            console.error(`Ping stderr: ${stderr}`);
          }

          if (error) {
            // Ping başarısız oldu
            console.error(`Ping error: ${error.message}`);
            return resolve({
              host,
              ipAddress,
              alive: false,
              error: error.message,
              time: null
            });
          }

          // Ping çıktısını analiz et
          try {
            const output = stdout.toString();
            console.log(`Ping output: ${output}`);

            // Ping istatistiklerini çıkar
            let time = null;
            let min = null;
            let max = null;
            let avg = null;
            let packetLoss = 100;

            // Paket kaybı oranını bul
            const packetLossMatch = output.match(/(\d+)% packet loss/);
            if (packetLossMatch && packetLossMatch[1]) {
              packetLoss = parseInt(packetLossMatch[1], 10);
            }

            // Ping sürelerini bul
            const rttMatch = output.match(/min\/avg\/max(?:\/mdev)? = ([\d.]+)\/([\d.]+)\/([\d.]+)/);
            if (rttMatch) {
              min = parseFloat(rttMatch[1]);
              avg = parseFloat(rttMatch[2]);
              max = parseFloat(rttMatch[3]);
              time = avg;
            } else {
              // Alternatif olarak, her satırdan süreleri çıkar
              const timeMatches = output.match(/time=([\d.]+) ms/g);
              if (timeMatches && timeMatches.length > 0) {
                const times = timeMatches.map(match => {
                  const t = match.match(/time=([\d.]+) ms/);
                  return t ? parseFloat(t[1]) : 0;
                }).filter(t => t > 0);

                if (times.length > 0) {
                  min = Math.min(...times);
                  max = Math.max(...times);
                  avg = times.reduce((sum, t) => sum + t, 0) / times.length;
                  time = avg;
                }
              }
            }

            // Yanıt süresini hesapla (ms cinsinden)
            const diff = process.hrtime(startTime);
            const totalTime = diff[0] * 1000 + diff[1] / 1000000;

            // Sonuçları döndür
            resolve({
              host,
              ipAddress,
              alive: packetLoss < 100,
              time: time || Math.round(totalTime),
              min,
              max,
              avg,
              packetLoss,
              output
            });
          } catch (parseError) {
            console.error(`Error parsing ping output: ${parseError.message}`);

            // Yanıt süresini hesapla (ms cinsinden)
            const diff = process.hrtime(startTime);
            const totalTime = diff[0] * 1000 + diff[1] / 1000000;

            resolve({
              host,
              ipAddress,
              alive: false,
              error: parseError.message,
              time: Math.round(totalTime),
              output: stdout.toString()
            });
          }
        });
      });
    } catch (error) {
      console.error(`Error in pingHost for ${host}:`, error);
      return {
        host,
        ipAddress: null,
        alive: false,
        error: error.message,
        time: null
      };
    }
  }

  /**
   * ICMP sonuçlarına göre 4-durumlu sistem durumu belirler
   * @param {Object} pingResult - Ping sonuçları
   * @returns {string} - up, warning, critical, down
   */
  determineIcmpStatus(pingResult) {
    // Bağlantı kurulamadıysa DOWN
    if (!pingResult.alive || pingResult.packetLoss === 100) {
      return 'down';
    }

    const responseTime = pingResult.time || pingResult.avg || 0;
    const packetLoss = pingResult.packetLoss || 0;

    // Standardized thresholds from config
    const thresholds = getThresholds('icmp');

    // CRITICAL: Yüksek paket kaybı veya çok yavaş yanıt
    if (packetLoss >= 26 || responseTime > thresholds.critical) {
      return 'critical';
    }

    // WARNING: Düşük paket kaybı veya orta seviye yavaş yanıt
    if (packetLoss >= 1 || responseTime >= thresholds.warning) {
      return 'warning';
    }

    // UP: İyi performans
    return 'up';
  }

  /**
   * ICMP durumu için açıklama mesajı oluşturur
   * @param {string} status - Durum (up, warning, critical, down)
   * @param {Object} pingResult - Ping sonuçları
   * @returns {string} - Açıklama mesajı
   */
  getIcmpStatusMessage(status, pingResult) {
    const responseTime = pingResult.time || pingResult.avg || 0;
    const packetLoss = pingResult.packetLoss || 0;

    switch (status) {
      case 'down':
        if (pingResult.packetLoss === 100) {
          return 'Ping yanıt vermiyor - %100 paket kaybı';
        }
        return 'Ping yanıt vermiyor - Bağlantı kurulamadı';

      case 'critical':
        if (packetLoss >= 26) {
          return `Kritik paket kaybı: %${packetLoss} (yanıt süresi: ${responseTime}ms)`;
        }
        const criticalThreshold = getThresholds('icmp').critical;
        return `Kritik yavaş yanıt: ${responseTime}ms (>${criticalThreshold}ms)`;

      case 'warning':
        if (packetLoss >= 1) {
          return `Paket kaybı tespit edildi: %${packetLoss} (yanıt süresi: ${responseTime}ms)`;
        }
        const warningThreshold = getThresholds('icmp').warning;
        return `Yavaş yanıt: ${responseTime}ms (>=${warningThreshold}ms)`;

      case 'up':
        return `Ping başarılı: ${responseTime}ms yanıt süresi`;

      default:
        return 'ICMP durumu bilinmiyor';
    }
  }

  /**
   * Bir cihazın ICMP durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // ICMP kontrolü için host değerini belirle
      // Eğer monitors.icmp.host tanımlıysa onu kullan, değilse device.host kullan
      const icmpHost = device.monitors?.icmp?.host || device.host;
      console.log(`ICMP check for ${device.name}: Using host ${icmpHost}`);

      // ICMP kontrolü için DNS sunucusunu belirle
      // Öncelik sırası: 1) Cihaz ayarları, 2) Sistem ayarları, 3) Sistem varsayılanı
      let dnsServer = device.monitors?.icmp?.dnsServer || null;

      // Eğer cihaz için özel bir DNS sunucusu belirtilmemişse, varsayılan DNS sunucusunu kullan
      if (!dnsServer) {
        dnsServer = await this.getDefaultDnsServer();
      }

      // ICMP kontrolü için options nesnesi
      // Frontend'den gelen değerler string olarak geliyor, sayıya dönüştürülmeli
      const options = {
        timeout: device.monitors?.icmp?.timeout ? parseInt(device.monitors.icmp.timeout) : 2000,
        retries: device.monitors?.icmp?.retries ? parseInt(device.monitors.icmp.retries) : 2,
        packetSize: device.monitors?.icmp?.packetSize ? parseInt(device.monitors.icmp.packetSize) : 64,
        ttl: device.monitors?.icmp?.ttl ? parseInt(device.monitors.icmp.ttl) : 128,
        dnsServer: dnsServer
      };

      console.log(`ICMP check for ${device.name}: Using DNS server ${dnsServer || 'system default'}`);
      console.log(`ICMP check for ${device.name}: Using timeout ${options.timeout}ms and retries ${options.retries}`);

      const pingResult = await this.pingHost(icmpHost, options);

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      // Eğer interval değeri yoksa, varsayılan olarak 5 dakika kullan
      const intervalValue = device.monitors.icmp && device.monitors.icmp.interval ? device.monitors.icmp.interval : '5';
      const interval = parseInt(intervalValue) * 60 * 1000;

      // 4-durumlu sistem için durum belirleme
      const status = this.determineIcmpStatus(pingResult);

      // Durum açıklaması oluştur
      const message = this.getIcmpStatusMessage(status, pingResult);

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: status,
        responseTime: pingResult.time || 0,
        error: pingResult.error || '',
        message: message, // Açıklama eklendi
        details: {
          host: pingResult.host,
          ipAddress: pingResult.ipAddress,
          resolved: pingResult.host !== pingResult.ipAddress,
          min: pingResult.min,
          max: pingResult.max,
          avg: pingResult.avg,
          packetLoss: pingResult.packetLoss,
          status: status,
          message: message // Detaylarda da açıklama
        }
      };

      // ✅ Artık sadece sonuç döndürüyoruz, kaydetmiyoruz
      // Scheduler bu sonucu alıp Redis'e kaydedecek
      return result; // result objesi message alanını içeriyor
    } catch (error) {
      console.error(`ICMP monitoring error for ${device.host}:`, error);

      // ✅ Hata durumunda da sadece sonuç döndürüyoruz
      // Scheduler hata durumunu da işleyecek

      return {
        host: device.host,
        alive: false,
        error: error.message,
        time: null
      };
    }
  }
}

// Singleton instance oluştur
const icmpMonitor = new IcmpMonitor();

module.exports = {
  pingHost: (host, options) => icmpMonitor.pingHost(host, options),
  monitorDevice: (device, io) => icmpMonitor.monitorDevice(device, io),
  getResults: (deviceId) => icmpMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => icmpMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => icmpMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => icmpMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => icmpMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => icmpMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
