/**
 * Tüm izleme servisleri için temel sınıf
 */
const redisClient = require('../../config/redis');
const { safeJsonStringify } = require('../../utils/helpers');

class BaseMonitor {
  /**
   * Temel izleme sınıfını oluşturur
   * @param {string} type - İzleme türü (icmp, http, tcp, vb.)
   */
  constructor(type) {
    this.type = type;
  }

  // ❌ KALDIRILAN METOD: saveResults()
  // Bu metod Scheduler ile çakışıyordu ve gereksiz çifte yazma yapıyordu.
  // Artık tüm yazma işlemleri sadece Scheduler tarafından yapılıyor.
  // İzleyici sınıfları sadece sonuç döndürüyor, kaydetmiyor.

  // ❌ KALDIRILAN METOD: saveError()
  // Bu metod da saveResults() metodunu kullanıyordu.
  // Artık hata durumları da Scheduler tarafından işleniyor.

  /**
   * İzleme sonuçlarını Redis'ten alır
   * @param {string} deviceId - Cihaz ID
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async getResults(deviceId) {
    try {
      const key = `monitor:${this.type}:${deviceId}`;
      const results = await redisClient.hgetall(key);

      // Sayısal değerleri dönüştür
      if (results) {
        if (results.lastCheck) results.lastCheck = parseInt(results.lastCheck);
        if (results.nextCheck) results.nextCheck = parseInt(results.nextCheck);
        if (results.responseTime) results.responseTime = parseInt(results.responseTime);

        // details alanını JSON'dan parse et
        if (results.details && typeof results.details === 'string') {
          try {
            results.details = JSON.parse(results.details);
          } catch (e) {
            console.error(`Error parsing details for ${this.type} monitor of device ${deviceId}:`, e);
          }
        }
      }

      return results;
    } catch (error) {
      console.error(`Error getting ${this.type} results for device ${deviceId}:`, error);
      return null;
    }
  }

  /**
   * İzleme geçmişini Redis'ten alır
   * @param {string} deviceId - Cihaz ID
   * @param {number} limit - Kaç kayıt alınacağı
   * @returns {Promise<Array>} - İzleme geçmişi
   */
  async getHistory(deviceId, limit = 100) {
    try {
      const historyKey = `history:${this.type}:${deviceId}`;
      const history = await redisClient.lrange(historyKey, 0, limit - 1);

      if (!history || history.length === 0) {
        return [];
      }

      return history.map(item => {
        try {
          return JSON.parse(item);
        } catch (e) {
          console.error(`Error parsing history item for ${this.type} of device ${deviceId}:`, e);
          return null;
        }
      }).filter(item => item !== null);
    } catch (error) {
      console.error(`Error getting ${this.type} history for device ${deviceId}:`, error);
      return [];
    }
  }

  /**
   * İzleme verilerini siler
   * @param {string} deviceId - Cihaz ID
   * @returns {Promise<boolean>} - Başarılı ise true
   */
  async deleteData(deviceId) {
    try {
      const key = `monitor:${this.type}:${deviceId}`;
      const historyKey = `history:${this.type}:${deviceId}`;

      await Promise.all([
        redisClient.del(key),
        redisClient.del(historyKey)
      ]);

      return true;
    } catch (error) {
      console.error(`Error deleting ${this.type} data for device ${deviceId}:`, error);
      return false;
    }
  }

  /**
   * Son kontrol zamanını Redis'ten alır
   * @param {string} deviceId - Cihaz ID
   * @returns {Promise<number>} - Son kontrol zamanı
   */
  async getLastCheckTime(deviceId) {
    try {
      const key = `monitor:${this.type}:${deviceId}`;
      const lastCheck = await redisClient.hget(key, 'lastCheck');
      return lastCheck ? parseInt(lastCheck) : 0;
    } catch (error) {
      console.error(`Error getting last check time for ${this.type} monitor of device ${deviceId}:`, error);
      return 0;
    }
  }

  /**
   * Sonraki kontrol zamanını Redis'ten alır
   * @param {string} deviceId - Cihaz ID
   * @returns {Promise<number>} - Sonraki kontrol zamanı
   */
  async getNextCheckTime(deviceId) {
    try {
      const key = `monitor:${this.type}:${deviceId}`;
      const nextCheck = await redisClient.hget(key, 'nextCheck');
      return nextCheck ? parseInt(nextCheck) : 0;
    } catch (error) {
      console.error(`Error getting next check time for ${this.type} monitor of device ${deviceId}:`, error);
      return 0;
    }
  }

  // ❌ KALDIRILAN METOD: updateNextCheckTime()
  // Bu metod da Redis'e yazma yapıyordu ve Scheduler ile çakışıyordu.
  // Artık tüm zaman güncellemeleri Scheduler tarafından yapılıyor.
}

module.exports = BaseMonitor;
