/**
 * E-posta servisi
 * SMTP ile e-posta gönderimi için
 */

const nodemailer = require('nodemailer');
const fs = require('fs').promises;
const path = require('path');
const settingsService = require('./settingsService');

class EmailService {
  constructor() {
    this.transporter = null;
    this.isConfigured = false;
  }

  /**
   * SMTP yapılandırmasını yükler ve transporter oluşturur
   */
  async configure() {
    try {
      const settings = await settingsService.getSettings();
      
      if (!settings.emailNotifications) {
        console.log('E-posta bildirimleri devre dışı');
        this.isConfigured = false;
        return false;
      }

      if (!settings.emailServer || !settings.emailPort || !settings.emailUser || !settings.emailPassword) {
        console.log('E-posta ayarları eksik:', {
          server: !!settings.emailServer,
          port: !!settings.emailPort,
          user: !!settings.emailUser,
          password: !!settings.emailPassword
        });
        this.isConfigured = false;
        return false;
      }

      // SMTP transporter oluştur
      const port = parseInt(settings.emailPort);
      this.transporter = nodemailer.createTransport({
        host: settings.emailServer,
        port: port,
        secure: port === 465, // true for 465, false for other ports (587, 25)
        auth: {
          user: settings.emailUser,
          pass: settings.emailPassword
        },
        tls: {
          rejectUnauthorized: false // Self-signed sertifikalar için
        }
      });

      // Bağlantıyı test et
      await this.transporter.verify();
      console.log('✅ SMTP bağlantısı başarılı');
      this.isConfigured = true;
      return true;

    } catch (error) {
      console.error('❌ SMTP yapılandırma hatası:', error.message);
      this.isConfigured = false;
      return false;
    }
  }

  /**
   * E-posta şablonunu yükler ve render eder
   */
  async renderTemplate(templateName, data) {
    try {
      const templatePath = path.join(__dirname, '../templates', `${templateName}.html`);
      let template = await fs.readFile(templatePath, 'utf8');

      // Basit template engine - {{variable}} formatını değiştir
      Object.keys(data).forEach(key => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        template = template.replace(regex, data[key] || '');
      });

      return template;
    } catch (error) {
      console.error(`Template yükleme hatası (${templateName}):`, error.message);
      // Fallback: Basit metin template
      return this.createFallbackTemplate(data);
    }
  }

  /**
   * Severity text'ini döndürür
   */
  getSeverityText(severity) {
    switch (severity) {
      case 'critical': return '🔴 Kritik';
      case 'warning': return '🟡 Uyarı';
      case 'info': return '🔵 Bilgi';
      case 'success': return '🟢 Başarılı';
      default: return '🟡 Uyarı';
    }
  }

  /**
   * E-posta adreslerini parse eder (virgül ile ayrılmış)
   */
  parseEmailAddresses(emailString) {
    if (!emailString) return [];

    return emailString
      .split(',')
      .map(email => email.trim())
      .filter(email => email && this.isValidEmail(email));
  }

  /**
   * E-posta adresinin geçerli olup olmadığını kontrol eder
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Fallback template oluşturur
   */
  createFallbackTemplate(data) {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
          <h2 style="color: #3B82F6;">NetWatch Bildirimi</h2>
          <p><strong>Başlık:</strong> ${data.title || 'Bildirim'}</p>
          <p><strong>Mesaj:</strong> ${data.message || 'Detay bilgi yok'}</p>
          <p><strong>Tarih:</strong> ${data.date || new Date().toLocaleString('tr-TR')}</p>
          <hr>
          <p style="color: #666; font-size: 12px;">
            Bu e-posta NetWatch Ağ İzleme Sistemi tarafından otomatik olarak gönderilmiştir.
          </p>
        </body>
      </html>
    `;
  }

  /**
   * Cihaz uyarısı e-postası gönderir
   */
  async sendDeviceAlert(alertData) {
    if (!this.isConfigured) {
      await this.configure();
      if (!this.isConfigured) return false;
    }

    try {
      const settings = await settingsService.getSettings();
      
      const severity = alertData.severity || 'warning';
      const severityText = this.getSeverityText(severity);

      const templateData = {
        deviceName: alertData.deviceName || 'Bilinmeyen Cihaz',
        deviceHost: alertData.deviceHost || '',
        alertType: alertData.type || 'Genel',
        alertMessage: alertData.message || 'Cihaz sorunu tespit edildi',
        severity: severity,
        severityText: severityText,
        timestamp: new Date(alertData.timestamp || Date.now()).toLocaleString('tr-TR'),
        companyName: settings.companyName || 'NetWatch'
      };

      const htmlContent = await this.renderTemplate('device-alert', templateData);

      // Çoklu e-posta adresi desteği
      const recipients = this.parseEmailAddresses(settings.emailTo);
      if (recipients.length === 0) {
        throw new Error('Geçerli e-posta adresi bulunamadı');
      }

      const mailOptions = {
        from: `"${settings.companyName || 'NetWatch'}" <${settings.emailFrom || settings.emailUser}>`,
        to: recipients.join(', '),
        subject: `🚨 Cihaz Uyarısı: ${templateData.deviceName}`,
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`✅ Cihaz uyarısı e-postası gönderildi (${recipients.length} alıcı): ${result.messageId}`);
      console.log(`📧 Alıcılar: ${recipients.join(', ')}`);
      return true;

    } catch (error) {
      console.error('❌ Cihaz uyarısı e-posta gönderme hatası:', error.message);
      return false;
    }
  }

  /**
   * Sistem bildirimi e-postası gönderir
   */
  async sendSystemNotification(notificationData) {
    if (!this.isConfigured) {
      await this.configure();
      if (!this.isConfigured) return false;
    }

    try {
      const settings = await settingsService.getSettings();
      
      const severity = notificationData.severity || 'info';
      const severityText = this.getSeverityText(severity);

      const templateData = {
        title: notificationData.title || 'Sistem Bildirimi',
        message: notificationData.message || '',
        severity: severity,
        severityText: severityText,
        timestamp: new Date(notificationData.timestamp || Date.now()).toLocaleString('tr-TR'),
        companyName: settings.companyName || 'NetWatch'
      };

      const htmlContent = await this.renderTemplate('system-notification', templateData);

      // Çoklu e-posta adresi desteği
      const recipients = this.parseEmailAddresses(settings.emailTo);
      if (recipients.length === 0) {
        throw new Error('Geçerli e-posta adresi bulunamadı');
      }

      const mailOptions = {
        from: `"${settings.companyName || 'NetWatch'}" <${settings.emailFrom || settings.emailUser}>`,
        to: recipients.join(', '),
        subject: `📢 ${templateData.title}`,
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`✅ Sistem bildirimi e-postası gönderildi (${recipients.length} alıcı): ${result.messageId}`);
      console.log(`📧 Alıcılar: ${recipients.join(', ')}`);
      return true;

    } catch (error) {
      console.error('❌ Sistem bildirimi e-posta gönderme hatası:', error.message);
      return false;
    }
  }

  /**
   * Test e-postası gönderir
   */
  async sendTestEmail() {
    if (!this.isConfigured) {
      await this.configure();
      if (!this.isConfigured) return false;
    }

    try {
      const settings = await settingsService.getSettings();
      
      const templateData = {
        title: 'Test E-postası',
        message: 'Bu bir test e-postasıdır. E-posta ayarlarınız doğru şekilde yapılandırılmıştır.',
        timestamp: new Date().toLocaleString('tr-TR'),
        companyName: settings.companyName || 'NetWatch'
      };

      const htmlContent = await this.renderTemplate('test-email', templateData);

      // Çoklu e-posta adresi desteği
      const recipients = this.parseEmailAddresses(settings.emailTo);
      if (recipients.length === 0) {
        throw new Error('Geçerli e-posta adresi bulunamadı');
      }

      const mailOptions = {
        from: `"${settings.companyName || 'NetWatch'}" <${settings.emailFrom || settings.emailUser}>`,
        to: recipients.join(', '),
        subject: '✅ NetWatch Test E-postası',
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`✅ Test e-postası gönderildi (${recipients.length} alıcı): ${result.messageId}`);
      console.log(`📧 Alıcılar: ${recipients.join(', ')}`);
      return true;

    } catch (error) {
      console.error('❌ Test e-postası gönderme hatası:', error.message);
      return false;
    }
  }

  /**
   * Şifre sıfırlama e-postası gönderir
   */
  async sendPasswordResetEmail(email, resetToken, baseUrl) {
    if (!this.isConfigured) {
      await this.configure();
      if (!this.isConfigured) return false;
    }

    try {
      const settings = await settingsService.getSettings();

      // Reset URL'ini oluştur
      const resetUrl = `${baseUrl}/reset-password/${resetToken}`;

      const templateData = {
        email: email,
        resetUrl: resetUrl,
        resetToken: resetToken,
        companyName: settings.companyName || 'NetWatch',
        timestamp: new Date().toLocaleString('tr-TR'),
        expiryTime: '1 saat'
      };

      const htmlContent = await this.renderTemplate('password-reset', templateData);

      const mailOptions = {
        from: `"${settings.companyName || 'NetWatch'}" <${settings.emailFrom || settings.emailUser}>`,
        to: email,
        subject: '🔐 Şifre Sıfırlama Talebi - NetWatch',
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`✅ Şifre sıfırlama e-postası gönderildi: ${email} (${result.messageId})`);
      return true;

    } catch (error) {
      console.error('❌ Şifre sıfırlama e-postası gönderme hatası:', error.message);
      return false;
    }
  }

  /**
   * E-posta yapılandırmasını test eder
   */
  async testConfiguration() {
    try {
      const configured = await this.configure();
      if (!configured) {
        return { success: false, error: 'E-posta ayarları eksik veya hatalı' };
      }

      // Test e-postası gönder
      const sent = await this.sendTestEmail();
      if (sent) {
        return { success: true, message: 'Test e-postası başarıyla gönderildi' };
      } else {
        return { success: false, error: 'Test e-postası gönderilemedi' };
      }

    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

// Singleton instance
const emailService = new EmailService();

module.exports = emailService;
