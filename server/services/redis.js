/**
 * Redis bağlantı servisi
 */

const Redis = require('ioredis');
const dotenv = require('dotenv');

// Yapılandırma dosyasını yükle
dotenv.config();

// Redis bağlantı bilgileri
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || '',
  db: process.env.REDIS_DB || 0
};

// Redis istemcisini oluştur
const redisClient = new Redis(redisConfig);

// Bağlantı olaylarını dinle
redisClient.on('connect', () => {
  console.log('Redis bağlantısı başarılı');
});

redisClient.on('error', (err) => {
  console.error('Redis bağlantı hatası:', err);
});

module.exports = redisClient;
