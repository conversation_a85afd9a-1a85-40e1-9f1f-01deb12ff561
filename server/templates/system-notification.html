<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Bildirimi - {{companyName}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header .icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .content {
            padding: 30px 20px;
        }
        .notification-box {
            background-color: #f0f9ff;
            border-left: 4px solid #3b82f6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .notification-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e40af;
            margin: 0 0 10px 0;
        }
        .notification-message {
            color: #374151;
            margin: 0;
            white-space: pre-line;
        }
        .severity-box {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            margin: 15px 0;
        }
        .severity-critical {
            background-color: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        .severity-warning {
            background-color: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        .severity-info {
            background-color: #eff6ff;
            color: #2563eb;
            border: 1px solid #bfdbfe;
        }
        .severity-success {
            background-color: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        .timestamp-box {
            background-color: #f9fafb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .timestamp {
            color: #6b7280;
            font-size: 14px;
            margin: 0;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        .company-name {
            color: #3b82f6;
            font-weight: 600;
        }
        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #e5e7eb, transparent);
            margin: 30px 0;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 4px;
            }
            .content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="icon">📢</div>
            <h1>Sistem Bildirimi</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">{{companyName}} Ağ İzleme Sistemi</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="notification-box">
                <div class="notification-title">{{title}}</div>
                <p class="notification-message">{{message}}</p>
            </div>

            <!-- Severity Badge -->
            <div style="text-align: center;">
                <span class="severity-box severity-{{severity}}">{{severityText}}</span>
            </div>

            <div class="divider"></div>

            <!-- Timestamp -->
            <div class="timestamp-box">
                <p class="timestamp">
                    <strong>📅 Bildirim Zamanı:</strong><br>
                    {{timestamp}}
                </p>
            </div>

            <!-- Info Box -->
            <div style="background-color: #f0f9ff; border-radius: 6px; padding: 15px; margin: 20px 0; border: 1px solid #bfdbfe;">
                <p style="margin: 0; color: #1e40af; font-size: 14px; text-align: center;">
                    <strong>ℹ️ Bilgi:</strong> 
                    Bu bildirim NetWatch sistem tarafından otomatik olarak oluşturulmuştur.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Bu e-posta <span class="company-name">{{companyName}}</span> Ağ İzleme Sistemi tarafından otomatik olarak gönderilmiştir.</p>
            <p style="margin-top: 10px; font-size: 12px; color: #9ca3af;">
                Gönderim Zamanı: {{timestamp}}
            </p>
        </div>
    </div>
</body>
</html>
