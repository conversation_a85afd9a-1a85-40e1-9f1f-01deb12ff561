<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>az Uyarısı - {{companyName}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header .icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .content {
            padding: 30px 20px;
        }
        .alert-box {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .alert-title {
            font-size: 18px;
            font-weight: 600;
            color: #dc2626;
            margin: 0 0 10px 0;
        }
        .alert-message {
            color: #374151;
            margin: 0;
        }
        .device-info {
            background-color: #f9fafb;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #374151;
        }
        .info-value {
            color: #6b7280;
        }
        .severity-critical {
            color: #dc2626;
            font-weight: 600;
        }
        .severity-warning {
            color: #d97706;
            font-weight: 600;
        }
        .severity-info {
            color: #2563eb;
            font-weight: 600;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        .company-name {
            color: #3b82f6;
            font-weight: 600;
        }
        .timestamp {
            color: #9ca3af;
            font-size: 12px;
            margin-top: 10px;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 4px;
            }
            .content {
                padding: 20px 15px;
            }
            .info-row {
                flex-direction: column;
            }
            .info-value {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="icon">🚨</div>
            <h1>Cihaz Uyarısı</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">{{companyName}} Ağ İzleme Sistemi</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="alert-box">
                <div class="alert-title">Cihaz Sorunu Tespit Edildi</div>
                <p class="alert-message">{{alertMessage}}</p>
            </div>

            <div class="device-info">
                <h3 style="margin: 0 0 15px 0; color: #374151;">Cihaz Bilgileri</h3>
                
                <div class="info-row">
                    <span class="info-label">Cihaz Adı:</span>
                    <span class="info-value">{{deviceName}}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Host/IP:</span>
                    <span class="info-value">{{deviceHost}}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Uyarı Türü:</span>
                    <span class="info-value">{{alertType}}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Önem Derecesi:</span>
                    <span class="info-value severity-{{severity}}">{{severityText}}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Tespit Zamanı:</span>
                    <span class="info-value">{{timestamp}}</span>
                </div>
            </div>

            <div style="background-color: #eff6ff; border-radius: 6px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #1e40af; font-size: 14px;">
                    <strong>💡 Önerilen İşlem:</strong> 
                    Lütfen cihazın durumunu kontrol edin ve gerekli müdahaleyi yapın. 
                    Sorun devam ederse sistem yöneticinizle iletişime geçin.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Bu e-posta <span class="company-name">{{companyName}}</span> Ağ İzleme Sistemi tarafından otomatik olarak gönderilmiştir.</p>
            <div class="timestamp">Gönderim Zamanı: {{timestamp}}</div>
        </div>
    </div>
</body>
</html>
