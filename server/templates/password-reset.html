<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - {{companyName}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 20px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #3B82F6;
            margin-bottom: 10px;
        }
        .title {
            font-size: 24px;
            color: #1F2937;
            margin-bottom: 20px;
        }
        .content {
            margin-bottom: 30px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
            color: #374151;
        }
        .message {
            font-size: 16px;
            margin-bottom: 25px;
            color: #374151;
            line-height: 1.7;
        }
        .reset-button {
            display: inline-block;
            background-color: #3B82F6;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            margin: 20px 0;
            transition: background-color 0.3s;
        }
        .reset-button:hover {
            background-color: #2563EB;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .alternative-link {
            background-color: #F3F4F6;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
            color: #374151;
        }
        .warning {
            background-color: #FEF3C7;
            border-left: 4px solid #F59E0B;
            padding: 15px;
            margin: 25px 0;
            border-radius: 4px;
        }
        .warning-title {
            font-weight: bold;
            color: #92400E;
            margin-bottom: 8px;
        }
        .warning-text {
            color: #92400E;
            font-size: 14px;
        }
        .info {
            background-color: #EFF6FF;
            border-left: 4px solid #3B82F6;
            padding: 15px;
            margin: 25px 0;
            border-radius: 4px;
        }
        .info-text {
            color: #1E40AF;
            font-size: 14px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
            text-align: center;
            color: #6B7280;
            font-size: 12px;
        }
        .footer-links {
            margin-top: 15px;
        }
        .footer-links a {
            color: #3B82F6;
            text-decoration: none;
            margin: 0 10px;
        }
        .timestamp {
            color: #9CA3AF;
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔐 {{companyName}}</div>
            <div class="title">Şifre Sıfırlama Talebi</div>
        </div>

        <div class="content">
            <div class="greeting">
                Merhaba,
            </div>

            <div class="message">
                <strong>{{email}}</strong> e-posta adresi için şifre sıfırlama talebi aldık. 
                Eğer bu talebi siz yaptıysanız, aşağıdaki butona tıklayarak yeni şifrenizi belirleyebilirsiniz.
            </div>

            <div class="button-container">
                <a href="{{resetUrl}}" class="reset-button">
                    🔑 Şifremi Sıfırla
                </a>
            </div>

            <div class="info">
                <div class="info-text">
                    <strong>Alternatif yöntem:</strong> Eğer buton çalışmıyorsa, aşağıdaki bağlantıyı kopyalayıp tarayıcınıza yapıştırabilirsiniz:
                </div>
            </div>

            <div class="alternative-link">
                {{resetUrl}}
            </div>

            <div class="warning">
                <div class="warning-title">⚠️ Güvenlik Uyarısı</div>
                <div class="warning-text">
                    • Bu bağlantı <strong>{{expiryTime}}</strong> süreyle geçerlidir.<br>
                    • Bağlantı sadece bir kez kullanılabilir.<br>
                    • Eğer bu talebi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.<br>
                    • Şifreniz değiştirilmeyecektir.
                </div>
            </div>

            <div class="message">
                Herhangi bir sorunuz varsa, lütfen sistem yöneticinizle iletişime geçin.
            </div>
        </div>

        <div class="footer">
            <div>
                Bu e-posta <strong>{{companyName}}</strong> Ağ İzleme Sistemi tarafından otomatik olarak gönderilmiştir.
            </div>
            <div class="timestamp">
                Gönderim Zamanı: {{timestamp}}
            </div>
            <div class="footer-links">
                <a href="#">Gizlilik Politikası</a> |
                <a href="#">Kullanım Koşulları</a> |
                <a href="#">Destek</a>
            </div>
        </div>
    </div>
</body>
</html>
