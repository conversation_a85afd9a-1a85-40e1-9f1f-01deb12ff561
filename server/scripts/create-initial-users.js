/**
 * <PERSON><PERSON> kullan<PERSON><PERSON><PERSON>ı oluşturma script'i
 */

const userModel = require('../models/user');
const settingsService = require('../services/settingsService');

async function createInitialUsers() {
  console.log('🔄 <PERSON>lk kullanıcılar oluşturuluyor...');

  try {
    // Varsayılan ayarları yükle
    await settingsService.getSettings();
    console.log('✅ Varsayılan ayarlar yüklendi');

    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      },
      {
        username: 'user1',
        email: '<EMAIL>',
        password: 'User123.',
        role: 'user'
      },
      {
        username: 'user2',
        email: '<EMAIL>',
        password: 'User123.',
        role: 'user'
      },
      {
        username: 'user3',
        email: '<EMAIL>',
        password: 'User123.',
        role: 'user'
      }
    ];

    console.log(`📊 ${users.length} kull<PERSON><PERSON><PERSON><PERSON> oluşturulacak`);

    for (const userData of users) {
      try {
        console.log(`🔄 Oluşturuluyor: ${userData.username} (${userData.role})`);

        const user = await userModel.createUser(userData);

        console.log(`✅ ${userData.username} başarıyla oluşturuldu`);
        console.log(`  📧 E-posta: ${user.email}`);
        console.log(`  👤 Rol: ${user.role}`);
        console.log(`  📊 Durum: ${user.status}`);
        console.log(`  🔓 Kilitli: ${user.locked}`);

      } catch (error) {
        console.error(`❌ ${userData.username} oluşturulamadı:`, error.message);
      }
    }

    console.log('\n🎉 İlk kullanıcılar oluşturuldu!');
    console.log('\n📋 Giriş Bilgileri:');
    console.log('┌─────────────────────────────────────────┐');
    console.log('│ Admin Kullanıcısı:                     │');
    console.log('│   Kullanıcı Adı: admin                 │');
    console.log('│   Şifre: admin123                      │');
    console.log('├─────────────────────────────────────────┤');
    console.log('│ Normal Kullanıcılar:                   │');
    console.log('│   user1 / User123.                     │');
    console.log('│   user2 / User123.                     │');
    console.log('│   user3 / User123.                     │');
    console.log('└─────────────────────────────────────────┘');

  } catch (error) {
    console.error('❌ İlk kullanıcı oluşturma hatası:', error);
    throw error;
  }
}

// Script doğrudan çalıştırılırsa
if (require.main === module) {
  createInitialUsers()
    .then(() => {
      console.log('✅ İlk kullanıcılar başarıyla oluşturuldu');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ İlk kullanıcı oluşturma başarısız:', error);
      process.exit(1);
    });
}

module.exports = { createInitialUsers };
