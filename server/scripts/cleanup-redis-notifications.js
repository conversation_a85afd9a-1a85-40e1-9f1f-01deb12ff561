/**
 * Redis bildirim anahtarlarını temizleme scripti
 * Kullanılmayan anahtarları siler ve sadece gerekli olanları tutar
 */

const Redis = require('ioredis');

// Redis bağlantısı
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
});

/**
 * Gerekli Redis anahtarları (bunlar korunacak)
 */
const REQUIRED_KEYS = [
  'notifications',                    // Ana liste
  'notifications:category:status',    // Durum bildirimleri
  'notifications:category:performance', // Performans bildirimleri
  'notifications:category:security',  // Güvenlik bildirimleri
  'notifications:category:maintenance', // Bakım bildirimleri
  'notifications:category:system'     // Sistem bildirimleri
];

/**
 * Tüm bildirim anahtarlarını listeler
 */
async function getAllNotificationKeys() {
  try {
    const keys = await redis.keys('notifications:*');
    console.log(`Toplam ${keys.length} bildirim anahtarı bulundu`);
    return keys;
  } catch (error) {
    console.error('Anahtarlar listelenirken hata:', error);
    return [];
  }
}

/**
 * Gereksiz anahtarları belirler
 */
function getKeysToDelete(allKeys) {
  const keysToDelete = allKeys.filter(key => {
    // Ana bildirim verilerini koru (notification:id formatında)
    if (key.startsWith('notification:') && !key.includes(':')) {
      return false;
    }

    // Gerekli anahtarları koru
    if (REQUIRED_KEYS.includes(key)) {
      return false;
    }

    // Diğer tüm anahtarları sil
    return true;
  });

  return keysToDelete;
}

/**
 * Anahtarları siler
 */
async function deleteKeys(keys) {
  if (keys.length === 0) {
    console.log('Silinecek anahtar bulunamadı');
    return;
  }

  console.log(`${keys.length} anahtar silinecek:`);
  keys.forEach(key => console.log(`  - ${key}`));

  try {
    // Batch olarak sil (performans için)
    const pipeline = redis.pipeline();
    keys.forEach(key => pipeline.del(key));

    const results = await pipeline.exec();
    const deletedCount = results.filter(([err, result]) => !err && result === 1).length;

    console.log(`✅ ${deletedCount} anahtar başarıyla silindi`);
  } catch (error) {
    console.error('Anahtarlar silinirken hata:', error);
  }
}

/**
 * Gerekli anahtarları yeniden oluşturur
 */
async function recreateRequiredKeys() {
  console.log('\n🔄 Gerekli anahtarlar kontrol ediliyor...');

  try {
    // Ana notifications anahtarının var olup olmadığını kontrol et
    const mainKeyExists = await redis.exists('notifications');
    if (!mainKeyExists) {
      console.log('Ana notifications anahtarı bulunamadı, yeniden oluşturulacak');
    }

    // Kategori anahtarlarını kontrol et
    for (const key of REQUIRED_KEYS) {
      const exists = await redis.exists(key);
      if (!exists && key !== 'notifications') {
        console.log(`${key} anahtarı bulunamadı, gerektiğinde oluşturulacak`);
      }
    }

    console.log('✅ Anahtar kontrolü tamamlandı');
  } catch (error) {
    console.error('Anahtar kontrolü sırasında hata:', error);
  }
}

/**
 * Redis istatistiklerini gösterir
 */
async function showStats() {
  try {
    const info = await redis.info('memory');
    const memoryLines = info.split('\n').filter(line =>
      line.includes('used_memory_human') ||
      line.includes('used_memory_peak_human')
    );

    console.log('\n📊 Redis Memory İstatistikleri:');
    memoryLines.forEach(line => {
      if (line.trim()) {
        console.log(`  ${line.trim()}`);
      }
    });

    // Bildirim sayısını göster
    const notificationCount = await redis.zcard('notifications');
    console.log(`  Toplam bildirim sayısı: ${notificationCount}`);

  } catch (error) {
    console.error('İstatistikler alınırken hata:', error);
  }
}

/**
 * Ana temizlik fonksiyonu
 */
async function cleanup() {
  console.log('🧹 Redis bildirim anahtarları temizleniyor...\n');

  try {
    // Mevcut durumu göster
    await showStats();

    // Tüm anahtarları listele
    const allKeys = await getAllNotificationKeys();

    // Silinecek anahtarları belirle
    const keysToDelete = getKeysToDelete(allKeys);

    console.log('\n📋 Temizlik Raporu:');
    console.log(`  Toplam anahtar: ${allKeys.length}`);
    console.log(`  Korunacak anahtar: ${allKeys.length - keysToDelete.length}`);
    console.log(`  Silinecek anahtar: ${keysToDelete.length}`);

    // Onay iste
    if (keysToDelete.length > 0) {
      console.log('\n⚠️  Bu işlem geri alınamaz!');
      console.log('Devam etmek için CTRL+C ile iptal edin veya 5 saniye bekleyin...');

      await new Promise(resolve => setTimeout(resolve, 5000));

      // Anahtarları sil
      await deleteKeys(keysToDelete);

      // Gerekli anahtarları kontrol et
      await recreateRequiredKeys();

      // Son durumu göster
      console.log('\n🎉 Temizlik tamamlandı!');
      await showStats();
    }

  } catch (error) {
    console.error('Temizlik sırasında hata:', error);
  } finally {
    // Redis bağlantısını kapat
    await redis.quit();
    console.log('\n👋 Redis bağlantısı kapatıldı');
  }
}

// Script'i çalıştır
if (require.main === module) {
  cleanup().catch(console.error);
}

module.exports = { cleanup };
