/**
 * Mevcut kullanıcılar için passwordChangedAt alanını ekleyen migration script
 */

const redisClient = require('../config/redis');

async function migratePasswordChangedAt() {
  try {
    console.log('🔄 passwordChangedAt migration başlatılıyor...');

    // Tüm kullanıcıları al
    const userIds = await redisClient.smembers('users');
    
    if (!userIds || userIds.length === 0) {
      console.log('📭 Hiç kullanıcı bulunamadı');
      return;
    }

    console.log(`👥 ${userIds.length} kullanıcı bulundu`);

    let updatedCount = 0;
    let skippedCount = 0;

    for (const userId of userIds) {
      try {
        // Kullanıcı verilerini al
        const userData = await redisClient.hgetall(`user:${userId}`);
        
        if (!userData || !userData.id) {
          console.log(`⚠️ Kullanıcı verisi bulunamadı: ${userId}`);
          continue;
        }

        // passwordChangedAt alanı zaten var mı kontrol et
        if (userData.passwordChangedAt) {
          console.log(`✅ Kullanıcı ${userData.username} zaten passwordChangedAt alanına sahip`);
          skippedCount++;
          continue;
        }

        // passwordChangedAt alanını ekle (kullanıcının oluşturulma zamanını kullan)
        const passwordChangedAt = userData.createdAt || Date.now();
        
        await redisClient.hset(`user:${userId}`, 'passwordChangedAt', passwordChangedAt);
        
        console.log(`✅ Kullanıcı ${userData.username} için passwordChangedAt eklendi: ${new Date(parseInt(passwordChangedAt)).toISOString()}`);
        updatedCount++;

      } catch (userError) {
        console.error(`❌ Kullanıcı ${userId} güncellenirken hata:`, userError);
      }
    }

    console.log('\n📊 Migration tamamlandı:');
    console.log(`✅ Güncellenen kullanıcı sayısı: ${updatedCount}`);
    console.log(`⏭️ Atlanan kullanıcı sayısı: ${skippedCount}`);
    console.log(`📝 Toplam kullanıcı sayısı: ${userIds.length}`);

  } catch (error) {
    console.error('❌ Migration hatası:', error);
    throw error;
  }
}

// Script doğrudan çalıştırılırsa migration'ı başlat
if (require.main === module) {
  migratePasswordChangedAt()
    .then(() => {
      console.log('🎉 Migration başarıyla tamamlandı');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration başarısız:', error);
      process.exit(1);
    });
}

module.exports = { migratePasswordChangedAt };
