/**
 * <PERSON><PERSON> script, örnek cihazları eklemek için kullanılır.
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// API URL
const API_URL = 'http://localhost:3000/api';

// Örnek cihazlar
const sampleDevices = [
  {
    name: 'Google',
    host: 'google.com',
    group: 'Web/WebServer',
    description: 'Google arama motoru',
    location: 'Mountain View, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://google.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'google.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'google.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Amazon',
    host: 'amazon.com',
    group: 'Web/WebServer',
    description: 'Amazon e-ticaret platformu',
    location: 'Seattle, WA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://amazon.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'amazon.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'amazon.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Microsoft',
    host: 'microsoft.com',
    group: 'Web/WebServer',
    description: 'Microsoft kurumsal web sitesi',
    location: 'Redmond, WA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://microsoft.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'microsoft.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'microsoft.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Facebook',
    host: 'facebook.com',
    group: 'Sunucular/Sanal',
    description: 'Facebook sosyal medya platformu',
    location: 'Menlo Park, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://facebook.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'facebook.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'facebook.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Twitter',
    host: 'twitter.com',
    group: 'Sunucular/Sanal',
    description: 'Twitter sosyal medya platformu',
    location: 'San Francisco, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://twitter.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'twitter.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'twitter.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'LinkedIn',
    host: 'linkedin.com',
    group: 'Sunucular/Sanal',
    description: 'LinkedIn profesyonel sosyal ağ',
    location: 'Sunnyvale, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://linkedin.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'linkedin.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'linkedin.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Netflix',
    host: 'netflix.com',
    group: 'Web/CDN',
    description: 'Netflix video streaming platformu',
    location: 'Los Gatos, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://netflix.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'netflix.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'netflix.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Spotify',
    host: 'spotify.com',
    group: 'Web/CDN',
    description: 'Spotify müzik streaming platformu',
    location: 'Stockholm, İsveç',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://spotify.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'spotify.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'spotify.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'GitHub',
    host: 'github.com',
    group: 'Web/WebServer',
    description: 'GitHub kod barındırma platformu',
    location: 'San Francisco, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://github.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'github.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'github.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Cloudflare',
    host: 'cloudflare.com',
    group: 'Web/CDN',
    description: 'Cloudflare CDN ve güvenlik hizmetleri',
    location: 'San Francisco, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://cloudflare.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'cloudflare.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'cloudflare.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Dropbox',
    host: 'dropbox.com',
    group: 'Sunucular/Depolama',
    description: 'Dropbox dosya depolama hizmeti',
    location: 'San Francisco, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://dropbox.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'dropbox.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'dropbox.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Reddit',
    host: 'reddit.com',
    group: 'Web/WebServer',
    description: 'Reddit sosyal haber platformu',
    location: 'San Francisco, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://reddit.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'reddit.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'reddit.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Wikipedia',
    host: 'wikipedia.org',
    group: 'Web/WebServer',
    description: 'Wikipedia çevrimiçi ansiklopedi',
    location: 'San Francisco, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://wikipedia.org',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'wikipedia.org',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'wikipedia.org',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Instagram',
    host: 'instagram.com',
    group: 'Sunucular/Sanal',
    description: 'Instagram fotoğraf paylaşım platformu',
    location: 'Menlo Park, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://instagram.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'instagram.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'instagram.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'YouTube',
    host: 'youtube.com',
    group: 'Web/CDN',
    description: 'YouTube video paylaşım platformu',
    location: 'San Bruno, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://youtube.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'youtube.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'youtube.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Apple',
    host: 'apple.com',
    group: 'Web/WebServer',
    description: 'Apple kurumsal web sitesi',
    location: 'Cupertino, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://apple.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'apple.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'apple.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Adobe',
    host: 'adobe.com',
    group: 'Web/WebServer',
    description: 'Adobe kurumsal web sitesi',
    location: 'San Jose, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://adobe.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'adobe.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'adobe.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },

  {
    name: 'Zoom',
    host: 'zoom.us',
    group: 'Web/API',
    description: 'Zoom video konferans platformu',
    location: 'San Jose, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://zoom.us',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'zoom.us',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'zoom.us',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      },
      api: {
        enabled: true,
        url: 'https://api.zoom.us/v2/users',
        method: 'GET',
        expectedStatus: 401, // Kimlik doğrulama olmadan 401 dönmesi beklenir
        headers: {},
        interval: '5'
      }
    }
  },
  {
    name: 'PayPal',
    host: 'paypal.com',
    group: 'Web/API',
    description: 'PayPal ödeme platformu',
    location: 'San Jose, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://paypal.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'paypal.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'paypal.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      },
      api: {
        enabled: true,
        url: 'https://api.paypal.com/v1/oauth2/token',
        method: 'POST',
        expectedStatus: 401, // Kimlik doğrulama olmadan 401 dönmesi beklenir
        headers: {},
        interval: '5'
      }
    }
  },
  {
    name: 'Twitch',
    host: 'twitch.tv',
    group: 'Web/CDN',
    description: 'Twitch canlı yayın platformu',
    location: 'San Francisco, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://twitch.tv',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'twitch.tv',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'twitch.tv',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'eBay',
    host: 'ebay.com',
    group: 'Web/WebServer',
    description: 'eBay e-ticaret platformu',
    location: 'San Jose, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://ebay.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'ebay.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'ebay.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Yahoo',
    host: 'yahoo.com',
    group: 'Web/WebServer',
    description: 'Yahoo web portalı',
    location: 'Sunnyvale, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://yahoo.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'yahoo.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'yahoo.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Bing',
    host: 'bing.com',
    group: 'Web/WebServer',
    description: 'Bing arama motoru',
    location: 'Redmond, WA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://bing.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'bing.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'bing.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  },
  {
    name: 'Pinterest',
    host: 'pinterest.com',
    group: 'Web/WebServer',
    description: 'Pinterest görsel keşif platformu',
    location: 'San Francisco, CA',
    monitors: {
      icmp: {
        enabled: true,
        method: 'icmp',
        interval: '10'
      },
      http: {
        enabled: true,
        url: 'https://pinterest.com',
        method: 'GET',
        expectedStatus: 'any',
        interval: '10'
      },
      dns: {
        enabled: true,
        server: '*******',
        domain: 'pinterest.com',
        recordType: 'A',
        interval: '10'
      },
      ssl: {
        enabled: true,
        port: '443',
        host: 'pinterest.com',
        interval: '720'
      },
      tcp: {
        enabled: true,
        port: '80',
        interval: '5'
      }
    }
  }
];

// Cihazları ekle
async function addDevices() {
  try {
    console.log('Örnek cihazlar ekleniyor...');

    // Önce mevcut cihazları sil
    const devicesResponse = await axios.get(`${API_URL}/devices`);
    const devices = devicesResponse.data;

    for (const device of devices) {
      console.log(`Siliniyor: ${device.name} (${device.id})`);
      await axios.delete(`${API_URL}/devices/${device.id}`);
    }

    // Yeni cihazları ekle
    for (const device of sampleDevices) {
      console.log(`Ekleniyor: ${device.name}`);
      await axios.post(`${API_URL}/devices`, device);
    }

    console.log('Tüm cihazlar başarıyla eklendi!');
  } catch (error) {
    console.error('Hata:', error);
    if (error.response) {
      console.error('Yanıt:', error.response.data);
    }
  }
}

// Cihazları ekle
addDevices();
