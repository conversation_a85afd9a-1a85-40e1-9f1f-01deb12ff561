/**
 * <PERSON><PERSON>ı<PERSON>ı durumu migration script'i
 * Eski sistem: locked, lockedUntil, manualLock
 * Yeni sistem: status (active/disabled), locked, lockedUntil
 */

const redisClient = require('../services/redis');

async function migrateUserStatus() {
  console.log('🔄 Kullanıcı durumu migration başlatılıyor...');

  try {
    // Tüm kullanıcıları al
    const userIds = await redisClient.smembers('users');
    console.log(`📊 ${userIds.length} kullanıcı bulundu`);

    let migratedCount = 0;
    let skippedCount = 0;

    for (const userId of userIds) {
      const user = await redisClient.hgetall(`user:${userId}`);

      if (!user || !user.username) {
        console.log(`⚠️  Geçersiz kullanıcı atlandı: ${userId}`);
        skippedCount++;
        continue;
      }

      console.log(`🔄 Migration: ${user.username} (${userId})`);

      // Yeni alan kont<PERSON> eski al<PERSON> temizle
      if (user.status && !user.manualLock) {
        console.log(`✅ ${user.username} zaten migrate edilmiş, atlanıyor`);
        skippedCount++;
        continue;
      }

      // Yeni sistem için değerler
      let newStatus = 'active'; // Varsayılan aktif
      let newLocked = false;
      let newLockedUntil = null;

      // Eski sistem analizi
      const oldLocked = user.locked === 'true' || user.locked === true;
      const oldManualLock = user.manualLock === 'true' || user.manualLock === true;
      const oldLockedUntil = user.lockedUntil ? parseInt(user.lockedUntil) : null;

      if (oldLocked && oldManualLock) {
        // Eski manuel kilitleme → Yeni devre dışı
        newStatus = 'disabled';
        console.log(`  📝 Manuel kilitleme → Devre dışı`);
      } else if (oldLocked && oldLockedUntil) {
        // Eski otomatik kilitleme → Yeni geçici kilitleme
        if (Date.now() < oldLockedUntil) {
          // Hala geçerli
          newLocked = true;
          newLockedUntil = oldLockedUntil;
          console.log(`  📝 Otomatik kilitleme → Geçici kilitleme (${new Date(oldLockedUntil).toLocaleString('tr-TR')} kadar)`);
        } else {
          // Süresi dolmuş
          console.log(`  📝 Süresi dolmuş kilitleme → Aktif`);
        }
      } else {
        console.log(`  📝 Normal kullanıcı → Aktif`);
      }

      // Yeni alanları ekle
      const updates = {
        status: newStatus,
        locked: newLocked.toString(),
        lockedUntil: newLockedUntil ? newLockedUntil.toString() : null
      };

      // Güncelleme yap
      for (const [field, value] of Object.entries(updates)) {
        if (value !== null) {
          await redisClient.hset(`user:${userId}`, field, value);
        }
      }

      // Eski alanları sil
      const fieldsToRemove = ['manualLock'];
      for (const field of fieldsToRemove) {
        if (user[field] !== undefined) {
          await redisClient.hdel(`user:${userId}`, field);
          console.log(`  🗑️  Eski alan silindi: ${field}`);
        }
      }

      // lockedUntil alanını temizle (eğer boşsa)
      if (user.lockedUntil === '' || user.lockedUntil === 'null') {
        await redisClient.hdel(`user:${userId}`, 'lockedUntil');
        console.log(`  🗑️  Boş lockedUntil silindi`);
      }

      console.log(`  ✅ ${user.username} başarıyla migrate edildi`);
      migratedCount++;
    }

    console.log('\n🎉 Migration tamamlandı!');
    console.log(`📊 Sonuçlar:`);
    console.log(`  ✅ Migrate edilen: ${migratedCount}`);
    console.log(`  ⏭️  Atlanan: ${skippedCount}`);
    console.log(`  📊 Toplam: ${userIds.length}`);

  } catch (error) {
    console.error('❌ Migration hatası:', error);
    throw error;
  }
}

// Script doğrudan çalıştırılırsa
if (require.main === module) {
  migrateUserStatus()
    .then(() => {
      console.log('✅ Migration başarıyla tamamlandı');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration başarısız:', error);
      process.exit(1);
    });
}

module.exports = { migrateUserStatus };
