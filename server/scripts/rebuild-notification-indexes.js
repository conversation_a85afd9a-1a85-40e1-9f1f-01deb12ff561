#!/usr/bin/env node

/**
 * Redis Bildirim İndekslerini Yeniden Oluşturma Script'i
 * 
 * Bu script mevcut bildirimlerin eksik Redis indekslerini oluşturur.
 * Optimize edilmiş bildirim sistemi için gerekli tüm indeksleri ekler.
 */

const Redis = require('ioredis');
const path = require('path');

// Redis bağlantısı
const redisClient = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
});

/**
 * JSON parse with fallback
 */
const safeJsonParse = (str, fallback = {}) => {
  try {
    return JSON.parse(str);
  } catch (e) {
    return fallback;
  }
};

/**
 * Ana rebuild fonksiyonu
 */
async function rebuildNotificationIndexes() {
  try {
    console.log('🚀 Redis bildirim indeksleri yeniden oluşturuluyor...');
    
    // Mevcut tüm bildirimleri al
    const allNotificationIds = await redisClient.zrange('notifications', 0, -1);
    
    if (!allNotificationIds || allNotificationIds.length === 0) {
      console.log('📭 Hiç bildirim bulunamadı.');
      return;
    }
    
    console.log(`📦 ${allNotificationIds.length} bildirim bulundu. İndeksler oluşturuluyor...`);
    
    // Mevcut indeksleri temizle (opsiyonel)
    console.log('🧹 Mevcut indeksler temizleniyor...');
    await clearExistingIndexes();
    
    // Her bildirim için indeksleri yeniden oluştur
    let processedCount = 0;
    const batchSize = 100;
    
    for (let i = 0; i < allNotificationIds.length; i += batchSize) {
      const batch = allNotificationIds.slice(i, i + batchSize);
      await processBatch(batch);
      processedCount += batch.length;
      
      console.log(`⚡ İşlenen: ${processedCount}/${allNotificationIds.length} (${Math.round(processedCount/allNotificationIds.length*100)}%)`);
    }
    
    console.log('✅ Tüm indeksler başarıyla yeniden oluşturuldu!');
    
    // İstatistikleri göster
    await showIndexStats();
    
  } catch (error) {
    console.error('❌ İndeks yeniden oluşturma hatası:', error);
    throw error;
  }
}

/**
 * Mevcut indeksleri temizle
 */
async function clearExistingIndexes() {
  const indexPatterns = [
    'notifications:type:*',
    'notifications:category:*',
    'notifications:severity:*',
    'notifications:status:*',
    'notifications:source:*'
  ];
  
  for (const pattern of indexPatterns) {
    const keys = await redisClient.keys(pattern);
    if (keys.length > 0) {
      await redisClient.del(...keys);
      console.log(`🗑️  ${keys.length} adet ${pattern} indeksi silindi`);
    }
  }
}

/**
 * Batch işleme
 */
async function processBatch(notificationIds) {
  const pipeline = redisClient.multi();
  
  for (const notificationId of notificationIds) {
    // Bildirim detaylarını al
    const notificationData = await redisClient.hgetall(`notification:${notificationId}`);
    
    if (!notificationData || !notificationData.timestamp) {
      continue;
    }
    
    // JSON alanlarını parse et
    const source = safeJsonParse(notificationData.source, {});
    const timestampMs = new Date(notificationData.timestamp).getTime();
    
    // 1. Ana bildirim listesi (zaten var)
    pipeline.zadd('notifications', timestampMs, notificationId);
    
    // 2. Tip indeksleri (device/system)
    if (notificationData.type) {
      pipeline.zadd(`notifications:type:${notificationData.type}`, timestampMs, notificationId);
    }
    
    // 3. Kategori indeksleri
    if (notificationData.category) {
      pipeline.zadd(`notifications:category:${notificationData.category}`, timestampMs, notificationId);
    }
    
    // 4. Önem derecesi indeksleri
    if (notificationData.severity) {
      pipeline.zadd(`notifications:severity:${notificationData.severity}`, timestampMs, notificationId);
    }
    
    // 5. Durum indeksleri
    if (notificationData.status) {
      pipeline.zadd(`notifications:status:${notificationData.status}`, timestampMs, notificationId);
    }
    
    // 6. Source indeksleri
    if (source && source.type) {
      if (source.id) {
        pipeline.zadd(`notifications:source:${source.type}:${source.id}`, timestampMs, notificationId);
      }
    }
    
    // 7. Kombinasyon indeksleri
    if (notificationData.type && notificationData.status) {
      pipeline.zadd(`notifications:type:${notificationData.type}:status:${notificationData.status}`, timestampMs, notificationId);
    }
    
    if (notificationData.type && notificationData.severity) {
      pipeline.zadd(`notifications:type:${notificationData.type}:severity:${notificationData.severity}`, timestampMs, notificationId);
    }
    
    if (notificationData.category && notificationData.status) {
      pipeline.zadd(`notifications:category:${notificationData.category}:status:${notificationData.status}`, timestampMs, notificationId);
    }
    
    if (notificationData.category && notificationData.severity) {
      pipeline.zadd(`notifications:category:${notificationData.category}:severity:${notificationData.severity}`, timestampMs, notificationId);
    }
  }
  
  // Batch'i çalıştır
  await pipeline.exec();
}

/**
 * İndeks istatistiklerini göster
 */
async function showIndexStats() {
  console.log('\n📊 İndeks İstatistikleri:');
  console.log('========================');
  
  // Ana indeksler
  const totalCount = await redisClient.zcard('notifications');
  console.log(`📋 Toplam bildirim: ${totalCount}`);
  
  // Tip indeksleri
  const deviceCount = await redisClient.zcard('notifications:type:device');
  const systemCount = await redisClient.zcard('notifications:type:system');
  console.log(`🔧 Device bildirimleri: ${deviceCount}`);
  console.log(`⚙️  System bildirimleri: ${systemCount}`);
  
  // Durum indeksleri
  const newCount = await redisClient.zcard('notifications:status:new');
  const readCount = await redisClient.zcard('notifications:status:read');
  const acknowledgedCount = await redisClient.zcard('notifications:status:acknowledged');
  const resolvedCount = await redisClient.zcard('notifications:status:resolved');
  
  console.log(`🆕 Yeni: ${newCount}`);
  console.log(`👁️  Okunmuş: ${readCount}`);
  console.log(`✅ Onaylanmış: ${acknowledgedCount}`);
  console.log(`🔒 Çözülmüş: ${resolvedCount}`);
  
  // Önem derecesi indeksleri
  const criticalCount = await redisClient.zcard('notifications:severity:critical');
  const warningCount = await redisClient.zcard('notifications:severity:warning');
  const infoCount = await redisClient.zcard('notifications:severity:info');
  
  console.log(`🔴 Kritik: ${criticalCount}`);
  console.log(`🟡 Uyarı: ${warningCount}`);
  console.log(`🔵 Bilgi: ${infoCount}`);
  
  console.log('========================\n');
}

/**
 * Script'i çalıştır
 */
async function main() {
  try {
    await rebuildNotificationIndexes();
    console.log('🎉 İşlem tamamlandı!');
    process.exit(0);
  } catch (error) {
    console.error('💥 Script hatası:', error);
    process.exit(1);
  } finally {
    redisClient.disconnect();
  }
}

// Script'i çalıştır
if (require.main === module) {
  main();
}

module.exports = { rebuildNotificationIndexes };
