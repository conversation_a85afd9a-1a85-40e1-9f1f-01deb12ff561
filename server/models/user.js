/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> modeli
 * Redis'te kullanıcı verilerini yönetmek için fonksiyonlar içerir
 */

const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');
const redisClient = require('../services/redis');
const settingsService = require('../services/settingsService');
const socketService = require('../services/socketService');

/**
 * Yeni bir kullanıcı oluşturur
 * @param {Object} userData - Kullanıcı verileri
 * @returns {Promise<Object>} - Oluşturulan kullanıcı
 */
async function createUser(userData) {
  // Kullanıcı adı veya e-posta zaten kullanılıyor mu kontrol et
  const existingUserByUsername = await getUserByUsername(userData.username);
  if (existingUserByUsername) {
    throw new Error('<PERSON><PERSON> kullanıcı adı zaten kullanılıyor');
  }

  const existingUserByEmail = await getUserByEmail(userData.email);
  if (existingUserByEmail) {
    throw new Error('Bu e-posta adresi zaten kullanılıyor');
  }

  // Kullanıcı ID'si oluştur
  const userId = uuidv4();

  // Şifreyi hash'le
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(userData.password, salt);

  // Kullanıcı nesnesini oluştur
  const user = {
    id: userId,
    username: userData.username,
    email: userData.email,
    password: hashedPassword,
    role: userData.role || 'user', // Varsayılan rol: user
    status: 'active', // Kullanıcı durumu: active, disabled
    locked: false, // Geçici kilitleme (yanlış giriş)
    lockedUntil: null, // Kilit bitiş zamanı
    passwordChangedAt: Date.now(), // Şifre değiştirme zamanı
    createdAt: Date.now(),
    updatedAt: Date.now()
  };

  // Redis'e kaydet
  await redisClient.hset(`user:${userId}`, user);

  // Kullanıcı adı ve e-posta için indeksler oluştur
  await redisClient.set(`username:${userData.username}`, userId);
  await redisClient.set(`email:${userData.email}`, userId);

  // Kullanıcılar listesine ekle
  await redisClient.sadd('users', userId);

  // Şifre olmadan kullanıcıyı döndür
  const { password, ...userWithoutPassword } = user;

  // Socket ile kullanıcı oluşturma event'i yayınla
  try {
    socketService.emitToAll('user:created', {
      action: 'create',
      user: userWithoutPassword,
      timestamp: Date.now()
    });
  } catch (socketError) {
    console.error('Socket event gönderme hatası:', socketError);
  }

  return userWithoutPassword;
}

/**
 * Kullanıcı ID'sine göre kullanıcıyı getirir
 * @param {string} userId - Kullanıcı ID'si
 * @param {boolean} includePassword - Şifreyi dahil et
 * @returns {Promise<Object|null>} - Kullanıcı nesnesi veya null
 */
async function getUserById(userId, includePassword = false) {
  const user = await redisClient.hgetall(`user:${userId}`);

  if (!user || Object.keys(user).length === 0) {
    return null;
  }

  if (!includePassword) {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  return user;
}

/**
 * Kullanıcı adına göre kullanıcıyı getirir
 * @param {string} username - Kullanıcı adı
 * @param {boolean} includePassword - Şifreyi dahil et
 * @returns {Promise<Object|null>} - Kullanıcı nesnesi veya null
 */
async function getUserByUsername(username, includePassword = false) {
  const userId = await redisClient.get(`username:${username}`);

  if (!userId) {
    return null;
  }

  return getUserById(userId, includePassword);
}

/**
 * E-posta adresine göre kullanıcıyı getirir
 * @param {string} email - E-posta adresi
 * @param {boolean} includePassword - Şifreyi dahil et
 * @returns {Promise<Object|null>} - Kullanıcı nesnesi veya null
 */
async function getUserByEmail(email, includePassword = false) {
  const userId = await redisClient.get(`email:${email}`);

  if (!userId) {
    return null;
  }

  return getUserById(userId, includePassword);
}

/**
 * Tüm kullanıcıları getirir
 * @returns {Promise<Array>} - Kullanıcılar dizisi
 */
async function getAllUsers() {
  const userIds = await redisClient.smembers('users');
  const users = [];

  for (const userId of userIds) {
    const user = await getUserById(userId);
    if (user) {
      // Kullanıcı durumunu kontrol et
      const userStatus = await getUserStatus(userId);
      users.push({
        ...user,
        status: userStatus.status,
        locked: userStatus.locked,
        lockedUntil: userStatus.lockedUntil
      });
    }
  }

  return users;
}

/**
 * Kullanıcıyı günceller
 * @param {string} userId - Kullanıcı ID'si
 * @param {Object} userData - Güncellenecek kullanıcı verileri
 * @returns {Promise<Object>} - Güncellenmiş kullanıcı
 */
async function updateUser(userId, userData) {
  const user = await getUserById(userId, true);

  if (!user) {
    throw new Error('Kullanıcı bulunamadı');
  }

  // Kullanıcı adı değiştiriliyorsa, benzersiz olduğunu kontrol et
  if (userData.username && userData.username !== user.username) {
    const existingUser = await getUserByUsername(userData.username);
    if (existingUser) {
      throw new Error('Bu kullanıcı adı zaten kullanılıyor');
    }

    // Eski kullanıcı adı indeksini sil
    await redisClient.del(`username:${user.username}`);

    // Yeni kullanıcı adı indeksini oluştur
    await redisClient.set(`username:${userData.username}`, userId);
  }

  // E-posta değiştiriliyorsa, benzersiz olduğunu kontrol et
  if (userData.email && userData.email !== user.email) {
    const existingUser = await getUserByEmail(userData.email);
    if (existingUser) {
      throw new Error('Bu e-posta adresi zaten kullanılıyor');
    }

    // Eski e-posta indeksini sil
    await redisClient.del(`email:${user.email}`);

    // Yeni e-posta indeksini oluştur
    await redisClient.set(`email:${userData.email}`, userId);
  }

  // Şifre değiştiriliyorsa hash'le
  if (userData.password) {
    const salt = await bcrypt.genSalt(10);
    userData.password = await bcrypt.hash(userData.password, salt);
  }

  // Kullanıcı nesnesini güncelle
  const updatedUser = {
    ...user,
    ...userData,
    updatedAt: Date.now()
  };

  // Redis'e kaydet
  await redisClient.hset(`user:${userId}`, updatedUser);

  // Şifre olmadan kullanıcıyı döndür
  const { password, ...userWithoutPassword } = updatedUser;

  // Socket ile kullanıcı güncelleme event'i yayınla
  try {
    socketService.emitToAll('user:updated', {
      action: 'update',
      user: userWithoutPassword,
      timestamp: Date.now()
    });
  } catch (socketError) {
    console.error('Socket event gönderme hatası:', socketError);
  }

  return userWithoutPassword;
}

/**
 * Kullanıcıyı siler
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise<boolean>} - Başarılı ise true
 */
async function deleteUser(userId) {
  const user = await getUserById(userId);

  if (!user) {
    throw new Error('Kullanıcı bulunamadı');
  }

  // Kullanıcı adı ve e-posta indekslerini sil
  await redisClient.del(`username:${user.username}`);
  await redisClient.del(`email:${user.email}`);

  // Kullanıcı verisini sil
  await redisClient.del(`user:${userId}`);

  // Kullanıcılar listesinden çıkar
  await redisClient.srem('users', userId);

  // Socket ile kullanıcı silme event'i yayınla
  try {
    socketService.emitToAll('user:deleted', {
      action: 'delete',
      userId: userId,
      username: user.username,
      timestamp: Date.now()
    });
  } catch (socketError) {
    console.error('Socket event gönderme hatası:', socketError);
  }

  return true;
}

/**
 * Kullanıcının şifresini doğrular
 * @param {string} userId - Kullanıcı ID'si
 * @param {string} password - Doğrulanacak şifre
 * @returns {Promise<boolean>} - Şifre doğru ise true
 */
async function verifyPassword(userId, password) {
  const user = await getUserById(userId, true);

  if (!user) {
    return false;
  }

  return bcrypt.compare(password, user.password);
}

/**
 * Kullanıcının şifresini değiştirir
 * @param {string} userId - Kullanıcı ID'si
 * @param {string} currentPassword - Mevcut şifre
 * @param {string} newPassword - Yeni şifre
 * @returns {Promise<boolean>} - Başarılı ise true
 */
async function changePassword(userId, currentPassword, newPassword) {
  const isValid = await verifyPassword(userId, currentPassword);

  if (!isValid) {
    throw new Error('Mevcut şifre yanlış');
  }

  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(newPassword, salt);

  const now = Date.now();
  await redisClient.hset(`user:${userId}`, 'password', hashedPassword);
  await redisClient.hset(`user:${userId}`, 'passwordChangedAt', now);
  await redisClient.hset(`user:${userId}`, 'updatedAt', now);

  return true;
}

/**
 * Kullanıcının şifresini sıfırlar (mevcut şifre kontrolü olmadan)
 * @param {string} userId - Kullanıcı ID'si
 * @param {string} newPassword - Yeni şifre
 * @returns {Promise<boolean>} - Başarılı ise true
 */
async function resetPassword(userId, newPassword) {
  const user = await getUserById(userId);

  if (!user) {
    throw new Error('Kullanıcı bulunamadı');
  }

  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(newPassword, salt);

  const now = Date.now();
  await redisClient.hset(`user:${userId}`, 'password', hashedPassword);
  await redisClient.hset(`user:${userId}`, 'passwordChangedAt', now);
  await redisClient.hset(`user:${userId}`, 'updatedAt', now);

  return true;
}

/**
 * Kullanıcının durumunu kontrol eder
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise<Object>} - { status: string, locked: boolean, lockedUntil: number|null }
 */
async function getUserStatus(userId) {
  const user = await getUserById(userId);

  if (!user) {
    return { status: 'active', locked: false, lockedUntil: null };
  }

  // Kullanıcı durumu kontrolü
  const status = user.status || 'active';

  // Geçici kilitleme kontrolü (yanlış giriş)
  let locked = false;
  let lockedUntil = null;

  // Önce loginAttempts sistemindeki kilidi kontrol et
  const loginLockKey = `login:locked:${user.username}`;
  const loginLockedUntil = await redisClient.get(loginLockKey);

  if (loginLockedUntil) {
    const lockTime = parseInt(loginLockedUntil);
    if (Date.now() < lockTime) {
      // Login sistemi tarafından kilitli
      locked = true;
      lockedUntil = lockTime;
    } else {
      // Kilit süresi dolmuş, temizle
      await redisClient.del(loginLockKey);
    }
  }

  // Kullanıcı modelindeki kilit durumunu kontrol et
  if (!locked && (user.locked === 'true' || user.locked === true)) {
    if (user.lockedUntil && Date.now() > parseInt(user.lockedUntil)) {
      // Kilit süresi dolmuş, kilidi kaldır
      await updateUser(userId, { locked: false, lockedUntil: null });
    } else {
      // Hala kilitli
      locked = true;
      lockedUntil = user.lockedUntil ? parseInt(user.lockedUntil) : null;
    }
  }

  return {
    status,
    locked,
    lockedUntil
  };
}

/**
 * Kullanıcının kilidini kaldırır (sadece geçici kilitleme)
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise<boolean>} - Başarılı ise true
 */
async function unlockUser(userId) {
  const user = await getUserById(userId);

  if (!user) {
    throw new Error('Kullanıcı bulunamadı');
  }

  // LoginAttempts sistemindeki kilidi kaldır
  const loginLockKey = `login:locked:${user.username}`;
  await redisClient.del(loginLockKey);

  // Başarısız giriş denemelerini sıfırla
  const failedAttemptsKey = `login:failed:${user.username}`;
  await redisClient.del(failedAttemptsKey);

  // Kullanıcı modelindeki kilidi kaldır
  await updateUser(userId, { locked: false, lockedUntil: null });

  // Socket ile kullanıcı durum değişikliğini yayınla
  try {
    const updatedUser = await getUserById(userId);
    const userStatus = await getUserStatus(userId);
    socketService.emitToAll('user:status:update', {
      userId: userId,
      username: user.username,
      status: userStatus.status,
      locked: userStatus.locked,
      lockedUntil: userStatus.lockedUntil,
      action: 'unlock',
      timestamp: Date.now(),
      user: updatedUser
    });
  } catch (socketError) {
    console.error('Socket event gönderme hatası:', socketError);
  }

  return true;
}

/**
 * Kullanıcıyı devre dışı bırakır
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise<boolean>} - Başarılı ise true
 */
async function disableUser(userId) {
  const user = await getUserById(userId);

  if (!user) {
    throw new Error('Kullanıcı bulunamadı');
  }

  // Kullanıcı durumunu devre dışı yap
  await updateUser(userId, { status: 'disabled' });

  // Socket ile kullanıcı durum değişikliğini yayınla
  try {
    const updatedUser = await getUserById(userId);
    const userStatus = await getUserStatus(userId);
    socketService.emitToAll('user:status:update', {
      userId: userId,
      username: user.username,
      status: userStatus.status,
      locked: userStatus.locked,
      lockedUntil: userStatus.lockedUntil,
      action: 'disable',
      timestamp: Date.now(),
      user: updatedUser
    });
  } catch (socketError) {
    console.error('Socket event gönderme hatası:', socketError);
  }

  return true;
}

/**
 * Kullanıcıyı aktif eder
 * @param {string} userId - Kullanıcı ID'si
 * @returns {Promise<boolean>} - Başarılı ise true
 */
async function enableUser(userId) {
  const user = await getUserById(userId);

  if (!user) {
    throw new Error('Kullanıcı bulunamadı');
  }

  // Kullanıcı durumunu aktif yap
  await updateUser(userId, { status: 'active' });

  // Socket ile kullanıcı durum değişikliğini yayınla
  try {
    const updatedUser = await getUserById(userId);
    const userStatus = await getUserStatus(userId);
    socketService.emitToAll('user:status:update', {
      userId: userId,
      username: user.username,
      status: userStatus.status,
      locked: userStatus.locked,
      lockedUntil: userStatus.lockedUntil,
      action: 'enable',
      timestamp: Date.now(),
      user: updatedUser
    });
  } catch (socketError) {
    console.error('Socket event gönderme hatası:', socketError);
  }

  return true;
}

module.exports = {
  createUser,
  getUserById,
  getUserByUsername,
  getUserByEmail,
  getAllUsers,
  updateUser,
  deleteUser,
  verifyPassword,
  changePassword,
  resetPassword,
  getUserStatus,
  unlockUser,
  disableUser,
  enableUser
};
