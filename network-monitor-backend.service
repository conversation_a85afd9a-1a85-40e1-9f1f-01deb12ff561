[Unit]
Description=Network Monitor Backend Service
After=network.target redis.service
Requires=redis.service

[Service]
User=monitoruser
Group=monitoruser
WorkingDirectory=/home/<USER>/Documents/backup_network_monitor/network_monitor
Environment=HOME=/home/<USER>
Environment=NODE_ENV=production
Environment=PORT=5000
ExecStart=/usr/bin/node server/app.js
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=network-monitor-backend
AmbientCapabilities=CAP_NET_RAW
CapabilityBoundingSet=CAP_NET_RAW

[Install]
WantedBy=multi-user.target
