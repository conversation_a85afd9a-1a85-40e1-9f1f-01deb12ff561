{"name": "netwatch", "version": "1.0.0", "description": "NetWatch - Agent gerektirmeyen ağ izleme yazılımı", "main": "server/app.js", "scripts": {"start": "node server/app.js", "server": "nodemon server/app.js", "client": "cd client && NODE_OPTIONS=--no-warnings npm start", "dev": "concurrently --kill-others-on-fail \"npm run server\" \"npm run client\"", "dev:server": "nodemon server/app.js", "dev:client": "cd client && NODE_OPTIONS=--no-warnings npm start", "install-all": "npm install && cd client && npm install"}, "keywords": ["network", "monitoring", "icmp", "http", "tcp", "dns", "ssl"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dns-packet": "^5.6.1", "dotenv": "^16.5.0", "express": "^4.18.2", "ioredis": "^4.30.0", "node-cron": "^3.0.3", "socket.io": "^4.7.2", "ssl-checker": "^2.0.10"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.1.9"}}