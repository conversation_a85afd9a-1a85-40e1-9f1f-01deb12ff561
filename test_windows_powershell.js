const { spawn } = require('child_process');

// Windows sistem bilgilerini PowerShell Remoting ile çekmek
function getWindowsSystemInfoPowerShell() {
  return new Promise((resolve, reject) => {
    const psScript = `
      try {
        Write-Host "PowerShell Remoting test başlıyor..."
        
        $cred = New-Object System.Management.Automation.PSCredential('sifirbiriki', (ConvertTo-SecureString '30935619' -AsPlainText -Force))
        Write-Host "Kimlik bilgileri hazırlandı"
        
        $result = Invoke-Command -ComputerName ************** -Credential $cred -ScriptBlock {
          Write-Host "Windows sistemine bağlanıldı"
          
          # CPU bilgileri
          $cpu = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
          
          # Memory bilgileri  
          $memory = Get-WmiObject -Class Win32_OperatingSystem
          
          # Disk bilgileri
          $disks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
          
          # Sistem bilgilerini hazırla
          $systemInfo = @{
            cpu = @{
              name = $cpu.Name.Trim()
              manufacturer = $cpu.Manufacturer.Trim()
              cores = $cpu.NumberOfCores
              speed = $cpu.MaxClockSpeed
            }
            memory = @{
              total = [long]$memory.TotalVisibleMemorySize * 1024
              free = [long]$memory.FreePhysicalMemory * 1024
              used = ([long]$memory.TotalVisibleMemorySize - [long]$memory.FreePhysicalMemory) * 1024
              usage = [math]::Round((([long]$memory.TotalVisibleMemorySize - [long]$memory.FreePhysicalMemory) / [long]$memory.TotalVisibleMemorySize) * 100, 2)
            }
            os = @{
              name = $memory.Caption.Trim()
              version = $memory.Version
              architecture = $memory.OSArchitecture
            }
            disk = @($disks | ForEach-Object {
              @{
                device = $_.DeviceID
                size = [long]$_.Size
                used = [long]($_.Size - $_.FreeSpace)
                free = [long]$_.FreeSpace
                usage = if ($_.Size -gt 0) { [math]::Round((($_.Size - $_.FreeSpace) / $_.Size) * 100, 2) } else { 0 }
              }
            })
            timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
          }
          
          # JSON olarak döndür
          $systemInfo | ConvertTo-Json -Depth 10
        }
        
        Write-Host "Sistem bilgileri alındı"
        Write-Output $result
        
      } catch {
        Write-Error "PowerShell Remoting Hatası: $($_.Exception.Message)"
        exit 1
      }
    `;

    console.log('PowerShell Remoting ile Windows sistem bilgileri alınıyor...');
    
    const ps = spawn('pwsh', ['-Command', psScript], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    ps.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    ps.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    ps.on('close', (code) => {
      if (code === 0) {
        try {
          // JSON çıktısını bul ve parse et
          const lines = stdout.split('\n');
          const jsonStart = lines.findIndex(line => line.trim().startsWith('{'));
          
          if (jsonStart !== -1) {
            const jsonLines = lines.slice(jsonStart);
            const jsonEnd = jsonLines.findIndex((line, index) => {
              if (index === 0) return false;
              return line.trim() === '}' && !line.includes('"');
            });
            
            const jsonText = jsonEnd !== -1 ? 
              jsonLines.slice(0, jsonEnd + 1).join('\n') : 
              jsonLines.join('\n');
            
            const systemData = JSON.parse(jsonText);
            resolve(systemData);
          } else {
            reject(new Error('JSON çıktısı bulunamadı: ' + stdout));
          }
        } catch (parseError) {
          reject(new Error(`JSON parse error: ${parseError.message}, Output: ${stdout}`));
        }
      } else {
        reject(new Error(`PowerShell error (${code}): ${stderr || stdout}`));
      }
    });

    ps.on('error', (error) => {
      reject(new Error(`PowerShell spawn error: ${error.message}`));
    });

    // 60 saniye timeout
    setTimeout(() => {
      ps.kill('SIGTERM');
      reject(new Error('PowerShell Remoting timeout'));
    }, 60000);
  });
}

// Test fonksiyonu
async function testWindowsPowerShell() {
  try {
    console.log('=== Windows PowerShell Remoting Test ===');
    console.log('Hedef: **************');
    console.log('Kullanıcı: sifirbiriki');
    console.log('Protokol: PowerShell Remoting (WinRM)');
    console.log('');
    
    const systemInfo = await getWindowsSystemInfoPowerShell();
    
    console.log('✅ PowerShell Remoting başarılı!');
    console.log('');
    console.log('📊 Sistem Bilgileri:');
    
    if (systemInfo.cpu) {
      console.log('CPU:', {
        name: systemInfo.cpu.name,
        manufacturer: systemInfo.cpu.manufacturer,
        cores: systemInfo.cpu.cores,
        speed: systemInfo.cpu.speed + ' MHz'
      });
    }
    
    if (systemInfo.memory) {
      console.log('');
      console.log('💾 Memory:', {
        total: Math.round(systemInfo.memory.total / 1024 / 1024 / 1024) + ' GB',
        used: Math.round(systemInfo.memory.used / 1024 / 1024 / 1024) + ' GB',
        free: Math.round(systemInfo.memory.free / 1024 / 1024 / 1024) + ' GB',
        usage: systemInfo.memory.usage + '%'
      });
    }
    
    if (systemInfo.disk && systemInfo.disk.length > 0) {
      console.log('');
      console.log('💿 Diskler:');
      systemInfo.disk.forEach(disk => {
        console.log(`  ${disk.device}`, {
          total: Math.round(disk.size / 1024 / 1024 / 1024) + ' GB',
          used: Math.round(disk.used / 1024 / 1024 / 1024) + ' GB',
          free: Math.round(disk.free / 1024 / 1024 / 1024) + ' GB',
          usage: disk.usage + '%'
        });
      });
    }
    
    if (systemInfo.os) {
      console.log('');
      console.log('🖥️ İşletim Sistemi:', {
        name: systemInfo.os.name,
        version: systemInfo.os.version,
        architecture: systemInfo.os.architecture
      });
    }
    
    console.log('');
    console.log('⏰ Zaman:', systemInfo.timestamp);
    
  } catch (error) {
    console.error('❌ Hata:', error.message);
  }
}

// Test başlat
testWindowsPowerShell();
