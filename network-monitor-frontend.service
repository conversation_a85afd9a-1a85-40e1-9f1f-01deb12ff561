[Unit]
Description=Network Monitor Frontend Service
After=network.target network-monitor-backend.service
Requires=network-monitor-backend.service

[Service]
User=monitoruser
Group=monitoruser
WorkingDirectory=/home/<USER>/Documents/backup_network_monitor/network_monitor/client
Environment=HOME=/home/<USER>
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=BROWSER=none
Environment=NODE_OPTIONS=--no-warnings --max-old-space-size=1024
ExecStart=/usr/bin/node /usr/bin/npx react-scripts start
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=network-monitor-frontend

[Install]
WantedBy=multi-user.target
