const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const redis = require('redis');

async function createAdmin() {
  const client = redis.createClient();
  await client.connect();

  const adminData = {
    id: uuidv4(),
    username: 'admin',
    email: '<EMAIL>',
    password: 'NetworkAdmin2024!',
    role: 'admin'
  };

  // Şifreyi hash'le
  const hashedPassword = await bcrypt.hash(adminData.password, 10);

  // Redis'e kaydet
  await client.hSet(`user:${adminData.id}`, {
    id: adminData.id,
    username: adminData.username,
    email: adminData.email,
    password: hashedPassword,
    role: adminData.role,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });

  // İndeksleri oluştur
  await client.set(`username:${adminData.username}`, adminData.id);
  await client.set(`email:${adminData.email}`, adminData.id);
  await client.sAdd('users', adminData.id);

  console.log('✅ Admin kullanıcısı oluşturuldu!');
  console.log('📧 Email:', adminData.email);
  console.log('👤 Kullanıcı Adı:', adminData.username);
  console.log('🔑 Şifre:', adminData.password);
  console.log('🆔 ID:', adminData.id);

  await client.quit();
}

createAdmin().catch(console.error);
