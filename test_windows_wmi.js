const { spawn } = require('child_process');

// Windows WMI ile sistem bilgilerini çekmek için Python script
function getWindowsSystemInfoWMI() {
  return new Promise((resolve, reject) => {
    const pythonScript = `
import sys
import json
from impacket.dcerpc.v5.dcom import wmi
from impacket.dcerpc.v5.dcomrt import DCOMConnection

def get_windows_system_info():
    try:
        # WMI bağlantısı
        dcom = DCOMConnection('**************', 'sifirbiriki', '30935619')
        iInterface = dcom.CoCreateInstanceEx(wmi.CLSID_WbemLevel1Login, wmi.IID_IWbemLevel1Login)
        iWbemLevel1Login = wmi.IWbemLevel1Login(iInterface)
        
        # WMI namespace'e bağlan
        iWbemServices = iWbemLevel1Login.NTLMLogin('//./root/cimv2', None, None)
        iWbemLevel1Login.RemRelease()
        
        # CPU bilgileri
        cpu_query = "SELECT Name, Manufacturer, NumberOfCores, NumberOfLogicalProcessors, MaxClockSpeed FROM Win32_Processor"
        iEnumWbemClassObject = iWbemServices.ExecQuery(cpu_query)
        
        cpu_info = {}
        while True:
            try:
                pclsObj = iEnumWbemClassObject.Next(0xffffffff, 1)[0]
                record = dict(pclsObj.getProperties())
                cpu_info = {
                    'name': str(record['Name']['value']).strip(),
                    'manufacturer': str(record['Manufacturer']['value']).strip(),
                    'cores': int(record['NumberOfCores']['value']),
                    'logicalProcessors': int(record['NumberOfLogicalProcessors']['value']),
                    'maxSpeed': int(record['MaxClockSpeed']['value'])
                }
                break
            except Exception:
                break
        
        # Memory bilgileri
        mem_query = "SELECT TotalVisibleMemorySize, FreePhysicalMemory FROM Win32_OperatingSystem"
        iEnumWbemClassObject = iWbemServices.ExecQuery(mem_query)
        
        memory_info = {}
        while True:
            try:
                pclsObj = iEnumWbemClassObject.Next(0xffffffff, 1)[0]
                record = dict(pclsObj.getProperties())
                total_kb = int(record['TotalVisibleMemorySize']['value'])
                free_kb = int(record['FreePhysicalMemory']['value'])
                used_kb = total_kb - free_kb
                
                memory_info = {
                    'total': total_kb * 1024,
                    'free': free_kb * 1024,
                    'used': used_kb * 1024,
                    'usage': round((used_kb / total_kb) * 100, 2)
                }
                break
            except Exception:
                break
        
        # Disk bilgileri
        disk_query = "SELECT DeviceID, Size, FreeSpace FROM Win32_LogicalDisk WHERE DriveType = 3"
        iEnumWbemClassObject = iWbemServices.ExecQuery(disk_query)
        
        disk_info = []
        while True:
            try:
                pclsObj = iEnumWbemClassObject.Next(0xffffffff, 1)[0]
                record = dict(pclsObj.getProperties())
                size = int(record['Size']['value'])
                free = int(record['FreeSpace']['value'])
                used = size - free
                
                disk_info.append({
                    'device': str(record['DeviceID']['value']),
                    'size': size,
                    'used': used,
                    'free': free,
                    'usage': round((used / size) * 100, 2) if size > 0 else 0
                })
            except Exception:
                break
        
        # İşletim sistemi bilgileri
        os_query = "SELECT Caption, Version, OSArchitecture, LastBootUpTime FROM Win32_OperatingSystem"
        iEnumWbemClassObject = iWbemServices.ExecQuery(os_query)
        
        os_info = {}
        while True:
            try:
                pclsObj = iEnumWbemClassObject.Next(0xffffffff, 1)[0]
                record = dict(pclsObj.getProperties())
                os_info = {
                    'name': str(record['Caption']['value']).strip(),
                    'version': str(record['Version']['value']),
                    'architecture': str(record['OSArchitecture']['value']),
                    'lastBoot': str(record['LastBootUpTime']['value'])
                }
                break
            except Exception:
                break
        
        # Sonuçları birleştir
        system_data = {
            'cpu': cpu_info,
            'memory': memory_info,
            'disk': disk_info,
            'os': os_info,
            'timestamp': '$(date "+%Y-%m-%d %H:%M:%S")'
        }
        
        print(json.dumps(system_data, indent=2))
        
        # Bağlantıyı kapat
        dcom.disconnect()
        
    except Exception as e:
        error_data = {
            'error': str(e),
            'timestamp': '$(date "+%Y-%m-%d %H:%M:%S")'
        }
        print(json.dumps(error_data, indent=2))
        sys.exit(1)

if __name__ == "__main__":
    get_windows_system_info()
`;

    console.log('WMI ile Windows sistem bilgileri alınıyor...');
    
    const python = spawn('python3', ['-c', pythonScript], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    python.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    python.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    python.on('close', (code) => {
      if (code === 0) {
        try {
          const systemData = JSON.parse(stdout);
          resolve(systemData);
        } catch (parseError) {
          reject(new Error(`JSON parse error: ${parseError.message}, Output: ${stdout}`));
        }
      } else {
        reject(new Error(`Python WMI error (${code}): ${stderr || stdout}`));
      }
    });

    python.on('error', (error) => {
      reject(new Error(`Python spawn error: ${error.message}`));
    });

    // 30 saniye timeout
    setTimeout(() => {
      python.kill('SIGTERM');
      reject(new Error('WMI timeout'));
    }, 30000);
  });
}

// Test fonksiyonu
async function testWindowsWMI() {
  try {
    console.log('=== Windows WMI Test ===');
    console.log('Hedef: **************');
    console.log('Kullanıcı: sifirbiriki');
    console.log('Protokol: WMI (Impacket)');
    console.log('');
    
    const systemInfo = await getWindowsSystemInfoWMI();
    
    if (systemInfo.error) {
      console.log('❌ WMI Hatası:', systemInfo.error);
      return;
    }
    
    console.log('✅ WMI bağlantısı başarılı!');
    console.log('');
    console.log('📊 Sistem Bilgileri:');
    
    if (systemInfo.cpu) {
      console.log('CPU:', {
        name: systemInfo.cpu.name,
        manufacturer: systemInfo.cpu.manufacturer,
        cores: systemInfo.cpu.cores,
        logicalProcessors: systemInfo.cpu.logicalProcessors,
        maxSpeed: systemInfo.cpu.maxSpeed + ' MHz'
      });
    }
    
    if (systemInfo.memory) {
      console.log('');
      console.log('💾 Memory:', {
        total: Math.round(systemInfo.memory.total / 1024 / 1024 / 1024) + ' GB',
        used: Math.round(systemInfo.memory.used / 1024 / 1024 / 1024) + ' GB',
        free: Math.round(systemInfo.memory.free / 1024 / 1024 / 1024) + ' GB',
        usage: systemInfo.memory.usage + '%'
      });
    }
    
    if (systemInfo.disk && systemInfo.disk.length > 0) {
      console.log('');
      console.log('💿 Diskler:');
      systemInfo.disk.forEach(disk => {
        console.log(`  ${disk.device}`, {
          total: Math.round(disk.size / 1024 / 1024 / 1024) + ' GB',
          used: Math.round(disk.used / 1024 / 1024 / 1024) + ' GB',
          free: Math.round(disk.free / 1024 / 1024 / 1024) + ' GB',
          usage: disk.usage + '%'
        });
      });
    }
    
    if (systemInfo.os) {
      console.log('');
      console.log('🖥️ İşletim Sistemi:', {
        name: systemInfo.os.name,
        version: systemInfo.os.version,
        architecture: systemInfo.os.architecture,
        lastBoot: systemInfo.os.lastBoot
      });
    }
    
    console.log('');
    console.log('⏰ Zaman:', systemInfo.timestamp);
    
  } catch (error) {
    console.error('❌ Hata:', error.message);
  }
}

// Test başlat
testWindowsWMI();
